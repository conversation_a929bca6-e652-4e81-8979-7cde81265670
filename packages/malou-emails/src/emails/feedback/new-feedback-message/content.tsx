import { Container, Text } from '@react-email/components';
import { DateTime } from 'luxon';

import { NewFeedbackMessageEmailProps } from '@malou-io/package-dto';

import { Goodbye, Hello, PrimaryButton, SocialPostPreview } from ':shared/components';
import { Translation } from ':shared/services';

export const Content = (props: NewFeedbackMessageEmailProps) => {
    const {
        locale,
        receiver,
        postLink,
        username,
        feedbackText,
        postStatus,
        plannedPublicationDate,
        fullPlatformKeys,
        restaurantName,
        postText,
        postImgUrl,
        isStory,
    } = props;
    const translator = new Translation(locale).getTranslator();
    const dateTime = plannedPublicationDate ? DateTime.fromJSDate(plannedPublicationDate) : undefined;
    const date = dateTime?.toLocaleString(DateTime.DATE_HUGE, { locale });
    const time = dateTime?.toLocaleString(DateTime.TIME_SIMPLE, { locale });
    return (
        <Container>
            <Hello locale={locale} receiver={receiver} />
            <Text>
                {translator.feedback.user_commented({
                    name: username,
                    date: date ?? '',
                    time: time ?? '',
                    postStatus,
                    fullPlatformKeys,
                    restaurantName,
                    object: isStory ? translator.feedback.story() : translator.feedback.post(),
                })}
            </Text>

            <Container className={'border border-solid border-[#F2F2FF] rounded mb-4'}>
                <Text className="w-full text-center">
                    <strong>"{feedbackText}"</strong>
                </Text>
            </Container>

            <SocialPostPreview postImgUrl={postImgUrl} postText={postText}></SocialPostPreview>

            <PrimaryButton link={postLink} text={translator.feedback.check_feedback()}></PrimaryButton>
            <Goodbye locale={locale} />
        </Container>
    );
};
export default Content;
