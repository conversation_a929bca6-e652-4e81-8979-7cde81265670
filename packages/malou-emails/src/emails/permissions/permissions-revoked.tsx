import { EmailPadding } from '@malou-io/package-utils';

import { MalouLayout } from ':shared/components';
import { MalouBasicHeader } from ':shared/components/layout/header';
import { Translation } from ':shared/services';
import { EmailMargin } from ':shared/types/report.enum';

import { Content, Props } from './content';

export const PermissionsRevokedMailTemplate = (props: Props) => {
    const { locale } = props;
    const translator = new Translation(locale).getTranslator();
    return (
        <MalouLayout
            context={{ ...props }}
            customHeader={
                <MalouBasicHeader
                    locale={locale}
                    dateTimeFormat="dd MMM yyyy"
                    notificationTitle={translator.notifications.common.notification()}
                    trackingUrl={props.trackingUrl}
                    xcss="text-malou-color-state-success rounded bg-malou-color-state-success/[.15] font-medium"
                />
            }
            emailMarginY={EmailMargin.LOW}
            paddingX={EmailPadding.LOW}>
            <Content {...props} />
        </MalouLayout>
    );
};

PermissionsRevokedMailTemplate.defaultProps = {
    locale: 'fr',
    link: 'https://malou.io',
    disconnectPlatformLink: 'https://malou.io',
    receiver: 'User',
    platformName: 'Deliveroo',
    restaurantName: 'Bon gueuleton',
    restaurantNames: ['Bon gueuleton', 'Bon gueuleton 2'],
};

export default PermissionsRevokedMailTemplate;
