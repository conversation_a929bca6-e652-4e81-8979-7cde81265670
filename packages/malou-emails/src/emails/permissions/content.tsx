import { Container, Text } from '@react-email/components';

import { BaseEmailProps } from '@malou-io/package-dto';

import { Hello, PrimaryButton, ThankYou } from ':shared/components';
import { Translation } from ':shared/services';

export interface Props extends BaseEmailProps {
    link: string;
    disconnectPlatformLink: string;
    restaurantName: string;
    platformName: string;
    restaurantNames: string[];
}

export const Content = ({ locale, link, disconnectPlatformLink, receiver, restaurantName, platformName, restaurantNames }: Props) => {
    const translator = new Translation(locale).getTranslator();
    return (
        <Container className="px-8 mb-8">
            <Hello locale={locale} receiver={receiver} />
            <Text className="font-semibold">
                {restaurantNames.length > 1
                    ? translator.permissions.revoked_connection.title_multiple_restaurants({
                          restaurantNames: restaurantNames.join(', '),
                          platformName,
                      })
                    : translator.permissions.revoked_connection.title({
                          restaurantName,
                          platformName,
                      })}
            </Text>
            <Text>{translator.permissions.revoked_connection.content()}</Text>
            <PrimaryButton link={link} text={translator.permissions.revoked_connection.cta()}></PrimaryButton>
            <PrimaryButton
                css="mt-2 cursor-pointer bg-malou-color-text-2"
                link={disconnectPlatformLink}
                text={translator.permissions.revoked_connection.disconnect_platform_cta()}></PrimaryButton>
            <ThankYou locale={locale} />
        </Container>
    );
};

export default Content;
