{"dependencies": {"@malou-io/package-dto": "workspace:*", "@malou-io/package-utils": "workspace:*", "@react-email/components": "0.0.25", "@react-email/markdown": "0.0.8", "luxon": "^3.4.4", "react": "^18.2.0", "tslib": "^2.8.1"}, "devDependencies": {"@malou-io/package-config": "workspace:*", "@sentry/cli": "^2.32.1", "@trivago/prettier-plugin-sort-imports": "^5.2.0", "@types/eslint": "^8.48.0", "@types/luxon": "^3.4.2", "@types/node": "^16.3.1", "@types/react": "^18.2.73", "@typescript-eslint/eslint-plugin": "^7.0.0", "@typescript-eslint/parser": "^7.0.0", "concurrently": "^8.2.2", "eslint": "^8.48.0", "eslint-config-airbnb-base": "^15.0.0", "eslint-config-airbnb-typescript": "^18.0.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-import": "^2.28.1", "prettier": "^3.5.3", "react-email": "^2.0.0", "typesafe-i18n": "^5.26.2", "typescript": "^5.3.3"}, "main": "./lib/index.js", "name": "@malou-io/package-emails", "private": true, "scripts": {"build": "tsc && tsc-alias", "build-clean": "rm -rf ./lib && rm -rf .turbo && rm -f tsconfig.tsbuildinfo", "build-development": "tsc && tsc-alias", "build-preview": "email build -d src/emails", "build-production": "tsc && tsc-alias", "build-staging": "tsc && tsc-alias", "format": "prettier --write \"**/*.{ts,tsx,md}\"", "format:check": "prettier \"**/*.{ts,tsx,md}\" --check", "i18n": "typesafe-i18n", "lint": "eslint \"**/*.{ts,tsx}\"", "lint-fix": "eslint \"**/*.{ts,tsx}\" --fix", "lint-staged": "lint-staged --no-stash", "preinstall": "npx only-allow pnpm", "sentry:sourcemaps": "sentry-cli sourcemaps inject --org ${SENTRY_ORG} --project ${SENTRY_PROJECT} --release ${SENTRY_RELEASE} ./lib && sentry-cli sourcemaps upload --org ${SENTRY_ORG} --project ${SENTRY_PROJECT} --release ${SENTRY_RELEASE} ./lib", "start-dev": "concurrently \"pnpm run i18n\" \"pnpm run update-ts-paths-and-watch\"", "start-email": "email dev -p 5001 -d src/emails", "start-local": "concurrently \"pnpm run i18n\" \"pnpm run update-ts-paths-and-watch\"", "start-production": "concurrently \"pnpm run i18n\" \"pnpm run update-ts-paths-and-watch\"", "start-staging": "concurrently \"pnpm run i18n\" \"pnpm run update-ts-paths-and-watch\"", "update-ts-paths-and-watch": "tsc-alias -w", "watch-build": "concurrently --kill-others \"tsc -w\" \"tsc-alias -w\""}, "types": "./lib/index.d.ts", "version": "0.0.1"}