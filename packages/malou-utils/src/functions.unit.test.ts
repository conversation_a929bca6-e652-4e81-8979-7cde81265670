import { getLanguageFromCountryCode } from './constants/languages';
import { formatPhoneNumber } from './functions/format-phone';
import { isTextContainingPhoneNumber } from './modules';

describe('functions', () => {
    describe('formatPhoneNumber', () => {
        it('should format a valid phone number for France', () => {
            const phoneNumber = '+33 612345678';
            const country = 'FR';
            const expected = { prefix: 33, digits: 612345678 };
            const result = formatPhoneNumber(phoneNumber, country);
            expect(result).toEqual(expected);
        });

        it('should format a valid phone number for United States', () => {
            const phoneNumber = '+14155552671';
            const country = 'US';
            const expected = { prefix: 1, digits: 4155552671 };
            const result = formatPhoneNumber(phoneNumber, country);
            expect(result).toEqual(expected);
        });

        it("should format a valid phone number without '+' car for France", () => {
            const phoneNumber = '1234567890';
            const country = 'FR';
            const expected = { digits: 1234567890, prefix: 33 };
            const result = formatPhoneNumber(phoneNumber, country);
            expect(result).toEqual(expected);
        });

        it('should format a valid phone number for Germany', () => {
            const phoneNumber = '+49123456789';
            const country = 'DE';
            const expected = { prefix: 49, digits: 123456789 };
            const result = formatPhoneNumber(phoneNumber, country);
            expect(result).toEqual(expected);
        });

        it('should format a valid phone number for United Kingdom', () => {
            const phoneNumber = '+441234567890';
            const country = 'GB';
            const expected = { prefix: 44, digits: 1234567890 };
            const result = formatPhoneNumber(phoneNumber, country);
            expect(result).toEqual(expected);
        });

        it('should format a valid phone number for Canada', () => {
            const phoneNumber = '+14165551234';
            const country = 'CA';
            const expected = { prefix: 1, digits: 4165551234 };
            const result = formatPhoneNumber(phoneNumber, country);
            expect(result).toEqual(expected);
        });

        it('should format a valid phone number for Switzerland', () => {
            const phoneNumber = '+41791234567';
            const country = 'CH';
            const expected = { prefix: 41, digits: 791234567 };
            const result = formatPhoneNumber(phoneNumber, country);
            expect(result).toEqual(expected);
        });

        it('should format a valid phone number for Belgium', () => {
            const phoneNumber = '+3212345678';
            const country = 'BE';
            const expected = { prefix: 32, digits: 12345678 };
            const result = formatPhoneNumber(phoneNumber, country);
            expect(result).toEqual(expected);
        });

        it('should format a valid phone number for Spain', () => {
            const phoneNumber = '+34123456789';
            const country = 'ES';
            const expected = { prefix: 34, digits: 123456789 };
            const result = formatPhoneNumber(phoneNumber, country);
            expect(result).toEqual(expected);
        });

        it('should format a valid phone number for Italy', () => {
            const phoneNumber = '+39 123456789';
            const country = 'IT';
            const expected = { prefix: 39, digits: 123456789 };
            const result = formatPhoneNumber(phoneNumber, country);
            expect(result).toEqual(expected);
        });

        it('should format a valid phone number for Portugal', () => {
            const phoneNumber = '+351 123456789';
            const country = 'PT';
            const expected = { prefix: 351, digits: 123456789 };
            const result = formatPhoneNumber(phoneNumber, country);
            expect(result).toEqual(expected);
        });

        it('should format a valid phone number for Dubai', () => {
            const phoneNumber = '+971 123456789';
            const country = 'AE';
            const expected = { prefix: 971, digits: 123456789 };
            const result = formatPhoneNumber(phoneNumber, country);
            expect(result).toEqual(expected);
        });

        it('should return null if phone is empty string', () => {
            const phoneNumber = '';
            const country = 'FR';
            const expected = null;
            const result = formatPhoneNumber(phoneNumber, country);
            expect(result).toEqual(expected);
        });
    });

    describe('getLanguageFromCountryCode', () => {
        it('should return english for US', () => {
            const result = getLanguageFromCountryCode('US');
            expect(result).toBe('en');
        });

        it('should return arabic for AE', () => {
            const result = getLanguageFromCountryCode('AE');
            expect(result).toBe('ar');
        });

        it('should return spanish for ES', () => {
            const result = getLanguageFromCountryCode('ES');
            expect(result).toBe('es');
        });

        it('should return english as default for an unknown country', () => {
            const result = getLanguageFromCountryCode('WTF');
            expect(result).toBe('en');
        });

        it('should return english as default if empty value is specified', () => {
            const result = getLanguageFromCountryCode('');
            expect(result).toBe('en');
        });

        it('should return english as default if no value is specified', () => {
            const result = getLanguageFromCountryCode(undefined as any);
            expect(result).toBe('en');
        });
    });

    describe('isTextContainingPhoneNumber', () => {
        it('should return true if text contains a phone number FR', () => {
            const text = 'My phone number is +33 612345678';
            const result = isTextContainingPhoneNumber(text);
            expect(result).toBe(true);
        });

        it('should return true if potential phone number', () => {
            const text = 'My phone number is 1234567890';
            const result = isTextContainingPhoneNumber(text);
            expect(result).toBe(true);
        });

        it('should return false if text is empty', () => {
            const text = '';
            const result = isTextContainingPhoneNumber(text);
            expect(result).toBe(false);
        });

        it('should return true if text contains a phone number US', () => {
            const text = 'My phone number is *************';
            const result = isTextContainingPhoneNumber(text);
            expect(result).toBe(true);
        });

        it('should return true if text contains a phone number IT', () => {
            const text = 'My phone number is +39 123456789';
            const result = isTextContainingPhoneNumber(text);
            expect(result).toBe(true);
        });

        it('should return true if text contains a phone number ES', () => {
            const text = 'My phone number is +34 123456789';
            const result = isTextContainingPhoneNumber(text);
            expect(result).toBe(true);
        });
        it('should return false if text does not contain a phone number', () => {
            const text = 'My phone number is';
            const result = isTextContainingPhoneNumber(text);
            expect(result).toBe(false);
        });
        it('it should not return true for a date', () => {
            const text = '1978';
            const result = isTextContainingPhoneNumber(text);
            expect(result).toBe(false);
        });
    });
});
