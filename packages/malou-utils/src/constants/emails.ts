export enum EmailType {
    ACCESS_UPDATE = 'access_update',
    AI_API_HARD_LIMIT_REACHED = 'ai_api_hard_limit_reached',
    AI_API_SOFT_LIMIT_REACHED = 'ai_api_soft_limit_reached',
    BOOSTER_PACK_SUBSCRIPTION_REQUEST = 'booster_pack_subscription_request',
    CLOSED_FEEDBACK = 'closed_feedback',
    CONFIRM_CREATE_ACCOUNT = 'confirm_create_account',
    CREATE_GUEST_ACCOUNT = 'create_guest_account',
    CREATE_PAGE = 'create_page',
    CREATE_RESTAURANT = 'create_restaurant',
    DOWNLOAD_MOBILE_APP = 'download_mobile_app',
    EMAIL_VERIFICATION = 'email_verification',
    EMPTY_STOCK = 'empty_stock',
    GIFT_EXPIRES_SOON = 'gift_expires_soon',
    NEW_FEEDBACK_MESSAGE = 'new_feedback_message',
    NEW_MESSAGE_RECEIVED = 'new_message_received',
    OPENED_FEEDBACK = 'opened_feedback',
    POST_LOCATION_EXPIRED = 'post_location_expired',
    RESET_PASSWORD = 'reset_password',
    REVIEW_BOOSTER = 'review_booster',
    REVIEW_BOOSTER_TEST = 'review_booster_test',
    REVIEW_REPLY = 'review_reply',
    RETRIEVE_GIFT = 'retrieve_gift',
    UPDATE = 'update',
    USER_REVOKED_FB_CONNECTION = 'user_revoked_fb_connection',
    WOF_LIVE_TOMORROW = 'wof_live_tomorrow',
    WRONG_PLATFORM_ACCESS = 'wrong_platform_access',
}

export enum EmailCategory {
    REVIEW_BOOSTER = 'review_booster',
    ADMIN_NOTIF = 'admin_notif',
    USER_NOTIF = 'user_notif',
    REVIEW_REPLY = 'review_reply',
    REVIEWS_REPORT = 'reviews_report',
    FEEDBACK_NOTIFICATION = 'feedback_notification',
    MESSAGES_NOTIFICATION = 'messages_notification',
    PERMISSIONS = 'permissions',
    POST_NOTIFICATION = 'post_notification',
    AI_NOTIFICATION = 'ai_notification',
    MOBILE_APP_NOTIFICATION = 'mobile_app_notification',
    WHEEL_OF_FORTUNE_NOTIFICATION = 'wheel_of_fortune_notification',
}
