export enum BricksCategory {
    VENUE_TYPE = 'venueType',
    VENUE_CATEGORY = 'venueCategory',
    VENUE_SPECIAL = 'venueSpecial',
    VENUE_LOCATION = 'venueLocation',
    VENUE_LABEL = 'venueLabel',
    TOURISTIC_AREA = 'touristicArea',
    STATION = 'station',
    VENUE_OFFER = 'venueOffer',
    VENUE_EQUIPMENT = 'venueEquipment',
    VENUE_ATTRIBUTE = 'venueAttribute',
    VENUE_AUDIENCE = 'venueAudience',
    STANDARD = 'standard',
    RESTAURANT_NAME = 'restaurantName',
    REVIEWER_NAME = 'reviewerName',
}

export enum BricksAiCategory {
    VENUE_TYPE = 'venueType',
    VENUE_CATEGORY = 'venueCategory',
    VENUE_DISH = 'venueDish',
    VENUE_LOCATION = 'venueLocation',
    VENUE_SERVICE = 'venueService',
    VENUE_EQUIPMENT = 'venueEquipment',
    VENUE_CHARACTERISTICS = 'venueCharacteristics',
    VENUE_AUDIENCE = 'venueAudience',
    STANDARD = 'standard',
}

export enum BrickType {
    CATEGORY_TO_SPECIAL = 'category_to_special',
    CATEGORY_TO_TYPE = 'category_to_type',
    POSTAL_CODE_TO_TOURISTIC_AREA = 'postal_code_to_touristic_area',
}
