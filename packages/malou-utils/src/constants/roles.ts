export enum Role {
    ADMIN = 'admin',
    MALOU_BASIC = 'malou-basic',
    MALOU_GUEST = 'malou-guest',
    MALOU_FREE = 'malou-free',
}

export enum UserCaslRole {
    ADMIN = 'admin', // This role bypasses all casl checks. It is never assigned directly to a user but rather replaced as a UserCaslRole when user role is admin
    OWNER = 'owner',
    GUEST = 'guest',
}

export enum CaslRole {
    ADMIN = 'admin', // This role bypasses all casl checks. It is never assigned directly to a userRestaurant but rather replaced as a CaslRole when user role is admin
    OWNER = 'owner',
    MODERATOR = 'moderator',
    EDITOR = 'editor',
    GUEST = 'guest',
}

export const mapRoleStringToCaslEnum = (role: string): CaslRole => {
    switch (role) {
        case 'admin':
            return CaslRole.ADMIN;
        case 'editor':
            return CaslRole.EDITOR;
        case 'guest':
            return CaslRole.GUEST;
        case 'owner':
            return CaslRole.OWNER;
        case 'moderator':
            return CaslRole.MODERATOR;
        default:
            return CaslRole.GUEST;
    }
};
