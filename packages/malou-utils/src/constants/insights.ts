export enum StoredInDBInsightsMetric {
    FOLLOWERS = 'followers',
    PLATFORM_RATING = 'platform_rating',
    EMAIL_CONTACTS = 'email_contacts',
    TEXT_MESSAGE_CLICKS = 'text_message_clicks',
    WEBSITE_CLICKS = 'website_clicks',
    IMPRESSIONS = 'impressions',
    POST_COUNT = 'post_count',
    PHONE_CALL_CLICKS = 'phone_call_clicks',
    BUSINESS_BOOKINGS = 'business_bookings',
    BUSINESS_FOOD_MENU_CLICKS = 'business_food_menu_clicks',
    DIRECTION_REQUESTS = 'direction_requests',
    BUSINESS_IMPRESSIONS_DESKTOP_MAPS = 'business_impressions_desktop_maps',
    BUSINESS_IMPRESSIONS_DESKTOP_SEARCH = 'business_impressions_desktop_search',
    BUSINESS_IMPRESSIONS_MOBILE_MAPS = 'business_impressions_mobile_maps',
    BUSINESS_IMPRESSIONS_MOBILE_SEARCH = 'business_impressions_mobile_search',
    BUSINESS_FOOD_ORDERS = 'business_food_orders',
    SHARES = 'shares',
    SAVES = 'saves',
    PAGE_POST_ENGAGEMENTS = 'page_post_engagements', // facebook only
}

// TODO: This should change in the near future to include the metric 'views' instead of 'impressions' as Facebook documentation states
// For the moment this is not the case, the 'views' metric is only available on direct call, not through webhook
// https://developers.facebook.com/blog/post/2025/01/21/making-it-easier-to-build-integrations-across-the-instagram-api-and-marketing-api/
// Developer forum thread regarding the issue: https://developers.facebook.com/community/threads/4032496000406458/
export enum IgWebhookStoryInsightsMetric {
    TAPS_BACK = 'taps_back',
    EXITS = 'exits',
    REACH = 'reach',
    IMPRESSIONS = 'impressions',
    TAPS_FORWARD = 'taps_forward',
    REPLIES = 'replies',
}

export enum IgLiveStoryInsightsMetric {
    TAP_BACK = 'tap_back',
    TAP_EXIT = 'tap_exit',
    TAP_FORWARD = 'tap_forward',
    SWIPE_FORWARD = 'swipe_forward',
}

export enum MalouMetric {
    IMPRESSIONS = 'impressions',
    ENGAGEMENTS = 'engagements',
    POSTS = 'posts',
    FOLLOWERS = 'followers',
    PLATFORM_RATING = 'platform_rating',
    QUERIES_DIRECT = 'queries_direct',
    QUERIES_INDIRECT = 'queries_indirect',
    ACTIONS_WEBSITE = 'actions_website',
    ACTIONS_PHONE = 'actions_phone',
    ACTIONS_DRIVING_DIRECTIONS = 'actions_driving_directions',
    BUSINESS_IMPRESSIONS_DESKTOP_MAPS = 'business_impressions_desktop_maps',
    BUSINESS_IMPRESSIONS_DESKTOP_SEARCH = 'business_impressions_desktop_search',
    BUSINESS_IMPRESSIONS_MOBILE_MAPS = 'business_impressions_mobile_maps',
    BUSINESS_IMPRESSIONS_MOBILE_SEARCH = 'business_impressions_mobile_search',
    BUSINESS_FOOD_ORDERS = 'business_food_orders',
    REACH = 'reach',
    TAPS_FORWARD = 'taps_forward',
    TAPS_BACK = 'taps_back',
    TAPS_EXITS = 'taps_exits',
    REPLIES = 'replies',
    ACTIONS_BOOKING_CLICK = 'actions_booking',
    ACTIONS_MENU_CLICK = 'actions_menu',
}

export enum AggregationType {
    TOTAL = 'total',
    AVERAGE = 'average',
    MAX = 'max',
    MIN = 'min',
}

export enum GmbPostsMetric {
    LOCAL_POST_VIEWS_SEARCH = 'LOCAL_POST_VIEWS_SEARCH',
    LOCAL_POST_ACTIONS_CALL_TO_ACTION = 'LOCAL_POST_ACTIONS_CALL_TO_ACTION',
}

export enum AggregationTimeScale {
    TOTAL = 'total',
    BY_DAY = 'by_day',
    BY_WEEK = 'by_week',
    BY_MONTH = 'by_month',
}

export enum InsightsTab {
    // todo: remove SEO when release-keywords-insights-v2 feature flag is removed
    SEO = 'seo',
    SEO_KEYWORDS = 'seo_keywords',
    SEO_IMPRESSIONS = 'seo_impressions',
    E_REPUTATION = 'e_reputation',
    E_REPUTATION_WITH_NEW_SEMANTIC_ANALYSIS = 'e_reputation_with_new_semantic_analysis',
    SOCIAL_NETWORKS = 'social_networks',
    BOOSTERS = 'boosters',
    AGGREGATED_SEO = 'aggregated_seo',
    SUMMARY = 'summary',
    // remove AGGREGATED_SEO_KEYWORDS when release-aggregated-keywords-insights-v2 feature flag is removed
    AGGREGATED_SEO_KEYWORDS = 'aggregated_seo_keywords',
    AGGREGATED_SEO_KEYWORDS_V2 = 'aggregated_seo_keywords_v2',
    AGGREGATED_SEO_IMPRESSIONS = 'aggregated_seo_impressions',
    AGGREGATED_E_REPUTATION = 'aggregated_e_reputation',
    AGGREGATED_E_REPUTATION_WITH_NEW_SEMANTIC_ANALYSIS = 'aggregated_e_reputation_with_new_semantic_analysis',
    AGGREGATED_SOCIAL_NETWORKS = 'aggregated_social_networks',
    AGGREGATED_BOOSTERS = 'aggregated_boosters',
    AGGREGATED_SUMMARY = 'aggregated_summary',
}

export const aggregatedInsightsTabs = [
    InsightsTab.AGGREGATED_SEO,
    InsightsTab.AGGREGATED_E_REPUTATION,
    InsightsTab.AGGREGATED_SOCIAL_NETWORKS,
    InsightsTab.AGGREGATED_BOOSTERS,
    InsightsTab.AGGREGATED_SUMMARY,
];

export enum InsightsChart {
    // seo
    ACTIONS = 'actions',
    APPARITIONS = 'apparitions',
    KEYWORDS = 'keywords',
    KEYWORD_SEARCH_IMPRESSIONS = 'keyword_search_impressions',
    // e-reputation
    REVIEW_RATINGS_KPIS = 'review_ratings_kpis',
    REVIEW_KPIS = 'review_kpis',
    REVIEW_RATING_EVOLUTION = 'review_rating_evolution',
    REVIEW_RATING_TOTAL = 'review_rating_total',
    REVIEW_ANALYSES_TAG_CHARTS = 'review_analyses_tag_charts',
    REVIEW_ANALYSES_TAG_EVOLUTION = 'review_analyses_tag_evolution',
    SEMANTIC_ANALYSIS_TOP_TOPICS = 'semantic_analysis_top_topics',
    SEMANTIC_ANALYSIS_TOPICS_EVOLUTION = 'semantic_analysis_topics_evolution',
    SEMANTIC_ANALYSIS_REVIEWS = 'semantic_analysis_reviews',
    // social network
    COMMUNITY = 'community',
    ENGAGEMENT = 'engagement',
    POST_INSIGHTS = 'post_insights',
    REEL_INSIGHTS = 'reel_insights',
    STORY_INSIGHTS = 'story_insights',
    // boosters
    BOOSTERS_PRIVATE_REVIEWS_COUNT = 'boosters_private_reviews_count',
    BOOSTERS_SCAN_COUNT = 'boosters_scan_count',
    BOOSTERS_TOTEMS_ESTIMATED_REVIEWS_COUNT = 'boosters_totems_estimated_reviews_count',
    BOOSTERS_WHEEL_OF_FORTUNE_GIFTS_DISTRIBUTION = 'boosters_wheel_of_fortune_gifts_distribution',
    // aggregated seo
    AGGREGATED_RANKINGS = 'aggregated_rankings',
    AGGREGATED_ACTIONS = 'aggregated_actions',
    AGGREGATED_APPARITIONS = 'aggregated_apparitions',
    AGGREGATED_TOP_SEARCH_KEYWORDS = 'aggregated_top_search_keywords',
    // aggregated e-reputation
    AGGREGATED_REVIEW_RATINGS_KPIS = 'aggregated_review_ratings_kpis',
    AGGREGATED_REVIEWS_COUNT = 'aggregated_reviews_count',
    AGGREGATED_REVIEWS_RATING_AVERAGE = 'aggregated_reviews_rating_average',
    AGGREGATED_REVIEW_ANALYSES_TAG_CHARTS = 'aggregated_review_analyses_tag_charts',
    AGGREGATED_REVIEW_ANALYSES_TAG_EVOLUTION = 'aggregated_review_analyses_tag_evolution',
    AGGREGATED_SEMANTIC_ANALYSIS_AVERAGE_BY_CATEGORY = 'aggregated_semantic_analysis_average_by_category',
    AGGREGATED_SEMANTIC_ANALYSIS_CHART = 'aggregated_semantic_analysis_chart',
    AGGREGATED_SEMANTIC_ANALYSIS_BREAKDOWN_BY_RESTAURANT = 'aggregated_semantic_analysis_breakdown_by_restaurant',
    // social network
    AGGREGATED_TOP_POSTS_CARDS = 'aggregated_top_posts_cards',
    AGGREGATED_PUBLICATIONS_TABLE = 'aggregated_publications_table',
    // aggregated boosters
    AGGREGATED_BOOSTERS_SCAN_COUNT = 'aggregated_boosters_scan_count',
    AGGREGATED_BOOSTERS_PRIVATE_REVIEWS_COUNT = 'aggregated_boosters_private_reviews_count',
    AGGREGATED_BOOSTERS_REVIEWS_COUNT = 'aggregated_boosters_reviews_count',
    AGGREGATED_BOOSTERS_WHEEL_OF_FORTUNE_GIFTS_DISTRIBUTION = 'aggregate_boosters_wheel_of_fortune_gifts_distribution',
    AGGREGATED_BOOSTERS_WHEEL_OF_FORTUNE_ESTIMATED_REVIEWS_COUNT = 'aggregated_boosters_wheel_of_fortune_estimated_reviews_count',
}

export enum CsvInsightChart {
    // seo
    KEYWORDS = 'keywords',
    GMB_VISIBILITY = 'gmb_visibility',
    KEYWORD_SEARCH_IMPRESSIONS = 'keyword_search_impressions',
    // e-reputation
    REVIEWS_RATINGS_EVOLUTION = 'reviews_ratings_evolution',
    REVIEWS_RATINGS_TOTAL = 'reviews_ratings_total',
    PLATFORMS_RATINGS = 'platforms_ratings',
    SEMANTIC_ANALYSIS_TOPICS = 'semantic_analysis_topics',
    SEMANTIC_ANALYSIS_DETAILS = 'semantic_analysis_details',
    // e-reputation
    AGGREGATED_PLATFORMS_RATINGS = 'aggregated_platforms_ratings',
    AGGREGATED_SEMANTIC_ANALYSIS_TOP_TOPICS = 'aggregated_semantic_analysis_top_topics',
    AGGREGATED_SEMANTIC_ANALYSIS_BY_CATEGORY = 'aggregated_semantic_analysis_by_category',
    // aggregated seo
    AGGREGATED_GMB_VISIBILITY = 'aggregated_gmb_visibility',
    AGGREGATED_RANKINGS = 'aggregated_rankings',
    AGGREGATED_TOP_SEARCH_KEYWORDS = 'aggregated_top_search_keywords',
    // social network
    PUBLICATIONS = 'publications',
    STORIES = 'stories',
    ALL_FOLLOWERS = 'all_followers',
    FB_FOLLOWERS = 'fb_followers',
    IG_FOLLOWERS = 'ig_followers',
    TIKTOK_FOLLOWERS = 'tiktok_followers',
    // boosters
    BOOSTERS_SCAN_COUNT = 'boosters_scan_count',
    BOOSTERS_REVIEWS_COUNT = 'boosters_reviews_count',
    BOOSTERS_WHEEL_OF_FORTUNE_GIFTS_DISTRIBUTION = 'boosters_wheel_of_fortune_gifts_distribution',
    // aggregated social network
    AGGREGATED_ALL_FOLLOWERS = 'aggregated_all_followers',
    AGGREGATED_FB_FOLLOWERS = 'aggregated_fb_followers',
    AGGREGATED_IG_FOLLOWERS = 'aggregated_ig_followers',
    AGGREGATED_TIKTOK_FOLLOWERS = 'aggregated_tiktok_followers',
    AGGREGATED_PUBLICATIONS = 'aggregated_publications',
    AGGREGATED_STORIES = 'aggregated_stories',
    AGGREGATED_REVIEW_COUNT = 'aggregated_review_count',
    AGGREGATED_AVERAGE_REVIEWS_RATINGS = 'aggregated_average_reviews_ratings',
    // aggregated boosters
    AGGREGATED_BOOSTERS_SCAN_COUNT = 'aggregated_boosters_scan_count',
    AGGREGATED_BOOSTERS_REVIEWS_COUNT = 'aggregated_boosters_reviews_count',
    AGGREGATED_BOOSTERS_PRIVATE_REVIEWS_COUNT = 'aggregated_boosters_private_reviews_count',
    AGGREGATED_BOOSTERS_WHEEL_OF_FORTUNE_GIFTS_DISTRIBUTION = 'aggregated_boosters_wheel_of_fortune_gifts_distribution',
    // SUMMARY
    INSIGHTS_SUMMARY = 'insights_summary',
    AGGREGATED_INSIGHTS_SUMMARY = 'aggregated_insights_summary',
}
