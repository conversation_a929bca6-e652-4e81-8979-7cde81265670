export enum FileFormat {
    PNG = 'png',
    JPEG = 'jpeg',
    JPG = 'jpg',
    MOV = 'mov',
    QUICKTIME = 'quicktime',
    MP4 = 'mp4',
    heic = 'heic',
    heif = 'heif',
}

export enum FileType {
    CSV = 'csv',
    XLS = 'xls',
    XLSX = 'xlsx',
    DOC = 'doc',
    DOCX = 'docx',
    PDF = 'pdf',
    TXT = 'txt',
    RTF = 'rtf',
    ODT = 'odt',
}

export const getFileFormatFromExtension = (extension: string): FileFormat => {
    switch (extension.toLowerCase()) {
        case 'png':
            return FileFormat.PNG;
        case 'jpg':
            return FileFormat.JPG;
        case 'jpeg':
            return FileFormat.JPEG;
        case 'mp4':
            return FileFormat.MP4;
        case 'quicktime':
            return FileFormat.QUICKTIME;
        case 'mov':
            return FileFormat.MOV;
        default:
            return FileFormat.JPEG;
    }
};
