export enum ProviderClientSource {
    MANUAL = 'manual',
    MALOU = 'malou',
    ZENCHEF = 'zenchef',
    ZELTY = 'zelty',
    LAFOURCHETTE = 'lafourchette',
    PULP = 'pulp',
    WHEEL_OF_FORTUNE = 'wheel_of_fortune',
    COMO = 'como',
}

export enum ProviderClientCivility {
    MALE = 'male',
    FEMALE = 'female',
    OTHER = 'other',
}

export interface ProviderClientPhone {
    prefix: number;
    digits: number;
}

export enum Civility {
    MALE = 'male',
    FEMALE = 'female',
    OTHER = 'other',
}

export enum ContactMode {
    EMAIL = 'email',
    SMS = 'sms',
}

export enum ClientSource {
    MANUAL = 'manual',
    MALOU = 'malou',
    ZENCHEF = 'zenchef',
    ZELTY = 'zelty',
    LAFOURCHETTE = 'lafourchette',
    PULP = 'pulp',
    WHEEL_OF_FORTUNE = 'wheel_of_fortune',
    TOTEMS = 'totems',
}

export enum ClientVariable {
    FIRSTNAME = '#CLIENT_FIRSTNAME#',
    LASTNAME = '#CLIENT_LASTNAME#',
}

export const clientsFilesFormat = Object.freeze([
    {
        key: 'zenchef',
        headers: [
            'Prenom',
            'Nom',
            'Email',
            'Telephone',
            'Pays',
            'Statut destinataire',
            'Email optin market',
            'Date inscription mail',
            'SMS optin market',
            'Date inscription SMS',
            'Email optin review',
            'Date inscription avis par mail',
            'SMS review market',
            'Date inscription avis par SMS',
            'lang',
            'vip',
            'has_no_show',
            'is_new',
            'is_blacklisted',
            'retention_rate_label',
            'retention_rate',
            'has_opt_in_market_mail',
            'has_opt_in_review_mail',
            'created_at',
            'updated_at',
        ],
        required: ['Prenom', 'Nom', 'Telephone', 'Email'],
    },
    {
        key: 'pulp',
        headers: [''],
        required: [''],
    },
    {
        key: 'lafourchette',
        headers: [
            'Civilité',
            'Prénom',
            'Nom',
            'Pays',
            'Ville',
            'Code Postal',
            'Adresse',
            'Mobile Personnel',
            'Email personnel',
            'Langue',
            "Date d'inscription",
            'Newsletter',
            'Date de naissance',
            'Statut',
            'VIP',
            'Fax Personnel',
            "Complément d'adresse",
            'Prescripteur',
            'Commission négociée',
            "Nombre d'apport d'affaire total",
            "Chiffre d'affaire apporté total",
            'Entreprise',
            'Fonction',
            'Email Pro',
            'Mobile Pro',
            'Téléphone pro',
            'Pays Pro',
            'Adresse Pro',
            "Complément d'adresse Pro",
            'Ville Pro',
            'Allergie',
            'Nom Allergie',
            'Végétarien',
            'Handicap',
            'Préférence Nourriture',
            'Tables préférées',
            'Note',
            'Newsletter',
            'Restaurant de référence',
            'Date de mise à jour',
            "Date d'inscription",
            "Nombre d'annulation",
            'Nombre de no-show',
            'Total dépense',
        ],
        matchedHeaders: ['Dernière date de visite', 'Nombre de visites'],
        required: ['Prénom', 'Nom', 'Mobile Personnel', 'Email personnel'],
    },
    {
        key: 'zelty',
        headers: [
            'Nom',
            'Prénom',
            'N° de rue',
            'Ville',
            'Code postal',
            'Nom de rue',
            'N° de rue',
            'Téléphone',
            'Mail',
            "Date d'inscription",
            'Optin Mail',
            'CA actuel',
            'Optin SMS',
            'Statut',
            'Date de naissance',
            'Dernier restaurant',
            'Date de la dernière commande',
            'Nombre de commandes',
            'CA',
            'Points de fidélités',
            'Info interne',
            'Info client',
            "Complément d'adresse",
            'Batiment',
            'Porte',
            'Étage',
            'Digicode',
            'Identifiant externe',
            'Carte fidélité',
            'VIP',
            'Téléphone 2',
            'Entreprise',
            'ID',
        ],
        required: ['Nom', 'Prénom', 'Téléphone', 'Mail'],
    },
    {
        key: 'malou',
        headers: [
            'Civilité (H/F/NSP)',
            'Prénom',
            'Nom',
            'Pays',
            'Ville',
            'Code Postal',
            'Numéro de téléphone',
            'Email',
            'Langue parlée',
            'Date de la dernière réservation ou du click and collect',
            'Nombre de visites/réservations',
            'Accepte de recevoir des mails',
            'Accepte de recevoir des sms',
        ],
        required: ['Nom', 'Prénom', 'Numéro de téléphone', 'Email'],
    },
]);

export enum ClientFileError {
    EMPTY = 'empty_file',
    MISSING = 'missing_headers',
}

export const ubereatsClientCredentials = {
    key: 'UBEREATS_CLIENT_CREDENTIALS',
    ttl: 2592000, // from ubereats doc: https://developer.uber.com/docs/eats/guides/authentication#example-response
};
