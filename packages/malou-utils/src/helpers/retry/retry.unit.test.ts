import { err, ok } from 'neverthrow';
import assert from 'node:assert/strict';

import { waitFor } from '../../functions/wait-for';
import { retry, RetryError, retryResult } from './retry';

jest.mock('../../functions/wait-for', () => ({
    waitFor: jest.fn(),
}));

const mockedWaitFor = waitFor as jest.MockedFunction<typeof waitFor>;

describe('retry.ts', () => {
    beforeEach(() => {
        jest.clearAllMocks();
    });

    describe('retry', () => {
        it('should return if success', async () => {
            const fnRes = 'hey la team';

            const result = await retry(async () => fnRes);
            expect(result.isOk()).toBe(true);
            assert(result.isOk());
            expect(result.value).toBe(fnRes);
            expect(mockedWaitFor).toHaveBeenCalledTimes(0);
        });

        it('should return if success with isSuccess fn', async () => {
            const fnRes = 'hey la team';

            const result = await retry(async () => fnRes, {
                isSuccess: (res) => res.length === 11,
            });
            expect(result.isOk()).toBe(true);
            assert(result.isOk());
            expect(result.value).toBe(fnRes);
            expect(mockedWaitFor).toHaveBeenCalledTimes(0);
        });

        it('should return if success after 3 attempts', async () => {
            const fnRes = 'hey la team';

            const result = await retry(async () => fnRes, {
                isSuccess: (res, attempt) => attempt === 3,
            });
            expect(result.isOk()).toBe(true);
            assert(result.isOk());
            expect(result.value).toBe(fnRes);
            expect(mockedWaitFor).toHaveBeenCalledTimes(2);
        });

        it('should return STILL_ERROR_AFTER_RETRIES', async () => {
            const fnRes = 'hey la team';

            const result = await retry(async () => fnRes, {
                attempts: 5,
                isSuccess: () => false,
            });
            expect(result.isErr()).toBe(true);
            assert(result.isErr());
            expect(result.error).toStrictEqual({
                error: RetryError.STILL_ERROR_AFTER_RETRIES,
                lastResult: err(undefined),
            });
            expect(mockedWaitFor).toHaveBeenCalledTimes(4);
        });

        it('should return SHOULD_NOT_RETRY_AFTER_SUCCESS', async () => {
            const result = await retry(async () => 'coucou', {
                attempts: 5,
                isSuccess: () => false,
                shouldRetrySuccess: () => false,
            });
            expect(result.isErr()).toBe(true);
            assert(result.isErr());
            expect(result.error).toStrictEqual({
                error: RetryError.SHOULD_NOT_RETRY_AFTER_SUCCESS,
                originalValue: 'coucou',
            });
            expect(mockedWaitFor).toHaveBeenCalledTimes(0);
        });

        it('should return SHOULD_NOT_RETRY_AFTER_ERROR', async () => {
            const error = new Error('error');
            const result = await retry(
                async () => {
                    throw error;
                },
                {
                    attempts: 5,
                    isSuccess: () => true,
                    shouldRetryError: () => false,
                }
            );
            expect(result.isErr()).toBe(true);
            assert(result.isErr());
            expect(result.error).toStrictEqual({
                error: RetryError.SHOULD_NOT_RETRY_AFTER_ERROR,
                originalError: error,
            });
            expect(mockedWaitFor).toHaveBeenCalledTimes(0);
        });
    });

    describe('retryResult', () => {
        it('should return if success', async () => {
            const fnRes = 'hey la team';

            const result = await retryResult(async () => ok(fnRes));
            expect(result.isOk()).toBe(true);
            assert(result.isOk());
            expect(result.value).toBe(fnRes);
            expect(mockedWaitFor).toHaveBeenCalledTimes(0);
        });

        it('should return if success with isSuccess fn', async () => {
            const fnRes = 'hey la team';

            const result = await retryResult(async () => ok(fnRes), {
                isSuccess: (res) => res.length === 11,
            });
            expect(result.isOk()).toBe(true);
            assert(result.isOk());
            expect(result.value).toBe(fnRes);
            expect(mockedWaitFor).toHaveBeenCalledTimes(0);
        });

        it('should return if success after 3 attempts', async () => {
            const fnRes = 'hey la team';

            const result = await retryResult(async () => ok(fnRes), {
                isSuccess: (res, attempt) => attempt === 3,
            });
            expect(result.isOk()).toBe(true);
            assert(result.isOk());
            expect(result.value).toBe(fnRes);
            expect(mockedWaitFor).toHaveBeenCalledTimes(2);
        });

        it('should return STILL_ERROR_AFTER_RETRIES', async () => {
            const fnRes = 'hey la team';

            const result = await retryResult(async () => ok(fnRes), {
                attempts: 5,
                isSuccess: () => false,
            });
            expect(result.isErr()).toBe(true);
            assert(result.isErr());
            expect(result.error).toStrictEqual({
                error: RetryError.STILL_ERROR_AFTER_RETRIES,
                lastResult: ok('hey la team'),
            });
            expect(mockedWaitFor).toHaveBeenCalledTimes(4);
        });

        it('should return SHOULD_NOT_RETRY_AFTER_SUCCESS', async () => {
            const fnRes = 'hey la team';

            const result = await retryResult(async () => ok(fnRes), {
                attempts: 5,
                isSuccess: () => false,
                shouldRetrySuccess: () => false,
            });
            expect(result.isErr()).toBe(true);
            assert(result.isErr());
            expect(result.error).toStrictEqual({
                error: RetryError.SHOULD_NOT_RETRY_AFTER_SUCCESS,
                originalValue: 'hey la team',
            });
            expect(mockedWaitFor).toHaveBeenCalledTimes(0);
        });

        it('should return SHOULD_NOT_RETRY_AFTER_ERROR', async () => {
            const result = await retryResult(async () => err('error'), {
                attempts: 5,
                isSuccess: () => true,
                shouldRetryError: () => false,
            });
            expect(result.isErr()).toBe(true);
            assert(result.isErr());
            expect(result.error).toStrictEqual({
                error: RetryError.SHOULD_NOT_RETRY_AFTER_ERROR,
                originalError: 'error',
            });
            expect(mockedWaitFor).toHaveBeenCalledTimes(0);
        });
    });
});
