export enum FileSize {
    OCTET = 'octet',
    KILOOCTET = 'kilooctet',
    MEGAOCTET = 'megaoctet',
}

export enum InputFileType {
    PNG = 'image/png',
    JPEG = 'image/jpeg',
    HEIC = 'image/heic',
    HEIF = 'image/heif',
    SVG = 'image/svg+xml',
    VIDEO = 'video/mp4',
    QUICKTIME = 'video/quicktime',
}

export enum PictureSize {
    ORIGINAL = 'original',
    SMALL = 'small',
    COVER = 'cover',
    SMALL_COVER = 'smallCover',
    IG_FIT = 'igFit',
}

export interface PictureSizeRecord<T extends string | number | MediaDimension> {
    original: T;
    small?: T;
    cover?: T;
    smallCover?: T;
    igFit?: T;
    thumbnail256Outside?: T;
}

export interface MediaDimension {
    width: number;
    height: number;
}

export interface Area {
    left: number;
    top: number;
    width: number;
    height: number;
}

export enum MediaCategory {
    PROFILE = 'profile',
    COVER = 'cover',
    ADDITIONAL = 'additional',
}

export enum MediaType {
    PHOTO = 'photo',
    VIDEO = 'video',
    FILE = 'file',
}

export enum InputMediaType {
    IMAGE = 'image',
    VIDEO = 'video',
}

export enum MediaTagCategory {
    FOOD = 'food',
    DRINKS = 'drinks',
    LOCATION = 'location',
    SPECIAL_EVENT = 'specialEvent',
    PEOPLE = 'people',
    OTHER = 'other',
}

export enum MediaTagSubcategory {
    MAIN = 'main',
    DESSERT = 'dessert',
    BACKGROUND_TYPE = 'backgroundType',
    LANDMARK = 'landmark',
}

export enum MediaFileType {
    IMAGE = 'image',
    VIDEO = 'video',
    AUDIO = 'audio',
    DOCUMENT = 'document',
}

export enum MediaConvertedStatus {
    ERROR = 'error',
    COMPLETE = 'complete',
    PROGRESSING = 'progressing',
}
