/**
 * Generates a random password with 8 characters containing letters and numbers
 * @returns A random password string
 */
export function generateRandomPassword(): string {
    let password = '';
    const possible = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';

    for (let i = 0; i < 8; i++) {
        password += possible.charAt(Math.floor(Math.random() * possible.length));
    }
    return password;
}
