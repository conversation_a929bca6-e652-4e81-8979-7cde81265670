import { CountryCode, parsePhoneNumber } from 'libphonenumber-js';

import { COUNTRY_CODES } from '../constants/countries';

export const formatPhoneNumber = (phoneNumber: string, country: string): { prefix: number; digits: number } | null => {
    const phoneString = phoneNumber.toString();
    const countryCode = COUNTRY_CODES.find((c) => c === country.toUpperCase()) as CountryCode;
    try {
        const { nationalNumber } = parsePhoneNumber(phoneString, countryCode);
        const parsedPhone = parsePhoneNumber(nationalNumber, countryCode);
        const res = {
            prefix: parseInt(parsedPhone.countryCallingCode, 10),
            digits: parseInt(parsedPhone.nationalNumber, 10),
        };
        return res;
    } catch (error) {
        return null;
    }
};

export const formatPhoneForDisplay = (phone: { prefix?: number; digits?: number } | undefined): string | null => {
    if (!phone || !phone.prefix || !phone.digits) {
        return null;
    }

    const strDigits = phone.digits.toString();
    if (phone.prefix === 1) {
        // US phone format
        return `+${phone.prefix} (${strDigits.substring(0, 3)}) ${strDigits.substring(3, 6)}-${strDigits.substring(6)}`;
    }

    return `+${phone.prefix} ${strDigits[0]} ${strDigits
        .substring(1)
        .match(/.{1,2}/g) // split string in equal number of chunks of size 2
        ?.join(' ')}`;
};
