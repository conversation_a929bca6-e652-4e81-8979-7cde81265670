export function updateObjectDeepProperty(obj: any, path: string, value: any) {
    const keys = path.split('.');

    // Clone the object first to avoid mutating the original
    const newObj = cloneDeep(obj);

    // Traverse the object to the desired path
    let current = newObj;
    for (let i = 0; i < keys.length - 1; i++) {
        if (!current[keys[i]]) {
            current[keys[i]] = {}; // Create intermediate objects if they don't exist
        }
        current = current[keys[i]];
    }

    // Set the value at the final key
    current[keys[keys.length - 1]] = value;

    return newObj;
}

function cloneDeep(obj: any): any {
    if (obj === null || typeof obj !== 'object') {
        return obj;
    }

    if (Array.isArray(obj)) {
        return obj.map(cloneDeep);
    }

    const clonedObj: any = {};
    Object.keys(obj).forEach((key) => {
        if (Object.prototype.hasOwnProperty.call(obj, key)) {
            clonedObj[key] = cloneDeep(obj[key]);
        }
    });

    return clonedObj;
}
