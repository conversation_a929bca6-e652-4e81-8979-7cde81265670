export function flattenObject(obj: Record<string, any>, parentKey?: string) {
    let result: Record<string, any> = {};

    Object.entries(obj).forEach(([key, value]) => {
        const _key = parentKey ? `${parentKey}.${key}` : key;
        if (typeof value === 'object' && !Array.isArray(value) && value !== null && value !== undefined) {
            result = { ...result, ...flattenObject(value, _key) };
        } else {
            result[_key] = value;
        }
    });
    return result;
}
