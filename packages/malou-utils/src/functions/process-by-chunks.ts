import { isFulfilled } from './is-fulfilled';

export const processPromisesByChunks = async <T>(
    promises: (() => Promise<T>)[],
    chunkSize: number,
    afterChunkProcessed?: (chunkResults: T[]) => Promise<void>
): Promise<T[]> => {
    const results: T[] = [];

    for (let i = 0; i < promises.length; i += chunkSize) {
        const chunk = promises.slice(i, i + chunkSize);
        const chunkResults = await Promise.allSettled(chunk.map((asyncFunc) => asyncFunc()));

        const res = chunkResults
            .filter((result) => isFulfilled(result))
            .map((result) => isFulfilled(result) && result.value)
            .filter((result) => result !== undefined) as T[];

        results.push(...res);

        if (afterChunkProcessed) {
            await afterChunkProcessed(res);
        }
    }

    return results;
};
