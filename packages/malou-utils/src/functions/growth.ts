import { GrowthVariation } from '../email/report.enum';

export const computeGrowth = (current: number, previous: number): number => (previous === 0 ? 0 : ((current - previous) / previous) * 100);

export const getGrowthVariation = (growth: number): GrowthVariation => {
    if (growth > 0) {
        return GrowthVariation.UP;
    }
    if (growth < 0) {
        return GrowthVariation.DOWN;
    }
    return GrowthVariation.SAME;
};
