import { FileFormat } from '../constants/file';

export const getFileExtension = (filename: string): string => {
    const extension = filename.split('.').pop() || '';
    // filenames from Facebook & Instagram can have a query string at the end
    if (!Object.values(FileFormat).includes(extension as FileFormat)) {
        return filename.split('?')?.[0]?.split('.').pop() || '';
    }
    return extension;
};
