import AWS from 'aws-sdk';
import assert from 'node:assert';

import type { NodeCrawlerEvent, NodeCrawlerResponse } from '@malou-io/package-service-interfaces';

const LambdaClient = new AWS.Lambda({
    region: process.env.AWS_REGION,
    accessKeyId: process.env.AWS_KEY,
    secretAccessKey: process.env.AWS_SECRET,
});

const AVAILABLE_CRAWLERS_COUNT = 10;

export class Crawler<T = any> {
    constructor(private readonly isResponseValid: (res: T) => boolean) {}

    /**
     *  All Node Crawlers are launched at the same time and restarted if none of them succeed
     */
    async fetchUntilWorks({
        params,
        retries = 3,
        crawlerCount = 1,
    }: {
        params: NodeCrawlerEvent;
        retries: number;
        crawlerCount: number;
    }): Promise<T> {
        assert(params.url);
        assert(this.isResponseValid);

        for (let i = 0; i <= retries; i += 1) {
            const crawlerIndexes = this.getRotatedCrawlerIndexes({ crawlerCount, retry: i });

            try {
                const currentParams =
                    params?.headers && Array.isArray(params.headers)
                        ? {
                              ...params,
                              headers: params.headers[i % params.headers.length],
                          }
                        : params;

                const response = await this._crawl({ params: currentParams, crawlerIndexes });

                return response.data;
            } catch (err) {
                console.warn('[NODE_CRAWLERS] - Error while fetching', JSON.stringify({ err, params, crawlerIndexes }));

                if (i >= retries) {
                    console.error('[NODE_CRAWLERS] - Max tries reached', JSON.stringify({ err, params, crawlerIndexes }));
                    throw err;
                }

                await this._restartCrawlers({ crawlerIndexes });
            }

            // wait 10s before retrying
            await new Promise((resolve) => {
                setTimeout(resolve, 10000);
            });
        }

        console.error('[NODE_CRAWLERS] - Max tries reached without any response', JSON.stringify(params));
        throw new Error(`[NODE_CRAWLERS] No results found, ${JSON.stringify(params)}`);
    }

    private async _crawl({
        params,
        crawlerIndexes,
    }: {
        params: NodeCrawlerEvent;
        crawlerIndexes: number[];
    }): Promise<NodeCrawlerResponse<T>> {
        const results = await Promise.all(
            crawlerIndexes.map(async (index) => {
                try {
                    const response = await this._callCrawler({ params, index });

                    if (!this.isResponseValid(response.data)) {
                        console.error(`[NODE_CRAWLERS] - Error in crawler response ${index}:`, JSON.stringify({ response, params }));

                        return undefined;
                    }

                    console.log(`[NODE_CRAWLERS] - Success in crawler ${index}`, JSON.stringify({ response, params }));

                    return response;
                } catch (error) {
                    console.error(`[NODE_CRAWLERS] - Error in crawler ${index}:`, JSON.stringify({ error, params }));

                    return undefined;
                }
            })
        );

        const result = results.find((res) => res !== undefined);
        if (!result) {
            throw new Error(`[NODE_CRAWLERS] No results found, ${JSON.stringify(params)}`);
        }

        return result;
    }

    private getRotatedCrawlerIndexes({ crawlerCount, retry }: { crawlerCount: number; retry: number }): number[] {
        return Array.from({ length: crawlerCount }, (_, i) => (retry * crawlerCount + i) % AVAILABLE_CRAWLERS_COUNT);
    }

    private _getNodeCrawlerFunctionName(index: number) {
        const suffix =
            {
                development: 'Development',
                staging: 'Staging',
                production: 'Production',
            }[process.env.NODE_ENV || 'production'] || 'Production';

        return `nodeCrawler${index}${suffix}`;
    }

    private async _callCrawler({ params, index }: { params: NodeCrawlerEvent; index: number }): Promise<NodeCrawlerResponse<T>> {
        const functionName = this._getNodeCrawlerFunctionName(index);
        console.log(`[NODE_CRAWLERS] - Calling lambda ${functionName} with payload: ${JSON.stringify(params)}`);

        const lambdaParams: AWS.Lambda.InvocationRequest = {
            FunctionName: functionName,
            Payload: JSON.stringify(params),
        };

        try {
            const data = await LambdaClient.invoke(lambdaParams).promise();

            if (!data.Payload || data.FunctionError || data.Payload === 'null') {
                console.error(`[NODE_CRAWLERS] - No payload returned from lambda ${functionName}`, JSON.stringify({ data, params }));
                throw new Error('No payload returned from lambda');
            }

            const response: NodeCrawlerResponse<T> = JSON.parse(data.Payload as string);
            return response;
        } catch (err) {
            console.error(`[NODE_CRAWLERS] - Error calling lambda ${functionName}`, JSON.stringify({ err, params }));

            throw err;
        }
    }

    private async _restartCrawler({ crawlerIndex }: { crawlerIndex: number }) {
        try {
            // Update config to force new containers and lambdas to deploy
            await LambdaClient.updateFunctionConfiguration({
                FunctionName: this._getNodeCrawlerFunctionName(crawlerIndex),
                Environment: {
                    Variables: {
                        RESTARTED_AT: new Date().toISOString(),
                    },
                },
            }).promise();

            console.log(`[NODE_CRAWLERS] - Restarted AWS crawler n°${crawlerIndex}`);
        } catch (err) {
            console.error(`[NODE_CRAWLERS] - Failed to restart AWS crawler n°${crawlerIndex}`, err);

            throw err;
        }
    }

    private async _restartCrawlers({ crawlerIndexes }: { crawlerIndexes: number[] }) {
        await Promise.all(crawlerIndexes.map((crawlerIndex) => this._restartCrawler({ crawlerIndex })))
            .then(() => console.log('[NODE_CRAWLERS] - Restarted all AWS crawlers'))
            .catch((err) => console.warn('[NODE_CRAWLERS] - Failed to restart some AWS crawlers', err));
    }
}
