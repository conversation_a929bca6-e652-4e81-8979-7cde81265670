import { FromSchema } from 'json-schema-to-ts';
import mongoose from 'mongoose';

import { DESERIALIZE_OPTIONS } from ':core/mongoose-json-schema/definitions/constants';
import { createMongooseSchemaFromJSONSchema } from ':core/mongoose-json-schema/definitions/jsonschema-to-mongoose';

import { scanJSONSchema } from './scan-schema';

const scanSchema = createMongooseSchemaFromJSONSchema(scanJSONSchema);

scanSchema.virtual('nfc', {
    ref: 'Nfcs',
    localField: 'nfcId',
    foreignField: '_id',
    justOne: true,
});

scanSchema.index({ nfcId: 1 });
scanSchema.index({ scannedAt: 1 });
scanSchema.index({ redirectedAt: 1 });
scanSchema.index({ matchedReviewSocialId: 1 });
scanSchema.index({ 'nfcSnapshot.restaurantId': 1, nfcId: 1, scannedAt: -1 }); // used for scan insights

export type IScan = FromSchema<
    typeof scanJSONSchema,
    {
        keepDefaultedPropertiesOptional: true;
        deserialize: DESERIALIZE_OPTIONS;
    }
>;

export const ScanModel = mongoose.model<IScan>(scanJSONSchema.title, scanSchema);
