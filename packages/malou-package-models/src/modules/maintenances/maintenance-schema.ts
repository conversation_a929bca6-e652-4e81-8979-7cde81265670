import { MaintenanceStatus } from '@malou-io/package-utils';

import { JSONSchemaExtraProps } from ':core/mongoose-json-schema/definitions/types';

export const maintenanceJSONSchema = {
    $schema: 'http://json-schema.org/draft-06/schema#',
    title: 'Maintenance',
    type: 'object',
    additionalProperties: false,
    properties: {
        _id: {
            type: 'string',
            format: 'objectId',
        },
        trespass: {
            type: 'array',
            items: {
                type: 'string',
            },
        },
        redirect: {
            type: 'boolean',
            default: false,
        },
        status: {
            enum: Object.values(MaintenanceStatus),
        },
        until: {
            type: 'string',
            format: 'date-time',
        },
        createdAt: {
            type: 'string',
            format: 'date-time',
        },
        updatedAt: {
            type: 'string',
            format: 'date-time',
        },
        webhookRedirectActive: {
            type: 'boolean',
            default: false,
        },
        /*
         * @deprecated we use the localWebhookUris array instead so we can have multiple redirection routes
         */
        localWebhookUri: {
            type: 'string',
        },
        localWebhookUris: {
            type: 'array',
            items: {
                type: 'string',
            },
            default: [],
        },
        currentRelease: {
            type: 'string',
            pattern: '^[0-9]+\\.[0-9]+$',
        },
    },
    required: [
        '_id',
        'createdAt',
        'currentRelease',
        'localWebhookUri',
        'localWebhookUris',
        'redirect',
        'status',
        'trespass',
        'until',
        'updatedAt',
        'webhookRedirectActive',
    ],
    definitions: {},
} as const satisfies JSONSchemaExtraProps;
