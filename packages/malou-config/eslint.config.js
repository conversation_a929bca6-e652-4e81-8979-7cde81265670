module.exports = {
    extends: ['airbnb-base', 'airbnb-typescript/base', 'eslint:recommended', 'plugin:@typescript-eslint/recommended', 'prettier'],
    parser: '@typescript-eslint/parser',
    plugins: ['@typescript-eslint'],
    ignorePatterns: ['.eslintrc.js', 'babel.config.js'],
    rules: {
        '@typescript-eslint/no-unused-vars': [
            'error',
            {
                varsIgnorePattern: '^_',
                argsIgnorePattern: '(next)|(^_)',
            },
        ],
        'no-restricted-imports': [
            'error',
            {
                patterns: ['@malou-io/package-*/lib', '@malou-io/package-*/src'],
            },
        ],
        '@typescript-eslint/no-floating-promises': 'warn',
        'no-nested-ternary': 'off',
        '@typescript-eslint/lines-between-class-members': 'off',
        '@typescript-eslint/no-use-before-define': 'off',
        '@typescript-eslint/no-throw-literal': 'error',
        'no-underscore-dangle': 'off',
        'import/export': 'off',
        'import/extensions': 'off',
        // Read more on why we should ban default exports: https://blog.neufund.org/why-we-have-banned-default-exports-and-you-should-do-the-same-d51fdc2cf2ad
        'import/prefer-default-export': 'off',
        'no-plusplus': ['error', { allowForLoopAfterthoughts: true }],
        'no-await-in-loop': 'off',
        // We have to disable these rules for new TS projects: https://typescript-eslint.io/rules/no-redeclare/
        'no-redeclare': 'off',
        '@typescript-eslint/no-redeclare': 'off',
        '@typescript-eslint/return-await': 'off',

        // TODO: Enforce these rules in the future
        'no-unsafe-optional-chaining': 'warn',
        '@typescript-eslint/naming-convention': 'off',
        '@typescript-eslint/no-explicit-any': 'off',
        'prefer-destructuring': 'warn',
        'class-methods-use-this': 'off',
        'no-restricted-syntax': 'off',
    },
};
