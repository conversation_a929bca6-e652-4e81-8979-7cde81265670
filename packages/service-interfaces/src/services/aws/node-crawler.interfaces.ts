import type { AxiosRequestConfig, AxiosResponse } from 'axios';

export interface NodeCrawlerEvent {
    url: string;
    headers: AxiosRequestConfig['headers'];
    body?: any;
    method?: AxiosRequestConfig['method'];
    timeout?: number;
}

export interface NodeCrawlerResponse<T = any> extends Pick<AxiosResponse<T>, 'data' | 'status' | 'statusText' | 'headers'> {
    config: Pick<AxiosResponse['config'], 'timeout' | 'baseURL' | 'params' | 'data' | 'method'>;
}
