export enum PuppeteerLambdaEventName {
    INSIGHTS_PDF = 'insights-pdf',
}

// Insights PDF
interface PuppeteerInsightsPdfPayload {
    jwtToken: string;
    baseUrl: string;
    callBackUrl: string;
    pdfParams: string;
    awsBucketName: string;
    awsBucketPath: string;
}

interface PuppeteerPayload extends Record<PuppeteerLambdaEventName, any> {
    [PuppeteerLambdaEventName.INSIGHTS_PDF]: PuppeteerInsightsPdfPayload;
}

export interface PuppeteerLambdaEvent<T extends PuppeteerLambdaEventName = PuppeteerLambdaEventName> {
    eventType: T;
    payload: PuppeteerPayload[T];
}
