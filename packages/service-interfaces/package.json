{"dependencies": {"tslib": "^2.8.1"}, "devDependencies": {"@malou-io/package-config": "workspace:*", "@trivago/prettier-plugin-sort-imports": "^5.2.0", "@types/eslint": "^8.48.0", "@types/node": "^16.3.1", "@typescript-eslint/eslint-plugin": "^7.0.0", "@typescript-eslint/parser": "^7.0.0", "axios": "^1.12.2", "eslint": "^8.48.0", "eslint-config-airbnb-base": "^15.0.0", "eslint-config-airbnb-typescript": "^18.0.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-import": "^2.28.1", "prettier": "^3.5.3", "typescript": "^5.3.3"}, "main": "./lib/index.js", "name": "@malou-io/package-service-interfaces", "private": true, "scripts": {"build": "tsc", "build-clean": "rm -rf ./lib && rm -rf .turbo && rm -f tsconfig.tsbuildinfo", "build-development": "tsc", "build-production": "tsc", "build-staging": "tsc", "format": "prettier --write \"**/*.{ts,js,md}\"", "format:check": "prettier \"**/*.{ts,js,md}\" --check", "lint": "eslint \"**/*.ts\"", "lint-fix": "eslint \"**/*.ts\" --fix", "lint-staged": "lint-staged --no-stash", "preinstall": "npx only-allow pnpm", "watch-build": "tsc -w"}, "types": "./lib/index.d.ts", "version": "0.0.0"}