import { z } from 'zod';

export const adminSearchOrganizationsQueryValidator = z.object({
    text: z.string().optional(),
    limit: z.string().transform(Number).optional(),
    offset: z.string().transform(Number).optional(),
});

export type AdminSearchOrganizationsQueryDto = z.infer<typeof adminSearchOrganizationsQueryValidator>;

export interface AdminSearchOrganizationsUserDto {
    _id: string;
    name?: string;
    lastname?: string;
    email: string;
    organizationIds?: string[];
}

export interface AdminSearchOrganizationsRestaurantDto {
    _id: string;
    name: string;
    active: boolean;
}

export interface AdminSearchOrganizationsOrganizationDto {
    _id: string;
    name: string;
    verifiedEmailsForCampaigns: string[];
    createdAt: Date;
    updatedAt: Date;
    users: AdminSearchOrganizationsUserDto[];
    restaurants: AdminSearchOrganizationsRestaurantDto[];
    subscriptionsProviderId: string;
}

export interface AdminSearchOrganizationsResponseDto {
    data: AdminSearchOrganizationsOrganizationDto[];
    total: number;
}
