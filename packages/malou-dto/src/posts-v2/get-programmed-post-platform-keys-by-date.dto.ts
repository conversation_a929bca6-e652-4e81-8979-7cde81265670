import { z } from 'zod';

import { PlatformKey, PostSource } from '@malou-io/package-utils';

import { restaurantIdParamsTransformValidator } from '../common';

export const getProgrammedPostPlatformKeysByDateParamsValidator = restaurantIdParamsTransformValidator;
export type GetProgrammedPostPlatformKeysByDateParamsDto = z.infer<typeof getProgrammedPostPlatformKeysByDateParamsValidator>;

export const getProgrammedPostPlatformKeysByDateQueryValidator = z
    .object({
        timezone: z.string().optional(),
        source: z.nativeEnum(PostSource),
        is_story: z.boolean().optional(),
    })
    .transform((data) => ({
        timezone: data.timezone,
        source: data.source,
        isStory: data.is_story,
    }));
export type GetProgrammedPostPlatformKeysByDateQueryDto = z.infer<typeof getProgrammedPostPlatformKeysByDateQueryValidator>;

export type ProgrammedPostPlatformKeysByDateDto = {
    date: string;
    platformKeys: PlatformKey[];
}[];
