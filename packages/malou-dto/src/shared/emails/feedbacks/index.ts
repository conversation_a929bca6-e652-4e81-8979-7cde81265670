import { BaseEmailProps } from '../common';

export interface NewFeedbackMessageEmailProps extends BaseEmailProps {
    postLink: string;
    username: string;
    feedbackText: string;
    postStatus: string;
    plannedPublicationDate?: Date;
    fullPlatformKeys: string;
    restaurantName: string;
    postText?: string;
    postImgUrl?: string;
    isStory: boolean;
}

export interface ClosedFeedbackEmailProps extends BaseEmailProps {
    postLink: string;
    username: string;
    postStatus: string;
    plannedPublicationDate?: Date;
    fullPlatformKeys: string;
    restaurantName: string;
    postText?: string;
    postImgUrl?: string;
}

export interface OpenedFeedbackEmailProps extends BaseEmailProps {
    postLink: string;
    username: string;
    postStatus: string;
    plannedPublicationDate?: Date;
    fullPlatformKeys: string;
    restaurantName: string;
    postText?: string;
    postImgUrl?: string;
}
