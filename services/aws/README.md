# AWS CDK / STACK

This service handles the deployment of infrastructure and applicative code to AWS using [AWS CDK](https://aws.amazon.com/fr/cdk/).

## Structure

This project acts like a monorepo on its own, even if services, lambdas, etc. are linked to the rest of the project using PNPM workspaces and TS references, enabling them to share packages, node dependencies, ...

The preferred structure [as detailed here](https://repost.aws/questions/QUsqAM5JgVSYW-ZOOLMwhWYw/how-to-organize-a-mono-repo-in-cdk-to-have-a-mono-stack-but-also-individual-services-can-still-be-quickly-deployed-individually) is to have a single CDK app (located in the bin folder) deploying multiple stacks, with each service having its own stack.

```
aws/
  ├── bin/
  │   └── app.ts (main CDK app entry point)
  ├── lib/
  │   ├── parent-stack.ts (parent stack that includes all services)
  │   └── shared-resources.ts (shared resources across services)
  ├── services/
  │   ├── service-a/
  │   │   ├── service-a-stack.ts
  │   │   └── bin/
  │   │       └── service-a.ts (entry point for individual deployment)
  │   └── service-b/
  │       ├── service-b-stack.ts
  │       └── bin/
  │           └── service-b.ts (entry point for individual deployment)
  └── package.json
```

## Installation

### Softwares

If this is not done yet:

- Install **[NVM](https://github.com/nvm-sh/nvm?tab=readme-ov-file#installing-and-updating)**: allows you to quickly install and use different versions of node via the command line. You can find another guide [here](https://www.freecodecamp.org/news/node-version-manager-nvm-install-guide#howtoinstallnvmonlinuxandmac)
- Install **[Docker Desktop](https://www.docker.com/products/docker-desktop/)**

### Commands

- Run `nvm use`. It will automatically detect the node version required to run our API and WEB apps in the `.nvmrc` file at the monorepo's root.
- Run `nvm install 22.15.0` (at this time of writing)
- Run `npm install -g pnpm@10.6.5` to install PNPM (our package manager)
- Run `pnpm i` at the monorepo's root (malou-app) to install the dependencies of **all** workspaces, including the root project.
- In `./aws` folder, create a `.env.default` file and ask a member of the team to give you the environment variables values

### Repo structure

As said in the intro, this folder acts like a monorepo on its own. `services/aws` as well as all other services in `services/aws/services/*` are linked to the monorepo in `pnpm-workspaces.yaml`. This means that all node dependencies are shared and all services are accessible in the whole monorepo.

Concerning `build`, `format` and `lint` commands, we could have added `build`, `format` and `lint` commands in all services but that forces to manage several lint configs, devDeps, ... and have duplicates.

Instead all these commands are defined and handled by the "monorepo" `services/aws` that will handle all formatting, build and lint checks for all aws services.

Note that build command is only present for check purposes as AWS CDK will handle building and bundling on its own before pushing code to AWS.

### Other tips

You can find other instructions in the [Monorepo README](../../README.md). More info on how to [install the repo](../../README.md#start-malou-app). Our [git conventions](../../README.md#git--github) or how to [setup your VSCode](../../README.md#vscode) to use Prettier or ESLint for example.

## Useful Commands

- `NODE_ENV=... pnpm run deploy` to deploy the stack attached to the NODE_ENV environment
- `pnpm run diff` to compare deployed stack with current state
- `pnpm run synth` to emit the synthesized CloudFormation template
