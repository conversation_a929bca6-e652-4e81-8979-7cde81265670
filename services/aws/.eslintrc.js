/* eslint-env node */
module.exports = {
    ...require('@malou-io/package-config/eslint.config'),
    parserOptions: {
        tsconfigRootDir: __dirname,
        // Define extra files/folders that need to be type checked by ESLint
        // but don't need to be compiled to JS
        project: './tsconfig.eslint.json',
    },
    rules: {
        ...require('@malou-io/package-config/eslint.config').rules,
        'no-new': 'off',
        'import/no-extraneous-dependencies': [
            'error',
            {
                devDependencies: ['**/bin/*.ts', '**/*.stack.ts'],
            },
        ],
    },
};
