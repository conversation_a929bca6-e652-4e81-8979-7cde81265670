#!/usr/bin/env node
import 'source-map-support/register';

import { App } from 'aws-cdk-lib';

import { config } from ':config';
import { AiDescriptionGeneratorStack } from ':services/ai-description-generator/ai-description-generator.stack';
import { NodeCrawlerStack } from ':services/node-crawler/node-crawler.stack';
import { PuppeteerStack } from ':services/puppeteer/puppeteer.stack';
import { SlackTechBotStack } from ':services/slack-tech-bot/slack-tech-bot.stack';
import { StoreLocatorContentGeneratorStack } from ':services/store-locator-content-generator/store-locator-content-generator.stack';

const COMMON_STACK_PROPS = {
    env: { region: config.awsRegion },
};

const app = new App();
new StoreLocatorContentGeneratorStack(app, `StoreLocatorContentGenerator${config.appSuffix}`, COMMON_STACK_PROPS);
new AiDescriptionGeneratorStack(app, `AiDescriptionGenerator${config.appSuffix}`, COMMON_STACK_PROPS);
new NodeCrawlerStack(app, `NodeCrawler${config.appSuffix}`, COMMON_STACK_PROPS);
new PuppeteerStack(app, `Puppeteer${config.appSuffix}`, COMMON_STACK_PROPS);

if (config.env === 'production') {
    new SlackTechBotStack(app, `SlackTechBot${config.appSuffix}`, COMMON_STACK_PROPS);
}
