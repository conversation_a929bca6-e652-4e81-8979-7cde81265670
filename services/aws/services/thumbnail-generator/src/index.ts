import type { APIGatewayProxyHandler } from 'aws-lambda';

import { ThumbnailGenerator } from './core/thumbnail-generator';
import { doesFileExist, extractParams, wipeTmpDirectory } from './core/utils';
import { VideoDownloader } from './core/video-downloader';
import { BucketStorage } from './plugins/storage';

export const handler: APIGatewayProxyHandler = async (event, _context) => {
    try {
        await wipeTmpDirectory();

        const { videoFileName, triggerBucketName } = extractParams(event);
        const storage = new BucketStorage();
        const videoDownloader = new VideoDownloader();
        const thumbnailGenerator = new ThumbnailGenerator();

        const tmpPath = await videoDownloader.downloadVideoReceived(videoFileName, triggerBucketName);

        const tmpThumbnailPath = await thumbnailGenerator.createImageFromVideo(tmpPath);

        if (doesFileExist(tmpThumbnailPath)) {
            const { resultObjectKey } = await storage.uploadFileToS3(
                tmpThumbnailPath,
                videoFileName.replace(/\.[^.]+$/, '.jpg'),
                triggerBucketName
            );

            return {
                statusCode: 200,
                body: JSON.stringify({ message: 'Successfully created thumbnail', resultObjectKey }),
            };
        }

        return {
            statusCode: 400,
            body: JSON.stringify({
                message: 'Error : File does not exist, thumbnail might not have been created',
                tmpPath,
            }),
        };
    } catch (error) {
        return {
            statusCode: 500,
            body: JSON.stringify({ message: 'Internal Server Error', error }),
        };
    }
};
