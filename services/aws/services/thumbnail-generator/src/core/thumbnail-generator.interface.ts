import type { StackProps } from 'aws-cdk-lib';

export interface QueueConfig {
    name: string;
    visibilityTimeout: number;
    id: string;
}

export interface LambdaConfig {
    id: string;
    ffmpegLayerId: string;
}

export interface BucketConfig {
    id: string;
    arn: string;
}

export interface ThumbnailGeneratorServiceStackProps extends StackProps {
    queue: QueueConfig;
    lambda: LambdaConfig;
    bucket: BucketConfig;
}
