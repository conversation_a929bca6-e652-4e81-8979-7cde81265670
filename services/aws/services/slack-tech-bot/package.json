{"author": "", "dependencies": {"@slack/bolt": "^4.2.1", "tslib": "^2.8.1", "typed-inject": "^5.0.0", "zod": "^3.25.67"}, "description": "Service AWS: slack internal bot", "devDependencies": {"@types/aws-lambda": "^8.10.149", "aws-cdk-lib": "^2.194.0", "constructs": "^10.4.2"}, "license": "MIT", "main": "./src/lambda.ts", "name": "@malou-io/aws-service-slack-tech-bot", "private": true, "scripts": {"build-clean": "rm -rf ./dist && rm -rf .turbo && rm -f tsconfig.tsbuildinfo"}, "version": "1.0.0"}