import { App, Aws<PERSON><PERSON>bda<PERSON><PERSON>ei<PERSON> } from '@slack/bolt';
import assert from 'node:assert';

export class Slack<PERSON>olt<PERSON>rovider {
    private _receiver: AwsLambdaReceiver | undefined;
    private _app: App;

    constructor() {
        assert(process.env.SLACK_TECH_BOT_SIGNING_SECRET);
        this._receiver = new AwsLambdaReceiver({
            signingSecret: process.env.SLACK_TECH_BOT_SIGNING_SECRET,
        });
        assert(process.env.SLACK_TECH_BOT_TOKEN);
        this._app = new App({
            token: process.env.SLACK_TECH_BOT_TOKEN,
            receiver: this._receiver,
        });
    }

    getReceiver(): Aws<PERSON><PERSON>bdaReceiver {
        if (!this._receiver) {
            throw new Error('Receiver is not initialized, surely because the app is running locally');
        }
        return this._receiver;
    }

    getApp(): App {
        return this._app;
    }
}
