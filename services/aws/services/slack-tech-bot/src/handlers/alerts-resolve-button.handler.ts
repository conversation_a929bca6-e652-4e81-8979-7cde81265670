import { App } from '@slack/bolt';
import { z } from 'zod';

export const registerAlertsResolveButtonHandler = (app: App) => {
    app.action('alerts_resolve_button', async ({ body, ack, client, logger }) => {
        await ack();

        try {
            if (body.type !== 'block_actions') {
                throw new Error('Not a block action');
            }

            const channel = body.channel?.id;
            const messageTs = body.message?.ts;

            if (!channel || !messageTs) {
                throw new Error('Missing channel or message timestamp');
            }

            // 1. Get the original message text
            let newBlockText = '*[Could not retrieve original message]*';
            const blocks = body.message?.blocks;
            if (!isMessageBlocks(blocks)) {
                newBlockText = `📌 *Original Message:*${body.message?.text ?? newBlockText}`;
            } else {
                const mrxDwnSectionBlock = blocks?.find((b) => isMrkDwnSectionBlock(b));
                newBlockText = `📌 *Original Message:*\n ${mrxDwnSectionBlock?.text.text ?? newBlockText}`;
            }

            const newBlocks = [
                {
                    type: 'section',
                    text: {
                        type: 'mrkdwn',
                        text: newBlockText.slice(0, 3000), // 3000 is a slack limit
                    },
                },
                ...(newBlockText.length > 3000
                    ? [
                          {
                              type: 'section',
                              text: {
                                  type: 'mrkdwn',
                                  text: newBlockText.slice(3000), // 3000 is a slack limit
                              },
                          },
                      ]
                    : []),
            ];

            // 2. Add it to the thread
            await client.chat.postMessage({
                channel,
                thread_ts: messageTs,
                text: newBlockText.slice(0, 3000), // 3000 is a slack limit
                blocks: newBlocks,
            });

            // 3. Format resolved message
            const user = body.user.username || body.user.name || body.user.id;
            const resolvedDate = new Date().toLocaleString('fr-FR');
            const resolvedText = `✅ Resolved at *${resolvedDate}* by ${user}`;

            // 4. Update the original message
            await client.chat.update({
                channel,
                ts: messageTs,
                text: resolvedText,
            });
        } catch (error) {
            logger.error('Failed to handle resolve button:', error);
        }
    });
};

const messageBlocksValidator = z.array(z.object({}));
type MessageBlocks = z.infer<typeof messageBlocksValidator>;

function isMessageBlocks(value: unknown): value is MessageBlocks {
    return messageBlocksValidator.safeParse(value).success;
}

const mrkDwnSectionBlockValidator = z.object({
    type: z.literal('section'),
    text: z.object({
        type: z.literal('mrkdwn'),
        text: z.string(),
    }),
});
type MrkDwnSectionBlock = z.infer<typeof mrkDwnSectionBlockValidator>;

function isMrkDwnSectionBlock(value: unknown): value is MrkDwnSectionBlock {
    return mrkDwnSectionBlockValidator.safeParse(value).success;
}
