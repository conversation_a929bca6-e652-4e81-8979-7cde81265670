import { appInjector } from './di';
import { registerAlertsResolveButtonHandler } from './handlers/alerts-resolve-button.handler';

const slackBoltProvider = appInjector.resolve('SLACK_BOLT_PROVIDER');
const app = slackBoltProvider.getApp();

registerAlertsResolveButtonHandler(app);

const receiver = slackBoltProvider.getReceiver();
export const handler = async (event: any, context: any, callback: any) => {
    const slackHandler = await receiver.start();
    return slackHandler(event, context, callback);
};
