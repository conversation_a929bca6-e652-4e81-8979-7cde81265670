import { CfnOutput, Duration, Stack, StackProps } from 'aws-cdk-lib';
import { FunctionUrlAuthType, Runtime } from 'aws-cdk-lib/aws-lambda';
import { NodejsFunction } from 'aws-cdk-lib/aws-lambda-nodejs';
import { Construct } from 'constructs';
import path from 'path';

import { config } from ':config';

export class SlackTechBotStack extends Stack {
    constructor(scope: Construct, id: string, props?: StackProps) {
        super(scope, id, props);

        const slackTechBotLambda = new NodejsFunction(this, `SlackTechBot${config.appSuffix}`, {
            entry: path.join(__dirname, 'src/lambda.ts'),
            handler: 'handler',
            functionName: `slackTechBot${config.appSuffix}`,
            timeout: Duration.seconds(60),
            runtime: Runtime.NODEJS_22_X,
            environment: {
                SLACK_TECH_BOT_TOKEN: config.slackTechBot.token,
                SLACK_TECH_BOT_SIGNING_SECRET: config.slackTechBot.signingSecret,
            },
        });

        slackTechBotLambda.addFunctionUrl({
            authType: FunctionUrlAuthType.NONE,
        });

        // To get names for output
        new CfnOutput(this, `SlackTechBotFunctionName`, {
            value: slackTechBotLambda.functionName,
            description: 'Slack tech bot lambda function',
        });
    }
}
