import { CfnOutput, Duration, Stack, StackProps } from 'aws-cdk-lib';
import { Runtime } from 'aws-cdk-lib/aws-lambda';
import { NodejsFunction } from 'aws-cdk-lib/aws-lambda-nodejs';
import { Construct } from 'constructs';
import path from 'path';

import { config } from ':config';

export class NodeCrawlerStack extends Stack {
    private readonly _NODE_CRAWLER_COUNT = 10;

    constructor(scope: Construct, id: string, props?: StackProps) {
        super(scope, id, props);

        Array.from({ length: this._NODE_CRAWLER_COUNT }).forEach((_, index) => {
            const nodeCrawlerLambda = new NodejsFunction(this, `NodeCrawler${index}${config.appSuffix}`, {
                entry: path.join(__dirname, 'src/lambda.ts'),
                handler: 'handler',
                functionName: `nodeCrawler${index}${config.appSuffix}`,
                timeout: Duration.seconds(60),
                runtime: Runtime.NODEJS_22_X,
            });

            // To get names for output
            new CfnOutput(this, `NodeCrawler${index}FunctionName`, {
                value: nodeCrawlerLambda.functionName,
                description: `Node crawler ${index} lambda function`,
            });
        });
    }
}
