import type { Context } from 'aws-lambda';
import axios, { AxiosError, AxiosResponse } from 'axios';
import https from 'https';
import qs from 'qs';

import type { NodeCrawlerEvent, NodeCrawlerResponse } from '@malou-io/package-service-interfaces';

export const handler = async <T>(event: NodeCrawlerEvent, _context: Context): Promise<NodeCrawlerResponse<T> | undefined> => {
    const ONE_MINUTE = 60 * 1000;
    const { method, url, headers, body, timeout = ONE_MINUTE } = event;

    console.log('event :>> ', event);

    if (method !== 'POST') {
        try {
            const response = await axios.get<T>(url, {
                headers,
                timeout,
            });

            console.log('response :>> ', response);
            return formatAxiosResponse(response);
        } catch (err) {
            const error: AxiosError<T> = err as AxiosError<T>;

            console.error('Error while fetching data :>> ', error);
            return formatAxiosResponse(error.response!);
        }
    }

    let cleanBody = body;
    if (headers) {
        const contentTypeKey = Object.keys(headers).find((key) => key.toLowerCase() === 'content-type');
        if (contentTypeKey && headers[contentTypeKey]?.match(/x-www-form-urlencoded/)) {
            cleanBody = qs.stringify(body);
        }
    }

    const httpsAgent = new https.Agent({
        ciphers: 'ECDHE-ECDSA-AES256-GCM-SHA384:ECDHE-RSA-AES256-GCM-SHA384',
    });

    try {
        const response = await axios.post<T>(url, cleanBody, {
            headers,
            timeout,
            httpsAgent,
        });

        console.log('response :>> ', response);
        return formatAxiosResponse(response);
    } catch (err) {
        const error: AxiosError<T> = err as AxiosError<T>;

        console.error('Error while fetching data :>> ', error);
        return formatAxiosResponse(error.response!);
    }
};

const formatAxiosResponse = <T>(response: AxiosResponse<T>): NodeCrawlerResponse<T> => ({
    status: response.status,
    statusText: response.statusText,
    data: response.data,
    headers: response.headers,
    config: {
        ...(response.config.timeout && { timeout: response.config.timeout }),
        ...(response.config.baseURL && { baseURL: response.config.baseURL }),
        ...(response.config.params && { params: response.config.params }),
        ...(response.config.data && { data: response.config.data }),
        ...(response.config.method && { method: response.config.method }),
    },
});
