{"author": "", "dependencies": {"@malou-io/package-service-interfaces": "workspace:*", "axios": "^1.12.2", "qs": "^6.12.0", "tslib": "^2.8.1"}, "description": "Service AWS: node crawlers", "devDependencies": {"@types/aws-lambda": "^8.10.149", "@types/jest": "^29.5.14", "@types/qs": "^6.9.14", "aws-cdk-lib": "^2.194.0", "constructs": "^10.4.2", "esbuild": "^0.25.4", "jest": "^29.7.0"}, "license": "MIT", "main": "./src/lambda.ts", "name": "@malou-io/aws-service-node-crawlers", "private": true, "scripts": {"build-clean": "rm -rf ./dist && rm -rf .turbo && rm -f tsconfig.tsbuildinfo"}, "version": "1.0.0"}