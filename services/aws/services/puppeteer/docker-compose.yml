services:
    puppeteer-lambda:
        build:
            context: ../../../..
            dockerfile: services/aws/services/puppeteer/Dockerfile
        container_name: puppeteer-service
        ports:
            - '9000:8080' # Lambda Runtime Interface Emulator port
        environment:
            - AWS_REGION=eu-west-3
            - authorization=4iwPTp3u3Q&r$82z
        volumes:
            - .:/var/task # Mount current directory for development
        stdin_open: true # Keep STDIN open
        tty: true # Allocate a pseudo-TTY
        networks:
            - lambda-network

networks:
    lambda-network:
