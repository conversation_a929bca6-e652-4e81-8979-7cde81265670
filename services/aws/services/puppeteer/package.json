{"dependencies": {"@malou-io/package-service-interfaces": "workspace:*", "aws-sdk": "^2.1296.0", "puppeteer-core": "^24.10.1", "puppeteer-extra": "^3.3.6", "puppeteer-extra-plugin-stealth": "^2.11.2", "tslib": "^2.8.1"}, "description": "Service AWS: <PERSON><PERSON><PERSON><PERSON>", "devDependencies": {"@types/aws-lambda": "^8.10.149", "aws-cdk-lib": "^2.194.0", "constructs": "^10.4.2", "esbuild": "^0.25.4", "typescript": "^5.7.3"}, "license": "MIT", "main": "dist/index.js", "name": "@malou-io/aws-service-puppeteer", "private": true, "scripts": {"build": "tsc", "build-clean": "rm -rf ./dist && rm -rf .turbo && rm -f tsconfig.tsbuildinfo", "invoke-lambda": "ts-node -T src/scripts/invoke-lambda.ts", "local:up": "chmod +x ./local-up.sh && ./local-up.sh"}, "version": "1.0.0"}