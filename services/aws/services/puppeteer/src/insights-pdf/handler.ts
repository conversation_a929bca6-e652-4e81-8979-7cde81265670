import AWS from 'aws-sdk';
import pp from 'puppeteer-core';
import { addExtra } from 'puppeteer-extra';
import StealthPlugin from 'puppeteer-extra-plugin-stealth';

import { PuppeteerLambdaEvent, PuppeteerLambdaEventName } from '@malou-io/package-service-interfaces';

const s3 = new AWS.S3({
    region: 'eu-west-3',
});

const MALOU_UNNECESSARY_EXTERNAL_SCRIPT_DOMAINS = [
    'jimo',
    'heapanalytics',
    'sentry',
    'frontapp',
    'googletagmanager',
    'google-analytics',
    'iframeResizer.contentWindow',
    'customer.io',
];

export async function handler(event: PuppeteerLambdaEvent<PuppeteerLambdaEventName.INSIGHTS_PDF>): Promise<any> {
    try {
        // Set environment variable to suppress DBus errors
        process.env.LIBC_FATAL_STDERR_ = '1';

        const puppeteer = addExtra(pp);
        puppeteer.use(StealthPlugin());

        const browser = await puppeteer.launch({
            headless: true,
            dumpio: true,
            timeout: 60000,
            executablePath: '/opt/chrome/linux-137.0.7151.70/chrome-linux64/chrome',
            args: [
                '--headless=new',
                '--no-sandbox',
                '--disable-gpu',
                '--disable-dev-shm-usage',
                '--disable-dev-tools',
                '--no-zygote',
                '--remote-debugging-port=9222',
            ],
        });

        const a4Ratio = 297 / 210;
        const width = 1025;
        const height = Math.round(width * a4Ratio);

        const { jwtToken, baseUrl, callBackUrl, pdfParams, awsBucketName, awsBucketPath } = event.payload;

        const page = await browser.newPage();
        await page.setRequestInterception(true);
        page.setDefaultNavigationTimeout(60000);
        page.setDefaultTimeout(60000);
        await page.setViewport({ width, height, deviceScaleFactor: 3 });
        await page.emulateMediaType('screen');

        const shouldBlockRequest = (req: any): boolean =>
            MALOU_UNNECESSARY_EXTERNAL_SCRIPT_DOMAINS.some((domain) => req.url().toLowerCase().includes(domain));

        page.on('request', (req: any) => {
            if (shouldBlockRequest(req)) {
                req.abort();
            } else {
                req.continue();
            }
        });

        const notFoundUrl = `${baseUrl}/404`;
        await page.goto(notFoundUrl, { timeout: 6000, waitUntil: 'networkidle2' });

        await page.evaluate(
            ([jwtTokenParam, queryParams]: [any, any]) => {
                localStorage.setItem('jwtToken', jwtTokenParam);
                localStorage.setItem('downloadInsightsParams', queryParams);
            },
            [jwtToken, pdfParams]
        );

        const insightsUrl = `${baseUrl}/${callBackUrl}`;

        await page.goto(insightsUrl, { timeout: 60000, waitUntil: 'networkidle2' });

        await new Promise((resolve) => {
            setTimeout(resolve, 3500);
        });

        const buffer = await page.pdf({
            width,
            height,
            printBackground: true,
            margin: {
                top: '1cm',
            },
        });

        const s3Response = await s3
            .upload({
                Bucket: awsBucketName,
                Key: awsBucketPath,
                Body: buffer,
                ContentType: 'application/pdf',
                ACL: 'public-read',
            })
            .promise();

        await page.close();
        await browser.close();

        return s3Response.Location;
    } catch (error) {
        console.error('Error in insights-pdf handler:', error);
        throw error;
    }
}
