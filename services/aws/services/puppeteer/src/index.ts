import type { PuppeteerLambdaEvent } from '@malou-io/package-service-interfaces';

import { handler as insightsPdfHandler } from './insights-pdf/handler';

export const handler = async (event: PuppeteerLambdaEvent) => {
    switch (event.eventType) {
        case 'insights-pdf':
            return insightsPdfHandler(event);
        default:
            console.error(`Unsupported event type: ${event.eventType}`, JSON.stringify(event));
            throw new Error(`Unsupported event type: ${event.eventType}`);
    }
};
