{
    "extends": "../../../../tsconfig.options.json",
    "compilerOptions": {
        "rootDir": "./src",
        "outDir": "./dist",
        "baseUrl": ".",

        // Output settings
        "target": "ES2022",

        // Additional checks
        "noUnusedLocals": true,
        "noUnusedParameters": true,
        "noImplicitReturns": true,
        "noFallthroughCasesInSwitch": true,

        // AWS Lambda compatibility
        "lib": ["ES2022", "DOM"]
    },
    "include": ["src"],
    "references": [{ "path": "../../../../packages/service-interfaces" }]
}
