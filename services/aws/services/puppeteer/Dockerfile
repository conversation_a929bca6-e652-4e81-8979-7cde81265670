# Lamb<PERSON> always looks for code in /var/task
ARG APP_ROOT="/var/task"
ARG APP_NAME="@malou-io/aws-service-puppeteer"

################## BUILD STAGES ##################
FROM public.ecr.aws/lambda/nodejs:22-x86_64 AS aws-lambda-node

FROM aws-lambda-node AS base
ARG APP_ROOT
ARG APP_NAME
RUN npm install pnpm@10.6.5 turbo@1.13.4 --global \
    && pnpm config set store-dir ~/.pnpm-store

FROM base AS pruner
WORKDIR ${APP_ROOT}
COPY . .
RUN turbo prune --scope=${APP_NAME} --docker
COPY tsconfig.json tsconfig.options.json ${APP_ROOT}/out/full/

FROM base AS builder
WORKDIR ${APP_ROOT}

# Copy lockfile and package.json's of isolated subworkspace
COPY --from=pruner ${APP_ROOT}/out/pnpm-lock.yaml ./pnpm-lock.yaml
COPY --from=pruner ${APP_ROOT}/out/pnpm-workspace.yaml ./pnpm-workspace.yaml
COPY --from=pruner ${APP_ROOT}/out/json/ .

# First install the dependencies (as they change less often)
RUN --mount=type=cache,id=pnpm,target=~/.pnpm-store pnpm install --prod=false --frozen-lockfile

# Copy and build isolated subworkspace
COPY --from=pruner ${APP_ROOT}/out/full/ .
RUN pnpm exec turbo build --filter=${APP_NAME}

# Clean up dev dependencies and source code
RUN --mount=type=cache,id=pnpm,target=~/.pnpm-store pnpm prune --prod --no-optional
RUN rm -rf ./**/*/src

################## SERVICE-SPECIFIC LAYERS ##################
# Create a base image with Chrome dependencies and Chrome installed
FROM aws-lambda-node AS chrome-base
RUN dnf install -y atk cups-libs gtk3 libXcomposite alsa-lib \
    libXcursor libXdamage libXext libXi libXrandr libXScrnSaver \
    libXtst pango at-spi2-atk libXt xorg-x11-server-Xvfb \
    xorg-x11-xauth dbus-glib dbus-glib-devel nss mesa-libgbm
RUN npx --yes @puppeteer/browsers install --path /opt chrome@137.0.7151.70

FROM chrome-base AS final-lambda
ARG APP_ROOT
WORKDIR ${APP_ROOT}
COPY --from=builder ${APP_ROOT} .
CMD ["services/aws/services/puppeteer/dist/index.handler"]