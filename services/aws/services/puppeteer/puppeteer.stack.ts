import { CfnOutput, Duration, Size, Stack, StackProps } from 'aws-cdk-lib';
import { Effect, PolicyStatement } from 'aws-cdk-lib/aws-iam';
import { DockerImageCode, DockerImageFunction } from 'aws-cdk-lib/aws-lambda';
import { Construct } from 'constructs';
import path from 'path';

import { config } from ':config';

export class PuppeteerStack extends Stack {
    constructor(scope: Construct, id: string, props?: StackProps) {
        super(scope, id, props);

        // Set docker context to monorepo root
        const dockerContext = path.join(__dirname, '../../../..');
        const puppeteerLambda = new DockerImageFunction(this, `PuppeteerLambda${config.appSuffix}`, {
            code: DockerImageCode.fromImageAsset(dockerContext, {
                file: 'services/aws/services/puppeteer/Dockerfile', // Relative to docker context
            }),

            functionName: `puppeteerLambda${config.appSuffix}`,
            timeout: Duration.seconds(90),
            memorySize: 4096,
            ephemeralStorageSize: Size.gibibytes(4),
        });

        // Add S3 permissions for uploading PDFs to specific buckets
        puppeteerLambda.addToRolePolicy(
            new PolicyStatement({
                effect: Effect.ALLOW,
                actions: ['s3:PutObject', 's3:PutObjectAcl'],
                resources: ['arn:aws:s3:::malou-dev/*', 'arn:aws:s3:::malou-staging/*', 'arn:aws:s3:::malou-production/*'],
            })
        );

        // To get names for output
        new CfnOutput(this, 'PuppeteerLambdaFunctionName', {
            value: puppeteerLambda.functionName,
            description: 'Puppeteer lambda function',
        });
    }
}
