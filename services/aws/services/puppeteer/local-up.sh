#!/bin/bash
set -e

# delete the test.pdf file if it exists
if [ -f test.pdf ]; then
    rm test.pdf
fi

docker compose down && \
COMPOSE_BAKE=true docker compose up -d --remove-orphans --build && \
docker exec -it puppeteer-service bash

# Once connected to the container, run the following command to test the service:
# node /dist/scripts/invoke-lambda.js
# NB: edit the src/scripts/invoke-lambda.js file to change the call payload