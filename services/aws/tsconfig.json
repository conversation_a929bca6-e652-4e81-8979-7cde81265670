{
    // Read: https://moonrepo.dev/docs/guides/javascript/typescript-project-refs
    "extends": "../../tsconfig.options.json",
    "compilerOptions": {
        "rootDir": ".",
        "paths": {
            ":config": ["./config.ts"],
            ":services/*": ["./services/*"]
        }
    },
    "references": [
        {
            "path": "./services/node-crawler"
        },
        {
            "path": "./services/slack-tech-bot"
        },
        {
            "path": "./services/puppeteer"
        },
        {
            "path": "./services/thumbnail-generator"
        }
    ],
    "exclude": ["node_modules", "cdk.out"]
}
