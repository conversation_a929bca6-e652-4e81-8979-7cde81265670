# Malou Monorepo

Welcome to the starter pack to get you ready to play with <PERSON><PERSON>'s ecosystem

## Table of content

- [Softwares to install](#softwares-to-install)
- [Clone Malou monorepo](#clone-malou-monorepo)
- [Start Malou App](#start-malou-app)
    - [Install dependencies](#install-dependencies)
        - [Go into the repository](#go-into-the-repository)
        - [Download node.js using nvm](#download-nodejs-using-nvm)
        - [Download PNPM (a faster alternative to NPM) using NPM](#download-pnpm-a-faster-alternative-to-npm-using-npm)
        - [Install dependencies](#install-dependencies-1)
    - [Start the App](#start-the-app)
        - [Get environments variables](#get-environments-variables)
        - [Start Docker Desktop](#start-docker-desktop)
        - [Run the app](#run-the-app)
- [Feed the DB](#feed-the-db)
    - [Dump and restore development DB to your local DB](#dump-and-restore-development-db-to-your-local-db)
    - [Connect to DBs](#connect-to-dbs)
- [Git / Github](#git--github)
- [Feature flag](#feature-flag)
    - [Feature Flag Categories](#feature-flag-categories)
    - [Naming Convention](#naming-convention)
    - [Best Practices](#best-practices)
- [Useful tips](#useful-tips)
    - [Setup Husky (Git hooks)](#setup-husky-git-hooks)
    - [VSCode](#vscode)
    - [Refresh your .env variables](#refresh-your-env-variables)
    - [Useful commands](#useful-commands)
    - [Useful infos](#useful-infos)
- [Contributing](#contributing)

## Softwares to install

Here are some softwares you need to run the Malou ecosystem, or help you in your day to day life as a software engineer. While these softwares are downloading, feel free to start the next section and clone the repository to your local machine.

- Install **[NVM](https://github.com/nvm-sh/nvm?tab=readme-ov-file#installing-and-updating)**: allows you to quickly install and use different versions of node via the command line. You can find another guide [here](https://www.freecodecamp.org/news/node-version-manager-nvm-install-guide#howtoinstallnvmonlinuxandmac)
- Install **[Docker Desktop](https://www.docker.com/products/docker-desktop/)**
- Install your favorite IDE, we recommend to download and use **[VSCode](https://code.visualstudio.com/download)** if you don't know what to choose. A lot of helpers for VSCode are included in this repo
- Download **[MongoDB Compass](https://www.mongodb.com/docs/compass/current/install/)** to connect and access our databases
- **(recommended)** You can download **[Postman](https://www.postman.com/downloads/)**: an API platform to build or use APIs
- **(recommended)** For MAC users, you can download **[Brew](https://brew.sh/)**: a package manager for MacOS

## Clone Malou monorepo

1. Before cloning the repository, you have to [generate a SSH key on your machine](https://docs.github.com/en/authentication/connecting-to-github-with-ssh/generating-a-new-ssh-key-and-adding-it-to-the-ssh-agent). Once done, [add it to your Github account](https://docs.github.com/en/authentication/connecting-to-github-with-ssh/adding-a-new-ssh-key-to-your-github-account).

2. Clone this repository to your local machine:

```
<NAME_EMAIL>:malou-io/malou-app.git
```

## Start Malou App

### Install dependencies

#### Go into the repository

```
cd malou-app
```

#### Download node.js using nvm

```
nvm use
nvm install 22.15.0 (at this time of writing)
```

`nvm use` will automatically detect the node version required to run our API and WEB apps in the `.nvmrc` file at the monorepo's root.

#### Download PNPM (a faster alternative to NPM) using NPM

```
npm install -g pnpm@10.6.5
```

At this time of writing the pnpm version is 10.6.5, but this can change quite quickly. To check the version currently used, search for `packageManager` in root's `package.json`

You can check the versions of pnpm and node you've installed:

```
node -v
pnpm -v
```

#### Install dependencies

Before you can install dependencies, you have to ask a member of team the value of `PQINA_NPM_TOKEN`. Then add `export PQINA_NPM_TOKEN=<value>` to your `~/.bash_profile`, `~/.zshrc`, `~/.profile`, or `~/.bashrc` depending on what you use. Once this is set, reload your terminal and run:

```
pnpm i
```

at the monorepo's root. This command will install all the monorepo's dependencies using pnpm workspaces feature. The workspaces composing our monorepo are defined in `pnpm-workspace.yaml`:

2 apps:

- our API, located in `apps/app-malou-api`
- our WEB app, located in `apps/app-malou-web`

Several packages used by the API, the WEB app or both:

- `packages/malou-utils`
- `packages/malou-emails`
- `packages/malou-dto`
- ...

### Start the App

#### Get environments variables

Please ask a member of the team to send you **env.zip**. Once it's done, add them to the root of the API in `apps/app-malou-api`.

Remember, **.env.default** is always set. Depending on your `NODE_ENV` (local, development, production ...), **.env.(\*)** will override **.env.default**.

Before running the app, create a `.env` to store all ports used by your containers (you can choose what you want, but you'll need to edit them if there are used in other `.env.\*`)

```
## .env
## docker-compose.yml will read .env file and set configured ports e.g ${ELASTICMQ_PORT}
ELASTICMQ_PORT=9324
REDIS_PORT=6379
MONGO_PORT=27017
```

#### Start Docker Desktop

Start Docker Desktop

#### Run the app

```
pnpm run start-local
```

This will build your packages, your apps and launch them in watch mode. Any changes you make will automatically trigger a rebuild of the corresponding apps or packages and refresh the browser for the WEB app.  
The application will be available at `http://localhost:4200/`.

## Feed the DB

In this section, we'll show you how to feed your local database to start playing with the app

### Dump and restore the development DB to your local DB

Ask a team member to create a user for you on [the development environment](https://development.omni.malou.io/)

Then to dump the development database and restore it to your local DB:

1. Make sure that you ran `pnpm run start-local` (we need your local DB up and running)
2. Run the following command:

```
NODE_ENV=development MONGODB_URI=<env.MONGODB_URI> pnpm run db:seed
```

and change `env.MONGODB_URI` with the value of `MONGODB_URI` in your `.env.development`. Your DB should be filled in with the development data and you should be able to connect using your user credentials !  
To check if the restore worked, continue to the next section.

As an alternative, you can also directly connect your local stack to the development remote DB using:

```
pnpm run start-dev
```

#### Note

This restore method uses, through Docker Compose, your local MongoDB to perform the data dump and restore. The main advantage of this technique is that you don't need to install `mongorestore` or `mongodump` (through the [MongoDB Database Tools](https://www.mongodb.com/docs/database-tools/installation/installation/)) locally to perform the data seed. Everything is run through the MongoDB docker container.

**The potential downside** is that for big dumps / restores you're limited by the resources allocated to Docker Desktop. If you feel somewhat limited, or if you feel that the action is too slow, you can increase the resources in Docker Desktop's settings, or install the [MongoDB Database Tools](https://www.mongodb.com/docs/database-tools/installation/installation/) and run the command locally.

### Connect and access DBs

Use MongoDB Compass to access our different DBs. To see the data you've just inserted locally, open MongoDB Compass and enter `mongodb://localhost:27017/` as the connection string.

## Git / Github

### Merging Pull requests

When merging a feature branch in main : **use the second or third option ("Squash and merge" or "Rebase and merge")**

When merging a release branch in main or release branch in release : **Always use the first option ("Create a merge commit")**

### Commit number per merged feature branch

One commit by feature branch (with some exceptions)
So squash your feature branch manually or with Github option "Squash and merge"

### Commit naming convention

```
type(module): short description (optional PR number)  # required

description  # optional, can be multi line

TICKET: Ticket link  # required if no PR link in the title or if PR link does not contains the ticket link
METADATA_TITLE: metadata  # optional, can be multiple
```

Line breaks are important between first line, description and footer

**type :** feature / hotfix (only for production fix) / fix / refactor / improvement / ci / docs / (create others if you need)

**module :** reviews / posts / ai-settings / jobs / ...  
required for types : feature / hotfix / fix  
optional for others types

Please include the PR number if the PR description contains additional information that is not in the commit description, eg : some screenshots.

If there is no Airtable ticket, please mention it either by adding empty parenthesis in the title or "Ticket: none" in the footer part or by adding "none" to the PR description (if you added the PR number) so we know that it's not an oversight.

Exemple 1 :

```
feature(stories): add facebook stories creation

- display facebook stories along instagram stories
- add modal to create / edit fb story

Ticket: https://artable.com/84fe5f1ez69a
Breaking change: Instagram stories cannot be scheduled anymore
```

Exemple 2 :

```
fix(ai-setting): confirm button translation

Translations was missing for languages other than french, so i added them

Ticket: https://artable.com/84fe5f1ez69a
```

Exemple 2 bis with PR link :

```
fix(ai-setting): confirm button translation (#5874)

Translations was missing for languages other than french, so i added them
```

### First way to commit : Squashing manually before submitting the PR ("Squash and merge" or "Rebase and merge" options)

In this case, Github will automatically include the commit description into the description of the PR, the PR description is optional

### Second way to commit: Squashing with Github ("Squash and merge" option)

In this case, when you create the PR, the final commit description is not yet created, so you need to include at least the Airtable ticket in the PR description manually so the reviewers can access it.

When your PR is reviewed/validated and Github asks for the commit description, follow the rules above.

### Third way to commit : You want to keep all commits in your PR ("Rebase and merge" option)

In this case, every commit on you PR must follow the convention, the PR description is optional

### Branch naming convention

```
type/module/short description
```

For the possible values of type and module [go here](#commit-naming-convention)

## Feature flag

This project uses **GrowthBook** to manage _feature flags_, enabling progressive releases, experiments, and permission management.
This documentation covers the naming convention, feature flag categories, and how to document links between **Airtable** and GrowthBook.

### Feature Flag Categories

Each feature flag must belong to one of the following categories:

- **release**: Controls the progressive rollout of a new feature.
- **kill-switch**: Quickly disables a feature when needed.
- **experiment**: Used for A/B testing and other experiments.
- **migration**: Supports transitioning from an old feature to a new one.
- **operational**: Related to technical functionalities (monitoring, alerting).
- **permission**: Manages user access rights.

### Naming Convention

Feature flags must follow this naming format:

\<category\>-\<project-name\>-\<feature-name\>

**Examples**:

- `release-yext-connections`
- `experiment-new-signup-flow`

### Best Practices

- **Airtable**: Add the feature-flag used in the specific Airtable ticket
- **Avoid Duplication**: Standardize feature flag names to simplify management.
- **Regular Reviews**: Schedule periodic reviews clean old feature flags that are not useful anymore

## Useful tips

### Setup Husky (Git hooks)

To activate git hooks, run: `pnpm exec husky install .husky` at the project's root.
At the moment, there is one pre-commit hook that will run eslint on the files you modified before committing them.

### VSCode

For VSCode users, several configurations have been commited to this repo to help you in your day to day work.

1. **Open the repo using the `malou.code-workspace` file**. After opening VSCode, go to `File > Open Workspace from File` then look for the file `.vscode/malou.code-workspace` (depending on your OS, you'll have to make a manipulation to display hidden files in your files explorer).
   This file will organize the repo by apps, packages, ... but also will come with some settings to enable format on save with Prettier, configurations for some extensions, ....

2. **Get recommended extensions**. We listed extensions that we're using on a day to day basis in `.vscode/extensions.json`. After opening your workspace using the `.vscode/malou.code-workspace`, you should see the recommended extensions in the extensions tab

3. **`.vscode/launch.json`** lists all commands for debugging, running the apps in development, staging and production mode as well as running manual tasks

### Refresh your .env variables

⚠️ Really useful if you want to refresh your .env variable taken into account by docker-compose.yml

- `docker compose stop`
  The docker compose stop command will stop your containers, but it won’t remove them.
- `docker compose down`
  The docker compose down command will stop your containers, but it also removes the stopped containers as well as any networks that were created.

### Useful commands

- `pnpm run format`: run prettier to format all your files
- `pnpm run lint`: run eslint on the whole monorepo
- `pnpm run test:unit`: run unit tests for the whole monorepo
- `pnpm run build-clean`: clean all your built artifacts
- `pnpm run build --force`: Rebuild the whole app (without launching it)
- `pnpm run start-development`: launch your app locally, connected to development environment
- `pnpm run start-staging`: launch your app locally, connected to staging environment
- `pnpm run start-production`: launch your app locally, connected to production environment
- ... (check all the scripts in root's `package.json`)

### Useful infos

Some info specific to the apps can be found in their readme:

- [API](./apps/app-malou-api/README.md)
- [WEB](./apps/app-malou-web/README.md)

## Contributing

1. Fork the Project
2. Create your new Branch (`git checkout -b feature/YourFeature`)
3. Commit your Changes (`git commit -m 'Add some new feature'`)
4. Push to the Branch (`git push origin feature/YourFeature`)
5. Open a Pull Request
