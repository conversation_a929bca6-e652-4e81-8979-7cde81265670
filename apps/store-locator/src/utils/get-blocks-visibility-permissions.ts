import { config } from ':config';

export enum BlockType {
    FAQ = 'faq',
}

const blockMap: Record<string, Record<BlockType, boolean>> = {
    'groupe-rollroll-x-bolkiri': {
        [BlockType.FAQ]: true,
    },
    'krispy-kreme': {
        [BlockType.FAQ]: true,
    },
    bioburger: {
        [BlockType.FAQ]: false,
    },
    'breads-bakery': {
        [BlockType.FAQ]: true,
    },
    'garden-ice-cafe': {
        [BlockType.FAQ]: true,
    },
    melt: {
        [BlockType.FAQ]: true,
    },
    'white-coffee': {
        [BlockType.FAQ]: true,
    },
};

const defaultBlockMap: Record<BlockType, boolean> = {
    [BlockType.FAQ]: true,
};

export function getBlocksVisibilityPermissions(
    organizationName: string,
): Record<BlockType, boolean> {
    const organizationKey = organizationName.toLowerCase().replaceAll(' ', '-');

    const blocksPermissions = !['production', 'test'].includes(
        config.environment,
    )
        ? defaultBlockMap
        : blockMap[organizationKey];

    if (!blocksPermissions) {
        throw new Error(
            `Cannot find blocks permissions for organization "${organizationName}".`,
        );
    }

    return blocksPermissions;
}
