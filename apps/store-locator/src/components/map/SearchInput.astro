---
import Coordinates from ':assets/icons/coordinates.svg';
import { initTranslationFunction } from ':i18n/index';
import { getStyles } from ':utils/get-element-styles';

import type { GetStoreLocatorCentralizationPageDto } from '@malou-io/package-dto';
interface Props {
    stores: GetStoreLocatorCentralizationPageDto['stores'];
    styles: GetStoreLocatorCentralizationPageDto['styles'];
    classList: string;
    inputClass: string;
}

const { styles, inputClass, classList } = Astro.props as Props;

const getElementStyles = getStyles({ styles });
const t = await initTranslationFunction();
---

<div
    class={`${getElementStyles({ elementId: 'store-list-search-wrapper' })} ${classList} h-[90px] w-full items-center justify-center `}
>
    <div class="flex w-[90%] items-center justify-between">
        <input
            class={`${getElementStyles({ elementId: 'store-list-search-input' })} ${inputClass}  placeholder:uppercase placeholder:text-sm  placeholder:font-secondary text-map-list-content color-[#333] text-sm pl-7 font-secondary my-3 h-[48px] w-full rounded-sm border bg-white`}
            placeholder={t('map.search.placeholder')}
        />
        <button
            class={`text-map-list-content user-location ${getElementStyles({ elementId: 'store-list-search-button' })} ml-2 flex h-[48px] w-[68px] items-center justify-center rounded-sm border`}
            aria-label={t('map.locate.button.aria-label')}
        >
            <Coordinates
                class=`${getElementStyles({ elementId: 'store-list-search-icon'})}`
                height={30}
                width={30}
            />
        </button>
    </div>
</div>
