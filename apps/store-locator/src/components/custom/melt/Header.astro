---
import type { GetStoreLocatorPagesDto } from '@malou-io/package-dto';
import { Picture } from 'astro:assets';

import type {
    StaticLinks,
    StoreLocatorLanguage,
} from ':interfaces/pages.interfaces';

// To type Astro component props, we have to name the interface "Props" literally
interface Props {
    urls: NonNullable<GetStoreLocatorPagesDto['urls']>;
    lang: StoreLocatorLanguage;
    storeId: string;
}

const { urls, lang, storeId } = Astro.props as Props;

const defaultLanguage = lang;

const selectableLanguages = ['fr', 'en'];

const storeKey = `store.${storeId}`;

const languages = selectableLanguages.map((language) => {
    const url = storeId
        ? urls[language as StoreLocatorLanguage]?.[storeKey]
        : urls[language as StoreLocatorLanguage]?.map;

    return {
        key: language as StoreLocatorLanguage,
        value: language === 'fr' ? 'Fr' : 'En',
        url,
    };
});

// TODO: add late [@hamza]
//const mapUrl = urls[lang]?.map || urls['fr']?.map;

const allMenuLinks: StaticLinks = {
    fr: [
        {
            text: 'nos univers',
            href: 'https://www.meltslowsmokedbarbecue.com/nos-univers',
            subLinks: [
                {
                    text: 'melt restaurant',
                    href: 'https://www.meltslowsmokedbarbecue.com/melt-restaurant',
                },
                {
                    text: 'melt deli',
                    href: 'https://www.meltslowsmokedbarbecue.com/melt-deli',
                },
            ],
        },
        {
            text: 'addresses',
            href: null,
            subLinks: [
                // TODO: will be changed later by store urls, just for testing now [@hamza]
                {
                    text: 'melt batignolles',
                    href: 'https://www.meltslowsmokedbarbecue.com/melt-restaurant-batignolles',
                },
            ],
        },
        {
            text: 'menu',
            href: 'https://www.meltslowsmokedbarbecue.com/menu',
            subLinks: [
                {
                    text: 'menu batignolles',
                    href: 'https://www.meltslowsmokedbarbecue.com/menu-melt-batignolles',
                },
                {
                    text: 'menu cambronne',
                    href: 'https://www.meltslowsmokedbarbecue.com/menu-melt-cambronne',
                },
                {
                    text: 'menu oberkampf',
                    href: 'https://www.meltslowsmokedbarbecue.com/menu-melt-oberkampf',
                },
                {
                    text: 'melt deli',
                    href: 'https://www.meltslowsmokedbarbecue.com/menu-melt-deli',
                },
            ],
        },
        {
            text: 'réservations',
            href: `https://www.meltslowsmokedbarbecue.com/reservation`,
        },
        {
            text: 'catering',
            href: 'https://www.meltslowsmokedbarbecue.com/catering',
        },
        {
            text: 'franchise',
            href: 'https://www.meltslowsmokedbarbecue.com/franchise',
        },
        {
            text: 'shop',
            href: 'https://www.meltslowsmokedbarbecue.com/shop',
        },
        {
            text: 'actualités',
            href: 'https://www.meltslowsmokedbarbecue.com/actualite',
        },
        {
            text: 'faq',
            href: 'https://www.meltslowsmokedbarbecue.com/faq',
        },
    ],
    en: [
        {
            text: 'our concepts',
            href: 'https://www.meltslowsmokedbarbecue.com/en/nos-univers',
            subLinks: [
                {
                    text: 'melt restaurant',
                    href: 'https://www.meltslowsmokedbarbecue.com/en/melt-restaurant',
                },
                {
                    text: 'melt deli',
                    href: 'https://www.meltslowsmokedbarbecue.com/en/melt-deli',
                },
            ],
        },
        {
            text: 'addresses',
            href: null,
            subLinks: [
                // TODO: will be changed later by store urls, just for testing now [@hamza]
                {
                    text: 'melt batignolles',
                    href: 'https://www.meltslowsmokedbarbecue.com/en/melt-restaurant-batignolles',
                },
            ],
        },
        {
            text: 'menu',
            href: 'https://www.meltslowsmokedbarbecue.com/en/menu',
            subLinks: [
                {
                    text: 'menu batignolles',
                    href: 'https://www.meltslowsmokedbarbecue.com/en/menu-melt-batignolles',
                },
                {
                    text: 'menu cambronne',
                    href: 'https://www.meltslowsmokedbarbecue.com/en/menu-melt-cambronne',
                },
                {
                    text: 'menu oberkampf',
                    href: 'https://www.meltslowsmokedbarbecue.com/en/menu-melt-oberkampf',
                },
                {
                    text: 'melt deli',
                    href: 'https://www.meltslowsmokedbarbecue.com/en/menu-melt-deli',
                },
            ],
        },
        {
            text: 'réservations',
            href: `https://www.meltslowsmokedbarbecue.com/en/reservation`,
        },
        {
            text: 'catering',
            href: 'https://www.meltslowsmokedbarbecue.com/en/catering',
        },
        {
            text: 'franchise',
            href: 'https://www.meltslowsmokedbarbecue.com/en/franchise',
        },
        {
            text: 'shop',
            href: 'https://www.meltslowsmokedbarbecue.com/en/shop',
        },
        {
            text: 'news',
            href: 'https://www.meltslowsmokedbarbecue.com/en/actualite',
        },
        {
            text: 'faq',
            href: 'https://www.meltslowsmokedbarbecue.com/en/faq',
        },
    ],
};

const allMainPageLink: StaticLinks = {
    fr: [
        {
            text: 'accueil',
            href: 'https://www.meltslowsmokedbarbecue.com/',
        },
    ],
    en: [
        {
            text: 'home',
            href: 'https://www.meltslowsmokedbarbecue.com/en/',
        },
    ],
};

const allMobileLinks: StaticLinks = {
    fr: [
        {
            text: 'livraisons',
            href: 'https://www.ubereats.com/fr-en/store/melt-cambronne/Xu49kkP7VC22Nbh0FoPK6g?diningMode=DELIVERY&mod=storeDeliveryTime',
        },
        {
            text: 'reservations',
            href: 'https://www.meltslowsmokedbarbecue.com/reservation',
        },
    ],
    en: [
        {
            text: 'deliveries',
            href: '', // not working on their side neither
        },
        {
            text: 'reservations',
            href: 'https://www.meltslowsmokedbarbecue.com/en/reservation',
        },
    ],
};

const menuLinks = allMenuLinks[lang] || [];
const mainLink = allMainPageLink[lang] || [];
const mobileLinks = allMobileLinks[lang] || [];
---

<div class="bg-fourth fixed top-0 left-0 z-100 flex h-[50px] w-full flex-col">
    <header class="font-primary">
        <div
            class="flex w-full items-center justify-between py-0 md:pt-0 md:pb-0"
        >
            <div
                class="flex h-[53px] w-full items-center justify-between font-normal xl:hidden"
            >
                <div class="h-5 w-5 pl-5">
                    <button
                        aria-label="Toggle Menu"
                        id="open-mobile-menu-button"
                    >
                        <svg
                            id="menu-icon"
                            class="h-5.5 w-5.5 fill-white"
                            data-bbox="0 0 252.492 252.492"
                            xmlns="http://www.w3.org/2000/svg"
                            viewBox="0 0 252.492 252.492"
                            role="presentation"
                            aria-hidden="true"
                        >
                            <g>
                                <path
                                    d="M252.492 117.246H135.246V0h-18v117.246H0v18h117.246v117.246h18V135.246h117.246v-18z"
                                ></path>
                            </g>
                        </svg>
                        <svg
                            aria-hidden="true"
                            id="close-icon"
                            role="presentation"
                            class="fill-primary hidden h-5.5 w-5.5"
                            viewBox="0 0 1000 1000"
                            xmlns="http://www.w3.org/2000/svg"
                            ><path
                                d="M742 167L500 408 258 167C246 154 233 150 217 150 196 150 179 158 167 167 154 179 150 196 150 212 150 229 154 242 171 254L408 500 167 742C138 771 138 800 167 829 196 858 225 858 254 829L496 587 738 829C750 842 767 846 783 846 800 846 817 842 829 829 842 817 846 804 846 783 846 767 842 750 829 737L588 500 833 258C863 229 863 200 833 171 804 137 775 137 742 167Z"
                            ></path></svg
                        >
                    </button>
                </div>
            </div>

            <div class="flex w-full pl-1 xl:w-1/4">
                <div>
                    <a
                        href={mainLink[0]?.href}
                        aria-label={mainLink[0]?.text}
                        class="block"
                    >
                        <Picture
                            src="https://malou-production.s3.eu-west-3.amazonaws.com/store-locator/organization/6216176cfad4028e790f2c68/favicons/logo-white.png"
                            formats={['webp']}
                            fallbackFormat="png"
                            alt="Logo melt"
                            inferSize
                            class="h-[55px] !w-[105px]"
                            densities={[1, 2, 3]}
                        />
                    </a>
                </div>
            </div>

            <div
                class="flex w-full justify-end xl:w-3/4 xl:items-center xl:justify-normal"
            >
                <!-- Desktop Menu -->
                <div class="hidden w-[70%] justify-end xl:flex">
                    <div class="flex gap-2">
                        {
                            menuLinks.map((link) =>
                                link.subLinks ? (
                                    <div class="group hover:bg-secondary relative p-2">
                                        <a
                                            class="font-tertiary text-[15px] text-white uppercase transition"
                                            href={link.href || ''}
                                        >
                                            {link.text}
                                        </a>

                                        <ul class="bg-fourth absolute top-[45px] left-[-50px] z-50 ms-auto hidden w-[160px] rounded-md group-hover:block">
                                            {link.subLinks?.map((subLink) => (
                                                <li class="w-full">
                                                    <a
                                                        href={subLink.href}
                                                        class="hover:bg-secondary block px-4 py-2 text-center text-[15px] text-white"
                                                    >
                                                        {subLink.text}
                                                    </a>
                                                </li>
                                            )) || null}
                                        </ul>
                                    </div>
                                ) : (
                                    <a
                                        href={link.href}
                                        class="font-tertiary hover:bg-secondary p-2 text-[15px] font-normal text-white uppercase"
                                    >
                                        {link.text}
                                    </a>
                                ),
                            )
                        }
                    </div>
                </div>
                <div class="flex w-full justify-end pr-4 xl:w-[30%] xl:pr-0">
                    <div class="font-secondary relative mr-8">
                        <div
                            class="flex items-center justify-center border border-white"
                        >
                            {
                                languages.map((language) => (
                                    <div
                                        id={`language-${language.key}`}
                                        class="font-tertiary flex h-[16px] w-[55px] cursor-pointer items-center justify-center text-[13px] text-white"
                                    >
                                        {language.value}
                                    </div>
                                ))
                            }
                        </div>

                        <ul
                            id="language-menu"
                            class="bg-primary absolute z-50 ms-auto hidden w-[100px]"
                        >
                            {
                                languages.map((language) => (
                                    <li
                                        id={`${language.key}-language-item`}
                                        class="hidden pt-2 pb-1"
                                        data-key={language.key}
                                    >
                                        <a
                                            href={language.url}
                                            class="pr-4 pl-1.5 text-[15px] font-normal text-white"
                                        >
                                            {language.value}
                                        </a>
                                    </li>
                                ))
                            }
                        </ul>
                    </div>
                </div>
            </div>
        </div>

        <!-- Mobile Menu Button -->

        <div
            id="mobile-menu"
            class="bg-fourth transition-right fixed top-0 right-[-100%] h-[100vh] w-full overflow-scroll p-6 shadow-lg duration-300 ease-in-out"
        >
            <div class="flex w-full justify-end pl-5">
                <button aria-label="Toggle Menu" id="close-mobile-menu-button">
                    <svg
                        aria-hidden="true"
                        id="close-icon"
                        role="presentation"
                        class="h-7 w-7 fill-white"
                        viewBox="0 0 1000 1000"
                        xmlns="http://www.w3.org/2000/svg"
                        ><path
                            d="M742 167L500 408 258 167C246 154 233 150 217 150 196 150 179 158 167 167 154 179 150 196 150 212 150 229 154 242 171 254L408 500 167 742C138 771 138 800 167 829 196 858 225 858 254 829L496 587 738 829C750 842 767 846 783 846 800 846 817 842 829 829 842 817 846 804 846 783 846 767 842 750 829 737L588 500 833 258C863 229 863 200 833 171 804 137 775 137 742 167Z"
                        ></path></svg
                    >
                </button>
            </div>
            <div class="flex flex-col">
                <div class="mb-10">
                    {
                        mobileLinks.map((link) => (
                            <div class="flex justify-center pt-2 pb-2">
                                <a
                                    href={link.href}
                                    class="font-tertiary w-[50%] border-4 border-white text-center text-[25px] font-normal text-white uppercase italic"
                                >
                                    {link.text}
                                </a>
                            </div>
                        ))
                    }
                </div>
                <div class="px-4">
                    {
                        menuLinks.map((link, index) =>
                            link.subLinks ? (
                                <div class="mb-4">
                                    <div class="flex justify-between border-b-8 border-white p-2">
                                        <a
                                            class="font-tertiary pl-1 text-[25px] text-white uppercase"
                                            href={link.href || ''}
                                        >
                                            {link.text}
                                        </a>
                                        <svg
                                            id={`mobile-link-sub-links-control-${index}`}
                                            xmlns="http://www.w3.org/2000/svg"
                                            class="h-4 w-4 fill-white"
                                            viewBox="0 0 9.2828 4.89817"
                                        >
                                            <path d="M4.64116,4.89817a.5001.5001,0,0,1-.34277-.13574L.15727.86448A.50018.50018,0,0,1,.84282.136L4.64116,3.71165,8.44.136a.50018.50018,0,0,1,.68555.72852L4.98393,4.76243A.5001.5001,0,0,1,4.64116,4.89817Z" />
                                        </svg>
                                    </div>

                                    <ul
                                        class="bg-fourth hidden w-full rounded-md"
                                        id={`mobile-link-sub-links-${index}`}
                                    >
                                        {link.subLinks?.map((subLink) => (
                                            <li class="w-full pt-2">
                                                <a
                                                    href={subLink.href}
                                                    class="hover:bg-secondary font-tertiary block px-4 py-2 text-start text-[20px] text-white"
                                                >
                                                    {subLink.text}
                                                </a>
                                            </li>
                                        )) || null}
                                    </ul>
                                </div>
                            ) : (
                                <div class="mb-4 border-b-8 border-white p-2">
                                    <a
                                        href={link.href}
                                        class="bg-fourth font-tertiary block text-start text-[25px] font-normal text-white uppercase"
                                    >
                                        {link.text}
                                    </a>
                                </div>
                            ),
                        )
                    }
                </div>
            </div>
        </div>

        <script
            is:inline
            define:vars={{ languages, initLanguage: defaultLanguage }}
        >
            // Mobile menu functionality
            const openMobileMenuButton = document.getElementById(
                'open-mobile-menu-button',
            );
            const closeMobileMenuButton = document.getElementById(
                'close-mobile-menu-button',
            );

            const mobileMenu = document.getElementById('mobile-menu');

            openMobileMenuButton?.addEventListener('click', (event) => {
                event.preventDefault();
                mobileMenu?.classList.toggle('right-[-100%]');
                mobileMenu?.classList.toggle('right-0');
            });

            closeMobileMenuButton?.addEventListener('click', (event) => {
                event.preventDefault();
                mobileMenu?.classList.toggle('right-[-100%]');
                mobileMenu?.classList.toggle('right-0');
            });

            // sub-links functionality for mobile menu
            const menuLinks = Array.from(
                document.querySelectorAll(
                    '[id^="mobile-link-sub-links-control-"]',
                ),
            );

            menuLinks.forEach((menuLink, index) => {
                const subLinks = document.getElementById(
                    `mobile-link-sub-links-${index}`,
                );
                menuLink.addEventListener('click', (event) => {
                    event.preventDefault();
                    menuLink.classList.toggle('rotate-180');

                    subLinks?.classList.toggle('hidden');
                    subLinks?.classList.toggle('block');
                });
            });

            // Language selector functionality
            let selectedLanguage = initLanguage;

            const frenchLanguageItem = document.getElementById('language-fr');
            const englishLanguageItem = document.getElementById('language-en');

            initLanguageItems();
            toggleLanguagesItems();

            function initLanguageItems() {
                languages.forEach((language) => {
                    const languageItem = document.getElementById(
                        `language-${language.key}`,
                    );

                    if (languageItem && selectedLanguage === language.key) {
                        languageItem.classList.toggle('bg-white/80');
                        languageItem.classList.toggle('text-white');
                        languageItem.classList.toggle('text-black');
                    }
                });
            }

            function toggleLanguagesItems() {
                languages.forEach((language) => {
                    const languageLink = document.getElementById(
                        `language-${language.key}`,
                    );
                    if (languageLink) {
                        languageLink.addEventListener('click', (e) => {
                            e.preventDefault();
                            onLanguageSelected(language.key);
                        });
                    }
                });
            }

            function onLanguageSelected(languageKey) {
                const relativePath = languages.find(
                    (lang) => lang.key === languageKey,
                )?.url;
                if (relativePath) {
                    window.location.replace(
                        window.location.origin + `/${relativePath}`,
                    );
                }
            }
        </script>
    </header>
</div>
