---
import type {
    StaticLinks,
    StoreLocatorLanguage,
} from ':interfaces/pages.interfaces';
import type { GetStoreLocatorPagesDto } from '@malou-io/package-dto/src/store-locator/store-locator.response.dto';
import { Picture } from 'astro:assets';

// To type Astro component props, we have to name the interface "Props" literally
interface Props {
    lang: StoreLocatorLanguage;
    urls: NonNullable<GetStoreLocatorPagesDto['urls']>;
}

const { lang } = Astro.props;

// TODO: To use later [@hamza]
//const mapUrl = urls[lang]?.map || urls['fr']?.map;

const allMenuLinks: StaticLinks = {
    fr: [
        {
            text: 'À Propos',
            href: 'https://www.meltslowsmokedbarbecue.com/apropos',
        },
        {
            text: 'Nos univers',
            href: 'https://www.meltslowsmokedbarbecue.com/nos-univers',
        },
        {
            text: 'Catering',
            href: 'https://www.meltslowsmokedbarbecue.com/catering',
        },
        {
            text: 'Franchise',
            href: 'https://www.meltslowsmokedbarbecue.com/franchise',
        },
        {
            text: 'Actualités',
            href: 'https://www.meltslowsmokedbarbecue.com/actualite',
        },
        {
            text: 'Faq',
            href: 'https://www.meltslowsmokedbarbecue.com/faq',
        },
        {
            text: 'Réservations',
            href: `https://www.meltslowsmokedbarbecue.com/reservation`,
        },
        {
            text: 'Recrutement',
            href: 'https://www.meltslowsmokedbarbecue.com/recrutement-2',
        },
        {
            text: 'shop',
            href: 'https://www.meltslowsmokedbarbecue.com/shop',
        },
        {
            text: 'Contact',
            href: 'https://www.meltslowsmokedbarbecue.com/contact',
        },
        {
            text: 'Legal Mentions',
            href: 'https://www.meltslowsmokedbarbecue.com/mentions-legales  ',
        },
    ],
    en: [
        {
            text: 'about us',
            href: 'https://www.meltslowsmokedbarbecue.com/en/apropos',
        },
        {
            text: 'Our Concepts',
            href: 'https://www.meltslowsmokedbarbecue.com/en/nos-univers',
        },
        {
            text: 'Catering',
            href: 'https://www.meltslowsmokedbarbecue.com/en/catering',
        },
        {
            text: 'Franchise',
            href: 'https://www.meltslowsmokedbarbecue.com/en/franchise',
        },
        {
            text: 'News',
            href: 'https://www.meltslowsmokedbarbecue.com/en/actualite',
        },
        {
            text: 'Faq',
            href: 'https://www.meltslowsmokedbarbecue.com/en/faq',
        },
        {
            text: 'Shop',
            href: 'https://www.meltslowsmokedbarbecue.com/en/shop',
        },
        {
            text: 'Réservations',
            href: `https://www.meltslowsmokedbarbecue.com/en/reservation`,
        },
        {
            text: 'Contact',
            href: 'https://www.meltslowsmokedbarbecue.com/en/contact',
        },
        {
            text: 'Legal Mentions',
            href: 'https://www.meltslowsmokedbarbecue.com/en/mentions-legales',
        },
    ],
};

const allRestaurantMenusLinks: StaticLinks = {
    fr: [
        {
            text: 'Menu Batignolles',
            href: 'https://www.meltslowsmokedbarbecue.com/menu-melt-batignolles',
        },
        {
            text: 'Menu Cambronne',
            href: 'https://www.meltslowsmokedbarbecue.com/menu-melt-cambronne',
        },
        {
            text: 'Menu Oberkampf',
            href: 'https://www.meltslowsmokedbarbecue.com/menu-melt-oberkampf',
        },
        {
            text: 'Menu Deli',
            href: 'https://www.meltslowsmokedbarbecue.com/menu-melt-deli',
        },
    ],
    en: [
        {
            text: 'Menu Batignolles',
            href: 'https://www.meltslowsmokedbarbecue.com/en/menu-melt-batignolles',
        },
        {
            text: 'Menu Cambronne',
            href: 'https://www.meltslowsmokedbarbecue.com/en/menu-melt-cambronne',
        },
        {
            text: 'Menu Oberkampf',
            href: 'https://www.meltslowsmokedbarbecue.com/en/menu-melt-oberkampf',
        },
        {
            text: 'Melt Deli',
            href: 'https://www.meltslowsmokedbarbecue.com/en/menu-melt-deli',
        },
    ],
};

const socialLinks = [
    {
        text: 'Instagram',
        href: 'https://www.instagram.com/meltslowsmokedbarbecue/',
    },
    {
        text: 'Tiktok',
        href: 'https://www.tiktok.com/@meltslowsmokedbarbecue',
    },
    {
        text: 'Youtube',
        href: 'https://www.youtube.com/@melt_slow_smoked_barbecue',
    },
    {
        text: 'Facebook',
        href: 'https://www.facebook.com/meltparis/',
    },
    {
        text: 'Linkedin',
        href: 'https://fr.linkedin.com/company/groupe-melt',
    },
];

const menuLinks = allMenuLinks[lang] || [];
const restaurantMenusLinks = allRestaurantMenusLinks[lang] || [];
---

<footer>
    <div class="font-secondary bg-fourth flex flex-col pt-10 font-[400]">
        <div
            class="flex flex-col items-center py-10 md:flex-row md:items-start"
        >
            <div class="flex w-full justify-center md:w-1/3 md:justify-normal">
                <Picture
                    src="https://malou-production.s3.eu-west-3.amazonaws.com/store-locator/organization/6216176cfad4028e790f2c68/favicons/logo-white.png"
                    formats={['webp']}
                    fallbackFormat="png"
                    alt="Melt Logo"
                    inferSize
                    width={400}
                    height={300}
                    densities={[1, 2, 3]}
                />
            </div>

            <div
                class="mt-10 flex w-full flex-col gap-10 text-white md:flex-row md:gap-40"
            >
                <div
                    class="mt-11 flex flex-col gap-3 text-center md:text-start"
                >
                    {
                        menuLinks.map((link) => (
                            <a
                                class="font-secondary text-[13px]"
                                href={link.href}
                            >
                                {link.text}
                            </a>
                        ))
                    }
                </div>
                <div
                    class="item-center flex flex-col gap-3 text-center md:text-start"
                >
                    <span class="font-primary text-[20px]"> MENU </span>

                    <div class="item-center flex flex-col gap-3 text-[16px]">
                        {
                            restaurantMenusLinks.map((link) => (
                                <a
                                    class="font-secondary text-[13px]"
                                    href={link.href}
                                >
                                    {link.text}
                                </a>
                            ))
                        }
                    </div>
                </div>

                <div
                    class="item-center flex flex-col gap-3 text-center md:text-start"
                >
                    {
                        lang === 'fr' && (
                            <span class="font-primary text-[20px]">
                                OÙ SOMMES NOUS
                            </span>
                        )
                    }
                    {
                        lang === 'en' && (
                            <span class="font-primary text-[20px]">
                                LOCATIONS
                            </span>
                        )
                    }

                    <div class="item-center flex flex-col gap-3 text-[16px]">
                        {
                            [
                                'Batignolles',
                                'Cambronne',
                                'Oberkampf',
                                'Les Halles',
                                'Rochechouart',
                            ].map((location) => (
                                <a class="font-secondary text-[13px]" href="">
                                    {location}
                                </a>
                            ))
                        }
                    </div>
                </div>

                <div
                    class="item-center flex flex-col gap-3 text-center md:text-start"
                >
                    {
                        lang === 'fr' && (
                            <span class="font-primary text-[20px]">
                                SUIVEZ NOUS
                            </span>
                        )
                    }
                    {
                        lang === 'en' && (
                            <span class="font-primary text-[20px]">
                                FOLLOW US
                            </span>
                        )
                    }

                    <div class="item-center flex flex-col gap-3 text-[16px]">
                        {
                            socialLinks.map((link) => (
                                <a
                                    class="font-secondary text-[13px]"
                                    href={link.href}
                                    target="_blank"
                                >
                                    {link.text}
                                </a>
                            ))
                        }
                    </div>
                </div>
            </div>
        </div>
    </div>
</footer>
