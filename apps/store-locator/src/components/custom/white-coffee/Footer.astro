---
import type {
    StaticLinks,
    StoreLocatorLanguage,
} from ':interfaces/pages.interfaces';
import type { GetStoreLocatorPagesDto } from '@malou-io/package-dto/src/store-locator/store-locator.response.dto';
import { Picture } from 'astro:assets';

// To type Astro component props, we have to name the interface "Props" literally
interface Props {
    lang: StoreLocatorLanguage;
    urls: NonNullable<GetStoreLocatorPagesDto['urls']>;
}

const { lang } = Astro.props;

// TODO: To use later [@hamza]
//const mapUrl = urls[lang]?.map || urls['fr']?.map;

const allUtilityLinks: StaticLinks = {
    fr: [
        {
            text: 'Politique de confidentialité',
            href: 'https://white-coffee.com/fr/legals/politique-de-confidentialite',
        },
        {
            text: 'Livraison et politique de retour',
            href: 'https://white-coffee.com/fr/legals/livraison-et-politique-de-retour',
        },
        {
            text: 'Conditions générales de vente',
            href: 'https://white-coffee.com/fr/legals/conditions-generales-de-vente',
        },
    ],
    en: [
        {
            text: 'Cookies',
            href: 'https://white-coffee.com/legals/privacy-policy',
        },
        {
            text: 'Terms and Conditions',
            href: 'https://white-coffee.com/legals/cgv',
        },
        {
            text: 'Shipping & Return Policy',
            href: 'https://white-coffee.com/legals/shipping-and-return-policy',
        },
    ],
};

const allMainPageLink: StaticLinks = {
    fr: [
        {
            text: 'Accueil',
            href: 'https://white-coffee.com/fr',
        },
    ],
    en: [
        {
            text: 'Home',
            href: 'https://white-coffee.com',
        },
    ],
};

const leftText = {
    title: lang === 'fr' ? 'CAFÉ, PÂTISSERIES,' : 'COFFEE, PASTRIES,',
    subtitle: lang === 'fr' ? 'JUS FRAIS' : 'FRESH JUICES',
};

const rightText = {
    title: lang === 'fr' ? 'CAFÉ DE SPÉCIALITÉ,' : 'SPECIALTY COFFEE,',
    subtitle: lang === 'fr' ? 'OUVERT 7/7' : 'OPEN 7/7',
};

const mainPageLink = allMainPageLink[lang] || [];

const utilityLinks = allUtilityLinks[lang] || [];
---

<footer class="bg-tertiary px-10 py-12">
    <div class="mb-10 flex">
        <div class="mt-5 flex w-1/3 flex-col items-start">
            <div
                class="font-secondary text-[10px] font-bold text-white sm:text-[15px]"
            >
                {leftText.title}
            </div>
            <div
                class="font-secondary text-[10px] font-bold text-white sm:text-[15px]"
            >
                {leftText.subtitle}
            </div>
        </div>
        <div class="flex w-1/3 justify-center">
            <a
                href={mainPageLink[0]?.href}
                aria-label={mainPageLink[0]?.text}
                class="block"
            >
                <Picture
                    src="https://malou-production.s3.eu-west-3.amazonaws.com/store-locator/organization/682afd5e5a0bdbeeaf109700/favicons/logo_white.png"
                    formats={['jpg']}
                    fallbackFormat="png"
                    alt="Logo white coffee"
                    inferSize
                    class="h-[100px] w-[190px]"
                    densities={[1, 2, 3]}
                />
            </a>
        </div>
        <div class="mt-5 flex w-1/3 flex-col items-end">
            <div
                class="font-secondary text-[10px] font-bold text-white sm:text-[15px]"
            >
                {rightText.title}
            </div>
            <div
                class="font-secondary text-[10px] font-bold text-white sm:text-[15px]"
            >
                {rightText.subtitle}
            </div>
        </div>
    </div>
    <div class="flex justify-center">
        <div class="w-full border border-white"></div>
    </div>

    <div class="mt-6 flex gap-6">
        {
            utilityLinks.map((link) => (
                <a
                    href={link.href}
                    aria-label={link.text}
                    target="_blank"
                    class="text-primary block py-2 text-[10px] hover:underline"
                >
                    {link.text}
                </a>
            ))
        }
    </div>
</footer>
