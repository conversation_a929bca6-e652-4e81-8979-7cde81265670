---
import type { GetStoreLocatorPagesDto } from '@malou-io/package-dto';
import { Picture } from 'astro:assets';

import type {
    StaticLinks,
    StoreLocatorLanguage,
} from ':interfaces/pages.interfaces';

// To type Astro component props, we have to name the interface "Props" literally
interface Props {
    urls: NonNullable<GetStoreLocatorPagesDto['urls']>;
    lang: StoreLocatorLanguage;
    storeId: string;
}

const { urls, lang, storeId } = Astro.props as Props;

const defaultLanguage = lang;

const selectableLanguages = ['fr', 'en'];

const storeKey = `store.${storeId}`;

const languages = selectableLanguages.map((language) => {
    const url = storeId
        ? urls[language as StoreLocatorLanguage]?.[storeKey]
        : urls[language as StoreLocatorLanguage]?.map;

    return {
        key: language as StoreLocatorLanguage,
        value: language === 'fr' ? 'FR' : 'EN',
        url,
    };
});

// TODO: add later [@hamza]
//const mapUrl = urls[lang]?.map || urls['fr']?.map;

const allMenuLinks: StaticLinks = {
    fr: [
        {
            text: 'A propos',
            href: 'https://white-coffee.com/about',
        },
        {
            text: 'Shop',
            href: 'https://white-coffee.com/catalog',
        },
        {
            text: 'Lieux',
            href: 'https://white-coffee.com/stores',
        },
        {
            text: 'Journal',
            href: `https://white-coffee.com/daily`,
        },
        {
            text: 'Collaborations',
            href: 'https://white-coffee.com/fr/edito/collaborations',
        },
    ],
    en: [
        {
            text: 'About',
            href: 'https://white-coffee.com/en/about',
        },
        {
            text: 'Shop',
            href: 'https://white-coffee.com/en/catalog',
        },
        {
            text: 'Stores',
            href: 'https://white-coffee.com/en/stores',
        },
        {
            text: 'Daily',
            href: `https://white-coffee.com/en/daily`,
        },
        {
            text: 'Collaborations',
            href: 'https://white-coffee.com/edito/collaboration',
        },
    ],
};

const allMainPageLink: StaticLinks = {
    fr: [
        {
            text: 'accueil',
            href: 'https://white-coffee.com/fr',
        },
    ],
    en: [
        {
            text: 'home',
            href: 'https://white-coffee.com',
        },
    ],
};

const menuLinks = allMenuLinks[lang] || [];
const mainLink = allMainPageLink[lang] || [];
---

<div
    class="bg-tertiary fixed top-0 left-0 z-100 flex h-[90px] w-full flex-col border-b border-white"
>
    <header class="font-primary h-full">
        <!-- Desktop Menu -->

        <div class="flex h-full w-full items-center px-2 xl:px-16">
            <div
                class="flex h-[53px] w-1/3 items-center justify-between font-normal xl:hidden"
            >
                <div class="h-5 w-5 pl-5">
                    <button
                        class="flex items-center justify-center gap-4"
                        aria-label="Toggle Menu"
                        id="open-mobile-menu-button"
                    >
                        <svg
                            id="menu-icon"
                            aria-hidden="true"
                            role="presentation"
                            class="h-5.5 w-5.5 fill-white"
                            viewBox="0 0 1000 1000"
                            xmlns="http://www.w3.org/2000/svg"
                            ><path
                                d="M104 333H896C929 333 958 304 958 271S929 208 896 208H104C71 208 42 237 42 271S71 333 104 333ZM104 583H896C929 583 958 554 958 521S929 458 896 458H104C71 458 42 487 42 521S71 583 104 583Z"
                            ></path></svg
                        >
                        <span
                            class="font-secondary text-[12px] text-white uppercase"
                        >
                            Menu</span
                        >
                    </button>
                </div>
            </div>

            <div class="hidden w-1/3 items-center gap-6 xl:flex xl:w-2/5">
                {
                    menuLinks.map((link) => (
                        <a
                            href={link.href}
                            class="font-secondary border-white pb-3 text-[15px] font-normal text-white hover:border-b"
                        >
                            {link.text}
                        </a>
                    ))
                }
            </div>

            <div class="flex w-1/2 justify-center pl-1 sm:w-1/3 xl:w-1/5">
                <a
                    href={mainLink[0]?.href}
                    aria-label={mainLink[0]?.text}
                    class="block"
                >
                    <Picture
                        src="https://malou-production.s3.eu-west-3.amazonaws.com/store-locator/organization/682afd5e5a0bdbeeaf109700/favicons/logo_white.png"
                        formats={['jpg']}
                        fallbackFormat="png"
                        alt="Logo white coffee"
                        inferSize
                        class="h-[100px] !w-[190px]"
                        densities={[1, 2, 3]}
                    />
                </a>
            </div>

            <div class="flex w-1/3 justify-end xl:w-2/5">
                <div class="font-secondary relative">
                    <div
                        class="mr-3 hidden items-center justify-center gap-3 xl:flex"
                    >
                        {
                            languages.map((language) => (
                                <div
                                    id={`language-${language.key}`}
                                    class="font-tertiary flex h-[16px] cursor-pointer items-center justify-center text-[13px] text-white/50"
                                >
                                    {language.value}
                                </div>
                            ))
                        }
                    </div>
                </div>
            </div>
        </div>

        <!-- Mobile Menu Button -->

        <div
            id="mobile-menu"
            class="transition-left fixed top-0 left-[-100%] h-[100vh] w-full overflow-scroll bg-white px-6 pb-6 shadow-lg duration-300 ease-in-out"
        >
            <div class="flex w-full justify-start">
                <button
                    aria-label="Toggle Menu"
                    id="close-mobile-menu-button"
                    class="ga-4 flex w-1/3 items-center justify-start"
                >
                    <svg
                        aria-hidden="true"
                        id="close-icon"
                        role="presentation"
                        class="fill-tertiary h-5 w-5"
                        viewBox="0 0 1000 1000"
                        xmlns="http://www.w3.org/2000/svg"
                        ><path
                            d="M742 167L500 408 258 167C246 154 233 150 217 150 196 150 179 158 167 167 154 179 150 196 150 212 150 229 154 242 171 254L408 500 167 742C138 771 138 800 167 829 196 858 225 858 254 829L496 587 738 829C750 842 767 846 783 846 800 846 817 842 829 829 842 817 846 804 846 783 846 767 842 750 829 737L588 500 833 258C863 229 863 200 833 171 804 137 775 137 742 167Z"
                        ></path></svg
                    >
                    <span
                        class="font-secondary text-tertiary text-[12px] uppercase"
                    >
                        Menu</span
                    >
                </button>

                <a
                    href={mainLink[0]?.href}
                    aria-label={mainLink[0]?.text}
                    class="flex w-2/3 justify-start"
                >
                    <Picture
                        src="https://malou-production.s3.eu-west-3.amazonaws.com/store-locator/organization/682afd5e5a0bdbeeaf109700/favicons/logo_black.png"
                        formats={['jpg']}
                        fallbackFormat="png"
                        alt="Logo white coffee"
                        inferSize
                        class="h-[90px] !w-[190px]"
                        densities={[1, 2, 3]}
                    />
                </a>
            </div>
            <div class="mt-4 flex flex-col">
                <div class="px-4">
                    {
                        menuLinks.map((link) => (
                            <div class="mb-4">
                                <a
                                    href={link.href}
                                    class="font-secondary text-tertiary block text-start text-[30px] font-normal"
                                    aria-label={link.text}
                                >
                                    {link.text}
                                </a>
                            </div>
                        ))
                    }
                </div>

                <div class="flex w-full items-center gap-7 px-4 py-7">
                    {
                        languages.map((language) => (
                            <a
                                id={`language-${language.key}-mobile`}
                                data-key={language.key}
                                href={language.url}
                                class="text-tertiary/50 text-[20px] font-normal"
                            >
                                {language.value}
                            </a>
                        ))
                    }
                </div>

                <a
                    class="flex items-center gap-1 px-4 py-7"
                    href="https://www.instagram.com/drink.white/"
                    target="_blank"
                >
                    <svg
                        class="h-7 w-7"
                        fill="#000000"
                        viewBox="0 0 32 32"
                        version="1.1"
                        xml:space="preserve"
                        xmlns="http://www.w3.org/2000/svg"
                        xmlns:xlink="http://www.w3.org/1999/xlink"
                        ><g stroke-width="0"></g><g
                            stroke-linecap="round"
                            stroke-linejoin="round"></g><g>
                            <g>
                                <path
                                    d="M22.3,8.4c-0.8,0-1.4,0.6-1.4,1.4c0,0.8,0.6,1.4,1.4,1.4c0.8,0,1.4-0.6,1.4-1.4C23.7,9,23.1,8.4,22.3,8.4z"
                                ></path>
                                <path
                                    d="M16,10.2c-3.3,0-5.9,2.7-5.9,5.9s2.7,5.9,5.9,5.9s5.9-2.7,5.9-5.9S19.3,10.2,16,10.2z M16,19.9c-2.1,0-3.8-1.7-3.8-3.8 c0-2.1,1.7-3.8,3.8-3.8c2.1,0,3.8,1.7,3.8,3.8C19.8,18.2,18.1,19.9,16,19.9z"
                                ></path>
                                <path
                                    d="M20.8,4h-9.5C7.2,4,4,7.2,4,11.2v9.5c0,4,3.2,7.2,7.2,7.2h9.5c4,0,7.2-3.2,7.2-7.2v-9.5C28,7.2,24.8,4,20.8,4z M25.7,20.8 c0,2.7-2.2,5-5,5h-9.5c-2.7,0-5-2.2-5-5v-9.5c0-2.7,2.2-5,5-5h9.5c2.7,0,5,2.2,5,5V20.8z"
                                ></path>
                            </g>
                        </g></svg
                    ><div class="font-secondary text-[25px]">instagram</div>
                </a>
            </div>
        </div>
    </header>

    <script is:inline define:vars={{ languages, initLanguage: defaultLanguage }}
    >
        // Mobile menu functionality
        const openMobileMenuButton = document.getElementById(
            'open-mobile-menu-button',
        );
        const closeMobileMenuButton = document.getElementById(
            'close-mobile-menu-button',
        );

        const mobileMenu = document.getElementById('mobile-menu');

        openMobileMenuButton?.addEventListener('click', (event) => {
            event.preventDefault();
            mobileMenu?.classList.toggle('left-[-100%]');
            mobileMenu?.classList.toggle('left-0');
        });

        closeMobileMenuButton?.addEventListener('click', (event) => {
            event.preventDefault();
            mobileMenu?.classList.toggle('left-[-100%]');
            mobileMenu?.classList.toggle('left-0');
        });

        // sub-links functionality for mobile menu
        const menuLinks = Array.from(
            document.querySelectorAll('[id^="mobile-link-sub-links-control-"]'),
        );

        menuLinks.forEach((menuLink, index) => {
            const subLinks = document.getElementById(
                `mobile-link-sub-links-${index}`,
            );
            menuLink.addEventListener('click', (event) => {
                event.preventDefault();
                menuLink.classList.toggle('rotate-180');

                subLinks?.classList.toggle('hidden');
                subLinks?.classList.toggle('block');
            });
        });

        // Language selector functionality
        let selectedLanguage = initLanguage;

        initLanguageItems();
        toggleLanguagesItems();

        function initLanguageItems() {
            languages.forEach((language) => {
                const languageItem = document.getElementById(
                    `language-${language.key}`,
                );
                const languageItemMobile = document.getElementById(
                    `language-${language.key}-mobile`,
                );

                if (languageItem && selectedLanguage === language.key) {
                    languageItem.classList.toggle('text-white');
                    languageItem.classList.toggle('text-white/50');
                }

                if (languageItemMobile && selectedLanguage === language.key) {
                    languageItemMobile.classList.toggle('text-tertiary');
                    languageItemMobile.classList.toggle('text-tertiary/50');
                }
            });
        }

        function toggleLanguagesItems() {
            languages.forEach((language) => {
                const languageLink = document.getElementById(
                    `language-${language.key}`,
                );
                const languageLinkMobile = document.getElementById(
                    `language-${language.key}-mobile`,
                );

                if (languageLink) {
                    languageLink.addEventListener('click', (e) => {
                        e.preventDefault();
                        onLanguageSelected(language.key);
                    });
                }

                if (languageLinkMobile) {
                    languageLinkMobile.addEventListener('click', (e) => {
                        e.preventDefault();
                        onLanguageSelected(language.key);
                    });
                }
            });
        }

        function onLanguageSelected(languageKey) {
            const relativePath = languages.find(
                (lang) => lang.key === languageKey,
            )?.url;
            if (relativePath) {
                window.location.replace(
                    window.location.origin + `/${relativePath}`,
                );
            }
        }
    </script>
</div>
