import { NgClass, NgTemplateOutlet } from '@angular/common';
import { Component, computed, inject, signal } from '@angular/core';
import { MatIconModule } from '@angular/material/icon';
import { ActivatedRoute, Router } from '@angular/router';
import { TranslateModule } from '@ngx-translate/core';
import { firstValueFrom } from 'rxjs';

import { DiagnosticDto } from '@malou-io/package-dto';
import { HeapEventName, MaloupeEventName } from '@malou-io/package-utils';

import { DiagnosticHttpService } from ':core/http-services/diagnostic.http-service';
import {
    LoaderStep,
    LoaderStepStatus,
    StepType,
} from ':modules/aggregated-restaurants-diagnostics-loader/aggregated-restaurants-diagnostics-loader.interface';
import { DiagnosticHeaderComponent } from ':shared/components/diagnostic-header/diagnostic-header.component';
import { MalouSpinnerComponent } from ':shared/components/spinner/malou-spinner.component';
import { SvgIcon } from ':shared/enums';
import { ApplyPurePipe, EnumTranslatePipe, Illustration, IllustrationPathResolverPipe } from ':shared/pipes';
import { getUserIdentityFromLocalStorage, isMalouUserIdentityInLocalStorage } from ':shared/utils/manage-user-uuid';

@Component({
    selector: 'app-aggregated-restaurants-diagnostics-loader',
    templateUrl: './aggregated-restaurants-diagnostics-loader.component.html',
    styleUrls: ['./aggregated-restaurants-diagnostics-loader.component.scss'],
    imports: [
        NgClass,
        NgTemplateOutlet,
        MatIconModule,
        TranslateModule,
        DiagnosticHeaderComponent,
        MalouSpinnerComponent,
        ApplyPurePipe,
        EnumTranslatePipe,
        IllustrationPathResolverPipe,
    ],
})
export class AggregatedRestaurantsDiagnosticsLoaderComponent {
    private readonly _router = inject(Router);
    private readonly _activatedRoute = inject(ActivatedRoute);
    private readonly _diagnosticHttpService = inject(DiagnosticHttpService);

    readonly Illustration = Illustration;
    readonly SvgIcon = SvgIcon;
    readonly LoaderStepStatus = LoaderStepStatus;
    readonly GOOGLE_STEP = {
        type: StepType.GOOGLE,
        stepAction: async (diagnosticId: string): Promise<DiagnosticDto> => {
            const res = await Promise.all([
                firstValueFrom(this._diagnosticHttpService.updateWithKeywords$(diagnosticId)),
                firstValueFrom(this._diagnosticHttpService.updateWithReviewAnalyses$(diagnosticId)),
                firstValueFrom(this._diagnosticHttpService.getRestaurantInconsistencies$(diagnosticId)),
            ]);
            return res[res.length - 1];
        },
        illustration: Illustration.OK_HAND,
    };

    readonly PLATFORMS_STEP = {
        type: StepType.OTHER_PLATFORMS,
        stepAction: async (diagnosticId: string): Promise<DiagnosticDto> =>
            await firstValueFrom(this._diagnosticHttpService.updateWithSimilarRestaurants$(diagnosticId)),
        illustration: Illustration.COMPUTER,
    };

    readonly COMPETITORS_STEP = {
        type: StepType.COMPETITORS,
        stepAction: async (diagnosticId: string): Promise<DiagnosticDto> => {
            const res = await Promise.all([
                firstValueFrom(this._diagnosticHttpService.updateWithGoogleDiagnostic$(diagnosticId)),
                firstValueFrom(this._diagnosticHttpService.updateWithAverageReviewCountForSimilarRestaurants$(diagnosticId)),
            ]);
            return res[res.length - 1];
        },
        illustration: Illustration.BINOCULARS,
    };

    readonly INSTAGRAM_STEP = {
        type: StepType.INSTAGRAM,
        stepAction: async (diagnosticId: string): Promise<DiagnosticDto> =>
            await firstValueFrom(this._diagnosticHttpService.updateWithInstagramRating$(diagnosticId)),
        illustration: Illustration.MAN,
    };

    readonly STEPS: LoaderStep[] = [];
    readonly diagnostics = signal<DiagnosticDto[]>([]);
    readonly completedStepIndex = signal(0);
    readonly currentStepIndex = computed(() => Math.floor(this.completedStepIndex() / this.diagnostics().length));
    readonly step1DoneSignals = signal<Promise<void>[]>([]);
    readonly step1Resolvers = signal<(() => void)[]>([]);

    readonly currentSteps = computed(() => this.STEPS);

    readonly currentIllustration = computed(() => {
        const currentStepIndex = this.currentStepIndex();
        return currentStepIndex < this.STEPS.length
            ? this.STEPS[currentStepIndex].illustration
            : this.STEPS[this.STEPS.length - 1]?.illustration;
    });

    readonly hasStartedDiagnosticComputation = computed(() => this.diagnostics().length > 0);
    private readonly _WAIT_BEFORE_REDIRECT = 1000; // for design purpose

    /**
     * Query params for hubspot form
     */
    private _utmSource = '';
    private _utmMedium = '';
    private _utmCampaign = '';
    private _firstName: string | null = null;
    private _lastName: string | null = null;
    private _email: string | null = null;
    private _phoneNumber: string | null = null;
    private _locationCount: string | null = null;
    private _eventName: MaloupeEventName | null = null;

    constructor() {
        this._activatedRoute.paramMap.subscribe((params) => {
            const diagnosticIdsParams = params.get('diagnostic_ids');
            if (diagnosticIdsParams) {
                const diagnosticIds = JSON.parse(diagnosticIdsParams);
                if (diagnosticIds.length !== 0) {
                    this._diagnosticHttpService.getDiagnosticsByIds$(diagnosticIds).subscribe(({ diagnostics }) => {
                        this.diagnostics.set([...diagnostics]);
                        const foundDiagnosticIds = diagnostics.map((diagnostic) => diagnostic.id);
                        this._startDiagnosticComputation(foundDiagnosticIds);
                    });
                    this._diagnosticHttpService
                        .trackDiagnosticAction$({
                            identity: getUserIdentityFromLocalStorage() ?? '',
                            eventName: HeapEventName.MALOUPE_TRACKING_GET_DIAGNOSTIC_ACTION,
                            diagnosticIds: diagnosticIds.join(','),
                        })
                        .subscribe();
                }
            }
        });
        this._setQueryParams();
    }

    getStepStatus(stepIndex: number, currentStepIndex: number): LoaderStepStatus {
        if (stepIndex < currentStepIndex) {
            return LoaderStepStatus.DONE;
        }
        if (stepIndex === currentStepIndex) {
            return LoaderStepStatus.CURRENT;
        }
        return LoaderStepStatus.TO_BE_DONE;
    }

    private _setQueryParams(): void {
        this._activatedRoute.queryParamMap.subscribe((queryParams) => {
            this._utmSource = queryParams.get('utm_source') ?? '';
            this._utmMedium = queryParams.get('utm_medium') ?? '';
            this._utmCampaign = queryParams.get('utm_campaign') ?? '';
            this._firstName = queryParams.get('first_name') ?? null;
            this._lastName = queryParams.get('last_name') ?? null;
            this._email = queryParams.get('email') ?? null;
            this._phoneNumber = queryParams.get('phone_number') ?? null;
            this._locationCount = queryParams.get('location_count') ?? '';
            this._eventName = queryParams.get('event_name') as MaloupeEventName;
        });
    }

    private async _startDiagnosticComputation(diagnosticIds: string[]): Promise<void> {
        if (diagnosticIds.length === 0) {
            return;
        }
        this._initSteps();
        this._initResolversAndSignals(diagnosticIds.length);
        // First diagnostic can start immediately
        this._startDiagnosticFirstStep(0);

        // Start all diagnostics in parallel, but stagger step 1
        const allPromises = diagnosticIds.map((_, i) =>
            (async () => {
                const diagnosticId = diagnosticIds[i];
                await this._waitSignalToStartFirstStep(i);

                await this._startNextStepForDiagnostic(0, diagnosticId);

                // Once step 1 is done, signal next diagnostic to start step 1
                if (i + 1 < diagnosticIds.length) {
                    this._startDiagnosticFirstStep(i + 1);
                }

                // Competitors step needs to be done before instagram and google step
                await this._startNextStepForDiagnostic(1, diagnosticId);

                for (let stepIndex = 2; stepIndex < this.STEPS.length; stepIndex++) {
                    await this._startNextStepForDiagnostic(stepIndex, diagnosticId);
                }
            })()
        );

        await Promise.all(allPromises);
        setTimeout(() => {
            if (diagnosticIds.length !== 0) {
                this._openDiagnosticForm(diagnosticIds);
            }
        }, this._WAIT_BEFORE_REDIRECT);
    }

    private _initSteps(): void {
        this.STEPS.length = 0;
        const steps: LoaderStep[] = [this.GOOGLE_STEP, this.PLATFORMS_STEP, this.COMPETITORS_STEP];
        const hasOneDiagnosticWithInstagram = this.diagnostics().some((diagnostic) => !!diagnostic.instagramPage?.name);
        if (hasOneDiagnosticWithInstagram) {
            steps.push(this.INSTAGRAM_STEP);
        }
        this.STEPS.push(...steps);
    }

    private _initResolversAndSignals(diagnosticCount: number): void {
        const step1DoneSignals: Array<Promise<void>> = [];
        const step1Resolvers: Array<() => void> = [];
        for (let i = 0; i < diagnosticCount; i++) {
            let resolver: () => void;
            const promise = new Promise<void>((resolve) => {
                resolver = resolve;
            });
            // @ts-expect-error
            step1Resolvers.push(resolver);
            step1DoneSignals.push(promise);
        }
        this.step1Resolvers.set([...step1Resolvers]);
        this.step1DoneSignals.set([...step1DoneSignals]);
    }

    private _startDiagnosticFirstStep(index: number): void {
        const step1Resolvers = this.step1Resolvers();
        if (index < step1Resolvers.length) {
            step1Resolvers[index]();
        }
    }

    private _waitSignalToStartFirstStep(index: number): Promise<void> {
        const step1DoneSignals = this.step1DoneSignals();
        if (index < step1DoneSignals.length) {
            return step1DoneSignals[index];
        }
        return Promise.resolve();
    }

    private async _startNextStepForDiagnostic(stepIndex: number, diagnosticId: string): Promise<void> {
        await this.STEPS[stepIndex].stepAction(diagnosticId);
        this.completedStepIndex.set(this.completedStepIndex() + 1);
    }

    private _openDiagnosticForm(diagnosticIds: string[]): void {
        if (this._eventName) {
            this._diagnosticHttpService.updateWithEventName$(diagnosticIds[0], this._eventName).subscribe({
                next: () => {
                    this._navigateToDiagnosticForm(diagnosticIds);
                },
            });
        } else {
            this._navigateToDiagnosticForm(diagnosticIds);
        }
    }

    private _navigateToDiagnosticForm(diagnosticIds: string[]): void {
        if (isMalouUserIdentityInLocalStorage()) {
            this._router.navigate(['diagnostic'], {
                queryParams: {
                    utm_source: this._utmSource,
                    utm_medium: this._utmMedium,
                    utm_campaign: this._utmCampaign,
                    diagnostic_id: diagnosticIds,
                },
            });
            return;
        }
        const userInfoParams = this._areAllInformationValid()
            ? {
                  first_name: this._firstName,
                  last_name: this._lastName,
                  email: this._email,
                  phone_number: this._phoneNumber,
                  location_count: this._locationCount,
              }
            : {};
        this._router.navigate(['form'], {
            queryParams: {
                utm_source: this._utmSource,
                utm_medium: this._utmMedium,
                utm_campaign: this._utmCampaign,
                diagnostic_id: diagnosticIds,
                ...userInfoParams,
            },
        });
    }

    private _areAllInformationValid(): boolean {
        return !!this._firstName && !!this._lastName && !!this._email && !!this._phoneNumber && !!this._locationCount;
    }
}
