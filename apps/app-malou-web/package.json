{"author": {"email": "<EMAIL>", "name": "<PERSON><PERSON>"}, "dependencies": {"@angular/animations": "^20.0.5", "@angular/cdk": "^20.0.4", "@angular/common": "^20.0.5", "@angular/compiler": "^20.0.5", "@angular/core": "^20.0.5", "@angular/forms": "^20.0.5", "@angular/material": "^20.0.4", "@angular/material-luxon-adapter": "^20.0.4", "@angular/platform-browser": "^20.0.5", "@angular/platform-browser-dynamic": "^20.0.5", "@angular/router": "^20.0.5", "@angular/service-worker": "^20.0.5", "@auth0/angular-jwt": "^5.2.0", "@casl/ability": "^5.4.3", "@ctrl/ngx-emoji-mart": "^9.2.0", "@customerio/cdp-analytics-browser": "^0.2.1", "@growthbook/growthbook": "^0.36.0", "@malou-io/package-dto": "workspace:*", "@malou-io/package-utils": "workspace:*", "@ngrx/component": "19.2.1", "@ngrx/effects": "19.2.1", "@ngrx/entity": "19.2.1", "@ngrx/operators": "^19.2.1", "@ngrx/signals": "^19.2.1", "@ngrx/store": "19.2.1", "@ngrx/store-devtools": "19.2.1", "@ngx-translate/core": "^15.0.0", "@ngx-translate/http-loader": "^8.0.0", "@pqina/angular-pintura": "^9.0.4", "@pqina/pintura": "^8.94.1", "@sentry/angular": "^9.30.0", "@uppy/aws-s3": "^3.6.2", "@uppy/core": "^3.11.3", "ang-jsoneditor": "^1.10.5", "angular-mentions": "~1.5.0", "autolinker": "^3.15.0", "canvas-confetti": "^1.9.2", "chart.js": "^4.1.2", "chartjs-adapter-luxon": "^1.3.0", "chartjs-plugin-annotation": "^2.1.1", "compass-mixins": "^0.12.10", "cropperjs": "^1.6.2", "dotenv": "^8.6.0", "file-saver": "^2.0.5", "file-type": "^16.5.3", "install": "^0.13.0", "jquery": "^3.6.0", "jsoneditor": "^9.4.1", "jszip": "^3.7.1", "libphonenumber-js": "^1.11.8", "lodash": "^4.17.21", "luxon": "^3.4.4", "modern-screenshot": "^4.4.38", "ng-lazyload-image": "^9.1.0", "ng2-charts": "^4.1.1", "ngx-clipboard": "^16.0.0", "ngx-color-picker": "^14.0.0", "ngx-drag-to-select": "^5.0.1", "ngx-image-cropper": "^6.2.2", "ngx-infinite-scroll": "^20.0.0", "ngx-skeleton-loader": "^5.0.0", "object-hash": "^3.0.0", "path-browserify": "^1.0.1", "pusher-js": "^7.0.6", "qrcode": "^1.5.3", "rxjs": "^7.4.0", "source-map-support": "^0.5.21", "tslib": "^2.8.1", "type-fest": "^4.26.1", "uuid": "^10.0.0", "xlsx": "^0.17.0", "zone.js": "^0.15.0"}, "devDependencies": {"@angular-devkit/architect": "0.2000.4", "@angular-devkit/build-angular": "^20.0.4", "@angular-devkit/core": "20.0.4", "@angular-eslint/eslint-plugin": "^16.2.0", "@angular/cli": "^20.0.4", "@angular/compiler-cli": "^20.0.5", "@sentry/cli": "^2.32.1", "@sentry/types": "^7.113.0", "@tailwindcss/container-queries": "^0.1.1", "@trivago/prettier-plugin-sort-imports": "^5.2.0", "@types/bcrypt-nodejs": "0.0.31", "@types/dotenv": "^6.1.1", "@types/express": "^4.17.11", "@types/file-saver": "^2.0.5", "@types/googlemaps": "3.39.2", "@types/jasmine": "~4.3.5", "@types/jasminewd2": "^2.0.9", "@types/lodash": "^4.14.169", "@types/luxon": "^3.4.2", "@types/node": "^22.15.0", "@types/object-hash": "^3.0.6", "@types/qrcode": "^1.5.2", "@types/uuid": "^10.0.0", "@types/webpack-env": "^1.18.0", "@typescript-eslint/eslint-plugin": "^6.2.0", "@typescript-eslint/eslint-plugin-tslint": "^6.2.0", "@typescript-eslint/parser": "^6.2.0", "buffer": "^6.0.3", "eslint": "^8.48.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-import": "^2.28.1", "eslint-plugin-rxjs": "^5.0.2", "jsdom": "^26.1.0", "madge": "^8.0.0", "ngx-translate-testing": "^7.0.0", "prettier": "^3.5.3", "prettier-plugin-organize-attributes": "^1.0.0", "prettier-plugin-tailwindcss": "^0.6.2", "style-loader": "^3.3.1", "tailwindcss": "^3.2.0", "ts-node": "~9.1.1", "tslint": "~6.1.0", "typescript": "~5.8.3", "utility-types": "^3.11.0", "vitest": "^3.1.4", "webpack": "5.74.0", "webpack-bundle-analyzer": "^4.8.0"}, "license": "MIT", "name": "@malou-io/app-web", "private": true, "repository": {"url": ""}, "scripts": {"analyze:deps": "madge --extensions ts --circular --warning --ts-config tsconfig.json --exclude '^.*\\.spec\\.ts$' src/", "build": "node --max_old_space_size=8192 ./node_modules/@angular/cli/bin/ng build --aot", "build-clean": "rm -rf .angular && rm -rf .turbo && rm -f tsconfig.tsbuildinfo", "build-development": "node --max_old_space_size=8192 ./node_modules/@angular/cli/bin/ng build --aot --configuration=development", "build-free": "ng build --configuration=free", "build-production": "node --max_old_space_size=8192 ./node_modules/@angular/cli/bin/ng build --aot --configuration=production", "build-staging": "node --max_old_space_size=8192 ./node_modules/@angular/cli/bin/ng build --aot --configuration=staging", "eslint": "eslint .", "format": "prettier --write \"**/*.{ts,html,scss,json}\"", "format:check": "prettier --check \"**/*.{ts,html,scss,json}\"", "lint": "eslint --color -c .eslintrc.js --ext .ts ./src/app", "lint-fix": "eslint \"**/*.ts\" \"**/*.js\" --fix", "lint-modified-files": "git status --porcelain -uall | grep -v D | cut -c4- | cut -d '>' -f 3 | grep -E '*.ts?$' | xargs eslint --color -c .eslintrc.js --ext .ts", "lint-staged": "lint-staged --no-stash", "preinstall": "npx only-allow pnpm", "sentry:sourcemaps": "sentry-cli sourcemaps inject --org ${SENTRY_ORG} --project ${SENTRY_PROJECT} --release ${SENTRY_RELEASE} ./dist && sentry-cli sourcemaps upload --org ${SENTRY_ORG} --project ${SENTRY_PROJECT} --release ${SENTRY_RELEASE} ./dist", "start": "node --max_old_space_size=6144 ./node_modules/@angular/cli/bin/ng serve --host 0.0.0.0", "start-dev": "node --max-old-space-size=6144 ./node_modules/@angular/cli/bin/ng serve --host 0.0.0.0", "start-local": "node --max-old-space-size=6144 ./node_modules/@angular/cli/bin/ng serve --host 0.0.0.0", "start-production": "node --max-old-space-size=6144 ./node_modules/@angular/cli/bin/ng serve --host 0.0.0.0", "start-staging": "node --max-old-space-size=6144 ./node_modules/@angular/cli/bin/ng serve --host 0.0.0.0", "stats": "webpack-bundle-analyzer dist/stats.json", "test:unit:angular": "ng test --watch=false", "test:watch": "ng test --watch"}, "version": "0.0.0"}