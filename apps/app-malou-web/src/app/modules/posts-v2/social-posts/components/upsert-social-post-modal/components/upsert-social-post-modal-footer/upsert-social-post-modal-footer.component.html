<app-upsert-generic-post-modal-footer
    [(selectedOption)]="selectedOption"
    [isDisabled]="isDisabled()"
    [isSubmitting]="isSubmitting()"
    [selectedDate]="selectedDate()"
    [postErrors]="postErrors()"
    [willDuplicate]="willDuplicate()"
    [isStory]="false"
    [cancelBtnTestId]="'cancel-upsert-social-post-btn'"
    [programmedPostPlatformKeys]="programmedPostPlatformKeys()"
    (cancel)="cancel.emit()"
    (savePost)="savePost.emit($event)"
    (selectedDateChange)="onSelectedDateChange($event)"></app-upsert-generic-post-modal-footer>
