import { ChangeDetectionStrategy, Component, computed, effect, inject, input, output, Signal, signal, WritableSignal } from '@angular/core';
import { FormControl, ReactiveFormsModule } from '@angular/forms';
import { MatIconModule } from '@angular/material/icon';
import { MatTooltipModule } from '@angular/material/tooltip';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { LazyLoadImageModule } from 'ng-lazyload-image';

import { capitalize, PlatformKey } from '@malou-io/package-utils';

import {
    CallToActionOptionKey,
    CtaBlockContentFormInputValidation,
    EditStorePageCallToActionUrlOption,
} from ':modules/store-locator/edit-store-locator-page/blocks/shared/call-to-action/call-to-action.interface';
import { SlideToggleComponent } from ':shared/components-v3/slide-toggle/slide-toggle.component';
import { DefaultErrorMessage, InputTextComponent } from ':shared/components/input-text/input-text.component';
import { SelectComponent } from ':shared/components/select/select.component';
import { ApplyPurePipe } from ':shared/pipes/apply-fn.pipe';

@Component({
    selector: 'app-store-locator-edit-page-call-to-action',
    templateUrl: './call-to-action.component.html',
    styleUrls: ['./call-to-action.component.scss'],
    imports: [
        LazyLoadImageModule,
        MatIconModule,
        MatTooltipModule,
        TranslateModule,
        SelectComponent,
        SlideToggleComponent,
        ApplyPurePipe,
        InputTextComponent,
        ReactiveFormsModule,
    ],
    changeDetection: ChangeDetectionStrategy.OnPush,
})
export class EditStoreLocatorPageCallToActionComponent {
    readonly ctaTextControl = input.required<FormControl>();
    readonly ctaUrlControl = input.required<FormControl>();
    readonly urlOptions = input<EditStorePageCallToActionUrlOption[]>([]);
    readonly title = input<string>('');
    readonly canBeDisabled = input<boolean>(false);
    readonly disabled = input<boolean>(false);

    readonly onCtaGlobalToggle = output<boolean>();

    selectorControl: FormControl<EditStorePageCallToActionUrlOption | null> = new FormControl<EditStorePageCallToActionUrlOption | null>(
        null,
        {
            nonNullable: true,
        }
    );

    private readonly _translateService = inject(TranslateService);

    readonly DefaultErrorMessage = DefaultErrorMessage;

    readonly CtaBlockContentFormInputValidation = CtaBlockContentFormInputValidation;

    readonly isCtaEnabled: WritableSignal<boolean> = signal(true);

    readonly selectedUrlOption = signal<EditStorePageCallToActionUrlOption | null>(null);

    readonly isCustomUrlOptionSelected = computed(() => this.selectedUrlOption()?.key === CallToActionOptionKey.CUSTOM);

    readonly urlOptionsToShow: Signal<EditStorePageCallToActionUrlOption[]> = computed(() => [
        ...this.urlOptions(),
        {
            key: CallToActionOptionKey.CUSTOM,
            value: '',
        },
    ]);

    constructor() {
        effect(() => {
            if (this.ctaUrlControl().enabled) {
                const selectedOption = this.urlOptionsToShow().find((option) => option.value === this.ctaUrlControl().value);
                if (selectedOption) {
                    this.selectedUrlOption.set(selectedOption);
                    this.selectorControl = new FormControl<EditStorePageCallToActionUrlOption | null>(selectedOption || null);
                } else {
                    this.selectedUrlOption.set({
                        key: CallToActionOptionKey.CUSTOM,
                        value: this.ctaUrlControl().value || '',
                    });
                    this.selectorControl = new FormControl<EditStorePageCallToActionUrlOption | null>({
                        key: CallToActionOptionKey.CUSTOM,
                        value: this.ctaUrlControl().value || '',
                    });
                }
                this.selectorControl.enable();
                this.isCtaEnabled.set(true);
            } else {
                this.selectedUrlOption.set(this.urlOptionsToShow()[0] || null);
                this.selectorControl.patchValue(this.urlOptionsToShow()[0] || null);
                this.ctaTextControl().patchValue(this.displayUrlOptionWith(this.urlOptionsToShow()[0]));
                this.ctaUrlControl().patchValue(this.urlOptionsToShow()[0]?.value || '');
                this.isCtaEnabled.set(false);
                this.selectorControl.disable();
            }
        });
    }

    onCtaToggle(): void {
        this.onCtaGlobalToggle.emit(!this.isCtaEnabled());
        this.isCtaEnabled.set(!this.isCtaEnabled());
        if (this.canBeDisabled()) {
            if (this.isCtaEnabled()) {
                this.ctaTextControl().enable();
                this.ctaUrlControl().enable();
                this.selectorControl.enable();
            } else {
                this.ctaTextControl().disable();
                this.ctaUrlControl().disable();
                this.selectorControl.disable();
            }
        }
    }

    displayUrlOptionWith = (option: EditStorePageCallToActionUrlOption): string => {
        switch (option.key) {
            case CallToActionOptionKey.CUSTOM:
            case CallToActionOptionKey.ORDER_URL:
            case CallToActionOptionKey.RESERVATION_URL:
            case CallToActionOptionKey.MENU_URL:
            case CallToActionOptionKey.ITINERARY:
            case CallToActionOptionKey.WEBSITE:
                return this._translateService.instant(`store_locator.edit_modal.controls.cta.enum.${option.key}`);
            default:
                if (Object.values(PlatformKey).includes(option.key as PlatformKey)) {
                    return this._translateService.instant(`enums.platform_key.${option.key}`);
                }
                return capitalize(option.key);
        }
    };

    onSelectChange(selectedOption: EditStorePageCallToActionUrlOption): void {
        this.selectedUrlOption.set(selectedOption);
        if (selectedOption.key !== CallToActionOptionKey.CUSTOM) {
            this.ctaUrlControl().setValue(selectedOption.value);
        }
    }
}
