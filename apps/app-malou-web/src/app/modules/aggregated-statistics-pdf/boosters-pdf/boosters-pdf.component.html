<div class="flex flex-col gap-12">
    <div class="my-6 flex flex-col items-center justify-center gap-2 px-4">
        <span class="malou-text-20--bold malou-color-text-1">
            {{ 'aggregated_statistics_pdf.boosters.title' | translate }}
        </span>
        <span class="malou-text-11--regular malou-color-text-2 italic">{{ { startDate, endDate } | fromToDateFormatter }}</span>
        <span class="malou-text-11--regular malou-color-text-2 text-center italic">
            {{ selectedRestaurantsTitle$ | async | statisticsPdfRestaurantsFormatter: true }}
        </span>
    </div>

    @if (boostersAggregatedScanCountHasData()) {
        <div class="flex flex-col gap-4 px-8.5 py-4">
            @if ((displayedCharts | includes: InsightsChart.AGGREGATED_BOOSTERS_SCAN_COUNT) && boostersAggregatedScanCountHasData()) {
                <div class="break-inside-avoid">
                    <app-aggregated-boosters-scan-count-v2
                        [chartSortBy]="chartOptions[InsightsChart.AGGREGATED_BOOSTERS_SCAN_COUNT]?.chartSortBy"
                        [data]="boostersData()"
                        [restaurants]="restaurants()"
                        [isParentLoading]="isLoadingBoosters()"
                        [isParentError]="isErrorBoosters()"
                        [showSortByTextInsteadOfSelector]="true"
                        (hasDataChange)="boostersAggregatedScanCountHasData.set($event)">
                    </app-aggregated-boosters-scan-count-v2>
                </div>
            }
            @if (
                (displayedCharts | includes: InsightsChart.AGGREGATED_BOOSTERS_REVIEWS_COUNT) ||
                (displayedCharts | includes: InsightsChart.AGGREGATED_BOOSTERS_PRIVATE_REVIEWS_COUNT)
            ) {
                <div class="break-inside-avoid">
                    <app-aggregated-totems-estimated-review-count
                        [isParentLoading]="isLoadingTotemReviews()"
                        [isParentError]="isErrorTotemReviews()"
                        [data]="totemReviewsData()"
                        [reviewsChartSortBy]="chartOptions[InsightsChart.AGGREGATED_BOOSTERS_REVIEWS_COUNT]?.chartSortBy"
                        [privateReviewsChartSortBy]="chartOptions[InsightsChart.AGGREGATED_BOOSTERS_PRIVATE_REVIEWS_COUNT]?.chartSortBy"
                        [hidePrivateReviewsChart]="!(displayedCharts | includes: InsightsChart.AGGREGATED_BOOSTERS_PRIVATE_REVIEWS_COUNT)"
                        [hideReviewsPerPlatformChart]="!(displayedCharts | includes: InsightsChart.AGGREGATED_BOOSTERS_REVIEWS_COUNT)"
                        [restaurants]="restaurants()"
                        [showSortByTextInsteadOfSelector]="true"
                        [hasTotemsReviewsData]="hasTotemsReviewsData()"
                        [hasPrivateReviewsData]="hasPrivateReviewsData()">
                    </app-aggregated-totems-estimated-review-count>
                </div>
            }
            @if (
                atLeastOneBoosterPackActivated() &&
                (displayedCharts | includes: InsightsChart.AGGREGATED_BOOSTERS_WHEEL_OF_FORTUNE_ESTIMATED_REVIEWS_COUNT)
            ) {
                <ng-container [ngTemplateOutlet]="wheelOfFortuneStatisticsTemplate"></ng-container>
            }
        </div>
    }
</div>

<ng-template #wheelOfFortuneStatisticsTemplate>
    <div class="malou-text-18--bold text-malou-color-text-1">{{ 'aggregated_statistics.boosters.scans.wheel_of_fortune' | translate }}</div>
    @if (wheelOfFortuneAggregatedGiftsKpisHasData()) {
        <div class="break-inside-avoid">
            <app-aggregated-wheel-of-fortune-gifts-kpis-v2
                [data]="giftsData()"
                [isParentLoading]="isLoadingGifts()"
                [isParentError]="isErrorGifts()"
                (hasDataChange)="wheelOfFortuneAggregatedGiftsKpisHasData.set($event)">
            </app-aggregated-wheel-of-fortune-gifts-kpis-v2>
        </div>
    }

    <div class="flex flex-col gap-4 md:flex-col">
        @if (wheelOfFortuneAggregatedGiftsDistributionHasData()) {
            <div class="min-w-0 flex-1 break-inside-avoid">
                <app-aggregated-wheel-of-fortune-gifts-distribution-v2
                    class="flex"
                    [data]="giftsData()"
                    [restaurants]="(restaurantsWithBoosterPackActivated$ | async) ?? []"
                    [isParentLoading]="isLoadingGifts()"
                    [isParentError]="isErrorGifts()"
                    [tableSort]="chartOptions[InsightsChart.AGGREGATED_BOOSTERS_WHEEL_OF_FORTUNE_GIFTS_DISTRIBUTION]?.tableSortOptions">
                </app-aggregated-wheel-of-fortune-gifts-distribution-v2>
            </div>
        }
        @if (wheelOfFortuneAggregatedEstimatedReviewCountHasData()) {
            <div class="min-w-0 flex-1 break-inside-avoid">
                <app-aggregated-wheel-of-fortune-estimated-review-count-v2
                    class="flex"
                    [data]="wheelOfFortuneData()"
                    [restaurants]="(restaurantsWithBoosterPackActivated$ | async) ?? []"
                    [isParentLoading]="isLoadingBoosters()"
                    [isParentError]="isErrorBoosters()"
                    [tableSort]="chartOptions[InsightsChart.AGGREGATED_BOOSTERS_WHEEL_OF_FORTUNE_ESTIMATED_REVIEWS_COUNT]?.tableSortOptions"
                    (hasDataChange)="wheelOfFortuneAggregatedEstimatedReviewCountHasData.set($event)">
                </app-aggregated-wheel-of-fortune-estimated-review-count-v2>
            </div>
        }
    </div>
</ng-template>
