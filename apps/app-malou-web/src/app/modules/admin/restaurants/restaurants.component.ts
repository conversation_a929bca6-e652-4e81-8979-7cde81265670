import { NgTemplateOutlet, SlicePipe } from '@angular/common';
import { ChangeDetectionStrategy, Component, DestroyRef, inject, OnDestroy, OnInit, signal, ViewChild } from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatOptionModule } from '@angular/material/core';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatMenuModule } from '@angular/material/menu';
import { MatPaginator } from '@angular/material/paginator';
import { MatProgressBarModule } from '@angular/material/progress-bar';
import { MatSelectModule } from '@angular/material/select';
import { MatSortModule } from '@angular/material/sort';
import { MatTableDataSource, MatTableModule } from '@angular/material/table';
import { RouterLink } from '@angular/router';
import { Store } from '@ngrx/store';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { BehaviorSubject, combineLatest, distinctUntilChanged, forkJoin, Observable, of, Subject, switchMap } from 'rxjs';
import { map, startWith, tap } from 'rxjs/operators';

import { ApiResultV2 } from '@malou-io/package-utils';

import { DialogService } from ':core/services/dialog.service';
import { OrganizationsService } from ':core/services/organizations.service';
import { RestaurantsService } from ':core/services/restaurants.service';
import { ToastService } from ':core/services/toast.service';
import { ManagersListModalComponent } from ':modules/admin/restaurants/managers-list-modal/managers-list-modal.component';
import {
    RestaurantUpdateForm,
    UpdateRestaurantModalComponent,
} from ':modules/admin/restaurants/update-restaurant-modal/update-restaurant-modal.component';
import * as RestaurantsActions from ':modules/restaurant-list/restaurant-list.actions';
import * as UserActions from ':modules/user/store/user.actions';
import { selectUserInfos } from ':modules/user/store/user.selectors';
import { User, UserRestaurant } from ':modules/user/user';
import { SlideToggleComponent } from ':shared/components-v3/slide-toggle/slide-toggle.component';
import { DialogVariant } from ':shared/components/malou-dialog/malou-dialog.component';
import { PaginatorComponent } from ':shared/components/paginator/paginator.component';
import { SearchComponent } from ':shared/components/search/search.component';
import { SkeletonComponent } from ':shared/components/skeleton/skeleton.component';
import { TypeSafeMatCellDefDirective } from ':shared/directives/type-safe-mat-cell-def.directive';
import { TypeSafeMatRowDefDirective } from ':shared/directives/type-safe-mat-row-def.directive';
import { TrackByFunctionFactory } from ':shared/helpers/track-by-functions';
import { ApiResult, Restaurant } from ':shared/models';
import { SvgIcon } from ':shared/modules/svg-icon.enum';
import { ApplySelfPurePipe } from ':shared/pipes/apply-fn.pipe';
import { HttpErrorPipe } from ':shared/pipes/http-error.pipe';
import { CustomDialogService } from ':shared/services/custom-dialog.service';

enum DetailedRestaurantsTableFieldName {
    NAME = 'name',
    ORGANIZATION = 'organization',
    ADDRESS = 'address',
    MANAGERS = 'managers',
    FUNCTIONALITIES = 'functionalities',
    ACTIVE = 'active',
    IS_MANAGED_BY_LOGGED_USER = 'isManagedByLoggedUser',
    ID = 'id',
    ACTIONS = 'actions',
}

@Component({
    selector: 'app-restaurants',
    templateUrl: './restaurants.component.html',
    styleUrls: ['./restaurants.component.scss'],
    imports: [
        NgTemplateOutlet,
        SearchComponent,
        SlideToggleComponent,
        FormsModule,
        MatButtonModule,
        MatFormFieldModule,
        MatIconModule,
        MatInputModule,
        MatTableModule,
        MatMenuModule,
        MatOptionModule,
        MatOptionModule,
        MatProgressBarModule,
        MatSelectModule,
        MatSortModule,
        ReactiveFormsModule,
        TranslateModule,
        TypeSafeMatCellDefDirective,
        TypeSafeMatRowDefDirective,
        SlicePipe,
        ApplySelfPurePipe,
        RouterLink,
        SkeletonComponent,
        PaginatorComponent,
    ],
    changeDetection: ChangeDetectionStrategy.OnPush,
})
export class RestaurantsComponent implements OnInit, OnDestroy {
    readonly SvgIcon = SvgIcon;

    private readonly _store = inject(Store);
    private readonly _translate = inject(TranslateService);
    private readonly _httpErrorPipe = inject(HttpErrorPipe);
    private readonly _restaurantsService = inject(RestaurantsService);
    private readonly _customDialogService = inject(CustomDialogService);
    private readonly _dialogService = inject(DialogService);
    private readonly _toastService = inject(ToastService);
    private readonly _organizationsService = inject(OrganizationsService);
    private readonly _destroyRef = inject(DestroyRef);

    dataSource: MatTableDataSource<Restaurant> = new MatTableDataSource<Restaurant>([]);
    displayedColumns: string[] = Object.values(DetailedRestaurantsTableFieldName);
    _paginator: MatPaginator | null = null;

    readonly DetailedRestaurantsTableFieldName = DetailedRestaurantsTableFieldName;
    readonly currentUser = signal<User | null>(null);
    readonly userRestaurants$: Observable<UserRestaurant[]> = this._store.select(selectUserInfos).pipe(
        tap((user) => this.currentUser.set(user)),
        map((user: User) => user.restaurants)
    );
    readonly refresh$: Subject<void> = new Subject<void>();
    readonly searchQuery$ = new BehaviorSubject<string>('');

    readonly pageSize = 20;
    readonly currentOffset = signal<number>(0);
    readonly isLoading = signal<boolean>(false);
    readonly totalCount = signal<number>(0);
    readonly hasNextPage = signal<boolean>(false);
    readonly hasPreviousPage = signal<boolean>(false);

    readonly trackByIdFn = TrackByFunctionFactory.get('_id');

    @ViewChild(MatPaginator) set paginator(matPaginator: MatPaginator) {
        if (matPaginator) {
            this._paginator = matPaginator;
        }
    }

    ngOnInit(): void {
        this._store.dispatch(RestaurantsActions.loadRestaurants());

        const search$ = this.searchQuery$.pipe(
            startWith(''),
            distinctUntilChanged(),
            tap(() => {
                this.currentOffset.set(0);
                if (this._paginator) {
                    this._paginator.firstPage();
                }
            })
        );

        combineLatest([search$, this.refresh$.pipe(startWith(''))])
            .pipe(
                tap(() => this.isLoading.set(true)),
                switchMap(() => this._loadRestaurants$(this.currentOffset())),
                tap(([restaurants, userRestaurants]) => {
                    const total = restaurants.metadata?.pagination?.total ?? 0;
                    this._updateDataSource(restaurants.data, userRestaurants, total);
                }),
                takeUntilDestroyed(this._destroyRef)
            )
            .subscribe();
    }

    private _loadRestaurants$(
        offset: number
    ): Observable<[ApiResultV2<Restaurant[], { pagination: { total: number } }>, UserRestaurant[]]> {
        const searchText = this.searchQuery$.getValue();
        const fields = ['address', 'name', 'active', 'organizationId', 'boosterPack', 'isYextActivated', 'roiActivated', 'type'];

        return combineLatest([
            this._restaurantsService.searchRestaurantsV2(searchText, fields, this.pageSize, offset),
            this.userRestaurants$,
        ]).pipe(takeUntilDestroyed(this._destroyRef));
    }

    onSearchChange(searchValue: string): void {
        this.searchQuery$.next(searchValue);
    }

    onPageEvent({ pageIndex, pageSize }: { pageIndex: number; pageSize: number }): void {
        const offset = pageIndex * pageSize;
        this.currentOffset.set(offset);
        this.refresh$.next();
    }

    displayManagersListModal(managers: UserRestaurant[]): void {
        this._customDialogService.open(ManagersListModalComponent, {
            disableClose: false,
            data: {
                managers,
            },
        });
    }

    updateActive(active: boolean, restaurantId: string): void {
        const restaurant = this.dataSource.data.find((r) => r._id === restaurantId);
        if (!restaurant) {
            return;
        }
        const { organizationId, organization } = restaurant;
        if (!organizationId || !organization) {
            return;
        }
        this._restaurantsService.updateActive(restaurantId, active).subscribe({
            next: (result) => {
                if (result) {
                    this.refresh$.next();
                }
            },
        });
    }

    updateIsManagedByLoggedUser(restaurantId: string, isManagedByLoggedUser: boolean): void {
        const updateUserManagement$ = isManagedByLoggedUser
            ? this._restaurantsService.addRestaurantForUser(restaurantId)
            : this._restaurantsService.removeRestaurantForUser(restaurantId);
        if (restaurantId === this.currentUser()?.lastVisitedRestaurantId) {
            this._restaurantsService.setSelectedRestaurant(null);
        }
        updateUserManagement$.subscribe({
            error: (err) => {
                console.warn('err :>> ', err);
                this._toastService.openErrorToast(this._httpErrorPipe.transform(err));
            },
        });
    }

    updateIsBoosterFeatureActivated(event: boolean, restaurantId: string): void {
        this._restaurantsService.adminUpdate(restaurantId, { boosterPack: { activated: event } }).subscribe({
            next: () => {
                this.refresh$.next();
            },
            error: (err) => {
                console.error('err :>> ', err);
                this._toastService.openErrorToast(this._httpErrorPipe.transform(err));
            },
        });
    }

    updateIsYextActivated(isYextActivated: boolean, restaurantId: string): void {
        const restaurant$ = isYextActivated
            ? this._restaurantsService.activateYextForRestaurant(restaurantId)
            : this._restaurantsService.deactivateYextForRestaurant(restaurantId);

        restaurant$.subscribe({
            next: () => {
                this.refresh$.next();
            },
            error: (err) => {
                console.error('err :>> ', err);
                this._toastService.openErrorToast(this._httpErrorPipe.transform(err));
            },
        });
    }

    updateIsRoiActivated(isRoiActivated: boolean, restaurantId: string): void {
        this._restaurantsService.adminUpdate(restaurantId, { roiActivated: isRoiActivated }).subscribe({
            next: () => {
                this.refresh$.next();
            },
            error: (err) => {
                console.error('err :>> ', err);
                this._toastService.openErrorToast(this._httpErrorPipe.transform(err));
            },
        });
    }

    updateRestaurant(restaurant: Restaurant): void {
        this._customDialogService
            .open(UpdateRestaurantModalComponent, {
                data: {
                    restaurant,
                },
            })
            .afterClosed()
            .subscribe((result: RestaurantUpdateForm) => {
                if (!result) {
                    return;
                }

                const updateManagers$ = this._getUpdateManagers$(restaurant, result);
                const updateOrganization$ = this._getUpdateOrganization$(restaurant, result);
                forkJoin([updateManagers$, updateOrganization$]).subscribe({
                    next: () => {
                        this.refresh$.next();
                    },
                    error: (err) => {
                        console.error('err :>> ', err);
                        this._toastService.openErrorToast(this._httpErrorPipe.transform(err));
                    },
                });
            });
    }

    openYextConfirmDeletionModal(restaurant: Restaurant, shouldDisableRestaurant: boolean): void {
        this._dialogService
            .open({
                variant: DialogVariant.INFO,
                title: this._translate.instant('admin.restaurants.yext.modal.delete.title'),
                primaryButton: {
                    label: this._translate.instant('common.confirm'),
                    action: () => {
                        this.updateIsYextActivated(false, restaurant._id);
                        if (shouldDisableRestaurant) {
                            this.updateActive(false, restaurant._id);
                        }
                    },
                },
                secondaryButton: {
                    label: this._translate.instant('common.cancel'),
                    action: () => {},
                },
            })
            .afterClosed()
            .subscribe();
    }

    openYextConfirmCreationModal(restaurant: Restaurant): void {
        this._dialogService
            .open({
                variant: DialogVariant.INFO,
                title: this._translate.instant('admin.restaurants.yext.modal.creation.title'),
                message: this._translate.instant('admin.restaurants.yext.modal.creation.description'),
                primaryButton: {
                    label: this._translate.instant('common.confirm'),
                    action: () => this.updateIsYextActivated(!restaurant.isYextActivated, restaurant._id),
                },
                secondaryButton: {
                    label: this._translate.instant('common.cancel'),
                    action: () => {},
                },
            })
            .afterClosed()
            .subscribe();
    }

    openYextConfirmModal(restaurant: Restaurant): void {
        const isYextAlreadyEnabled = restaurant.isYextActivated;

        if (isYextAlreadyEnabled) {
            this.openYextConfirmDeletionModal(restaurant, false);
        } else {
            this.openYextConfirmCreationModal(restaurant);
        }
    }

    proceedUpdateActive(restaurant: Restaurant, newActiveValue: boolean): void {
        if (!newActiveValue) {
            this._deactivateRestaurant(restaurant);
        } else {
            this._activateRestaurant(restaurant);
        }
    }

    private _updateDataSource(restaurants: Restaurant[], userRestaurants: UserRestaurant[], total: number): void {
        restaurants.forEach((r) => (r.isManagedByLoggedUser = !!userRestaurants.find((rest) => rest.restaurantId === r._id)));

        this.dataSource.data = [...restaurants];
        this.totalCount.set(total);
        this.isLoading.set(false);
    }

    private _getUpdateManagers$(restaurant: Restaurant, result: RestaurantUpdateForm): Observable<ApiResult | null> {
        if (
            result.users.length !== restaurant.managers.length ||
            result.users.some((u) => !restaurant.managers.map((m) => m.user._id).includes(u._id))
        ) {
            return this._restaurantsService.updateUserRestaurant(
                restaurant._id,
                result.users.map((u) => u._id)
            );
        } else {
            return of(null);
        }
    }

    private _getUpdateOrganization$(restaurant: Restaurant, result: RestaurantUpdateForm): Observable<null | ApiResultV2<undefined>> {
        const organization = result.organization;
        if (!organization || organization._id === restaurant.organization?._id) {
            return of(null);
        }
        return this._restaurantsService.updateRestaurantOrganization(
            { restaurantId: restaurant._id },
            { organizationId: organization._id }
        );
    }

    private _deactivateRestaurant(restaurant: Restaurant): void {
        if (restaurant.isYextActivated) {
            this.openYextConfirmDeletionModal(restaurant, true);
            return;
        }
        this.updateActive(false, restaurant._id);
    }

    private _activateRestaurant(restaurant: Restaurant): void {
        if (restaurant.isYextActivated) {
            this.updateIsYextActivated(false, restaurant._id);
        }
        this.updateActive(true, restaurant._id);
    }

    ngOnDestroy(): void {
        this._store.dispatch(UserActions.loadUser());
    }
}
