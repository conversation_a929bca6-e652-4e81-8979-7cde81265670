import { DatePipe, NgTemplateOutlet, SlicePipe } from '@angular/common';
import { ChangeDetectionStrategy, Component, DestroyRef, inject, OnInit, signal, ViewChild } from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatMenuModule } from '@angular/material/menu';
import { MatPaginator } from '@angular/material/paginator';
import { MatSortModule } from '@angular/material/sort';
import { MatTableDataSource, MatTableModule } from '@angular/material/table';
import { RouterLink } from '@angular/router';
import { Store } from '@ngrx/store';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { differenceBy } from 'lodash';
import { BehaviorSubject, combineLatest, distinctUntilChanged, forkJoin, map, Observable, Subject, throwError } from 'rxjs';
import { switchMap, tap } from 'rxjs/operators';

import {
    AdminSearchOrganizationsOrganizationDto,
    CreateOrganizationRequestBodyDto,
    CreateOrganizationResponseBodyDto,
} from '@malou-io/package-dto';

import { DialogService } from ':core/services/dialog.service';
import { SpinnerService } from ':core/services/malou-spinner.service';
import { OrganizationsService } from ':core/services/organizations.service';
import { RestaurantsService } from ':core/services/restaurants.service';
import { ToastService } from ':core/services/toast.service';
import { AdminService } from ':modules/admin/admin.service';
import {
    OrganizationUpsertForm,
    UpsertOrganizationModalComponent,
} from ':modules/admin/organizations/upsert-organization-modal/upsert-organization-modal.component';
import { UsersListModalComponent } from ':modules/admin/organizations/users-list-modal/users-list-modal.component';
import { RestaurantsListModalComponent } from ':modules/admin/restaurants-list-modal/restaurants-list-modal.component';
import * as UserActions from ':modules/user/store/user.actions';
import { UserState } from ':modules/user/store/user.reducer';
import { User } from ':modules/user/user';
import { UsersService } from ':modules/user/users.service';
import { DialogVariant } from ':shared/components/malou-dialog/malou-dialog.component';
import { PaginatorComponent } from ':shared/components/paginator/paginator.component';
import { SearchComponent } from ':shared/components/search/search.component';
import { SkeletonComponent } from ':shared/components/skeleton/skeleton.component';
import { TypeSafeMatCellDefDirective } from ':shared/directives/type-safe-mat-cell-def.directive';
import { TypeSafeMatRowDefDirective } from ':shared/directives/type-safe-mat-row-def.directive';
import { UpsertKind } from ':shared/enums/upsert-kind.enum';
import { TrackByFunctionFactory } from ':shared/helpers/track-by-functions';
import { Organization } from ':shared/models/organization';
import { SvgIcon } from ':shared/modules/svg-icon.enum';
import { HttpErrorPipe } from ':shared/pipes/http-error.pipe';
import { CustomDialogService } from ':shared/services/custom-dialog.service';

type OrganizationWithData = AdminSearchOrganizationsOrganizationDto;

@Component({
    selector: 'app-organizations-manager',
    templateUrl: './organizations.component.html',
    styleUrls: ['./organizations.component.scss'],
    imports: [
        NgTemplateOutlet,
        FormsModule,
        MatFormFieldModule,
        MatInputModule,
        MatButtonModule,
        MatTableModule,
        MatMenuModule,
        MatSortModule,
        MatIconModule,
        ReactiveFormsModule,
        RouterLink,
        TranslateModule,
        PaginatorComponent,
        SearchComponent,
        SlicePipe,
        TypeSafeMatCellDefDirective,
        TypeSafeMatRowDefDirective,
        DatePipe,
        SkeletonComponent,
    ],
    changeDetection: ChangeDetectionStrategy.OnPush,
})
export class OrganizationsComponent implements OnInit {
    readonly SvgIcon = SvgIcon;
    readonly UpsertKind = UpsertKind;

    dataSource: MatTableDataSource<OrganizationWithData> = new MatTableDataSource<OrganizationWithData>([]);
    displayedColumns: string[] = ['name', 'createdAt', 'users', 'restaurants', 'id', 'hyperlineId', 'actions'];
    refresh$: Subject<void> = new Subject<void>();
    readonly searchQuery$ = new BehaviorSubject<string>('');
    _paginator: MatPaginator | null = null;

    readonly pageSize = 20;
    readonly currentOffset = signal<number>(0);
    readonly isLoading = signal<boolean>(false);
    readonly totalCount = signal<number>(0);
    readonly hasNextPage = signal<boolean>(false);
    readonly hasPreviousPage = signal<boolean>(false);

    readonly MAX_USERS_PER_COLUMN = 3;
    readonly MAX_RESTAURANTS_PER_COLUMN = 5;
    readonly killSubscriptions$: Subject<void> = new Subject<void>();

    private readonly _destroyRef = inject(DestroyRef);
    readonly trackByIdFn = TrackByFunctionFactory.get('_id');

    constructor(
        private readonly _adminService: AdminService,
        private readonly _organizationsService: OrganizationsService,
        private readonly _customDialogService: CustomDialogService,
        private readonly _dialogService: DialogService,
        public readonly translate: TranslateService,
        private readonly _spinnerService: SpinnerService,
        private readonly _toastService: ToastService,
        private readonly _httpErrorPipe: HttpErrorPipe,
        private readonly _usersService: UsersService,
        private readonly _restaurantsService: RestaurantsService,
        private readonly _store: Store<{ user: UserState }>
    ) {}

    @ViewChild(MatPaginator) set paginator(matPaginator: MatPaginator) {
        if (matPaginator) {
            this._paginator = matPaginator;
        }
    }

    ngOnInit(): void {
        combineLatest([
            this.searchQuery$.pipe(
                distinctUntilChanged(),
                tap(() => {
                    this.currentOffset.set(0);
                    if (this._paginator) {
                        this._paginator.firstPage();
                    }
                })
            ),
            this.refresh$,
        ])
            .pipe(
                tap(() => this.isLoading.set(true)),
                switchMap(() => this._loadOrganizations$(this.currentOffset())),
                tap(({ organizations, total }) => {
                    this.totalCount.set(total);
                    this._updateDataSource(organizations);
                    this.isLoading.set(false);
                }),
                takeUntilDestroyed(this._destroyRef)
            )
            .subscribe();

        this.refresh$.next();
    }

    onSearchChange(searchValue: string): void {
        this.searchQuery$.next(searchValue);
    }

    onPageEvent({ pageIndex, pageSize }: { pageIndex: number; pageSize: number }): void {
        const offset = pageIndex * pageSize;
        this.currentOffset.set(offset);
        this.refresh$.next();
    }

    private _loadOrganizations$(offset: number): Observable<{
        organizations: OrganizationWithData[];
        total: number;
    }> {
        const searchText = this.searchQuery$.getValue();

        return this._organizationsService
            .adminSearchOrganizations({
                text: searchText || undefined,
                limit: this.pageSize,
                offset: offset,
            })
            .pipe(
                map((searchResult) => ({
                    organizations: searchResult.data,
                    total: searchResult.metadata?.pagination?.total || 0,
                })),
                takeUntilDestroyed(this._destroyRef)
            );
    }

    private _updateDataSource(organizations: OrganizationWithData[]): void {
        this.dataSource.data = organizations;
        this._spinnerService.hide();
    }

    displayUsersListModal(users: OrganizationWithData['users']): void {
        this._customDialogService.open(UsersListModalComponent, {
            disableClose: false,
            data: {
                users: users.map((user) => ({
                    _id: user._id,
                    name: user.name,
                    lastname: user.lastname,
                    email: user.email,
                    organizationIds: user.organizationIds,
                })),
            },
        });
    }

    displayRestaurantsListModal(organization: OrganizationWithData): void {
        this._customDialogService.open(RestaurantsListModalComponent, {
            disableClose: false,
            data: {
                restaurants: organization.restaurants.map((restaurant) => ({
                    _id: restaurant._id,
                    name: restaurant.name,
                })),
            },
        });
    }

    delete(organizationId: string): void {
        this._dialogService.open({
            title: this.translate.instant('common.are_you_sure'),
            message: this.translate.instant('admin.organizations.delete_organization_confirmation'),
            variant: DialogVariant.ALERT,
            primaryButton: {
                label: this.translate.instant('common.yes'),
                action: () => {
                    this._spinnerService.show();
                    this._organizationsService.delete({ organizationId }).subscribe({
                        next: () => {
                            this._store.dispatch({ type: UserActions.loadUser.type });
                            this.refresh$.next();
                        },
                        error: (err) => {
                            if (err.status === 400) {
                                this._toastService.openErrorToast(
                                    this.translate.instant('admin.organizations.organization_has_restaurants')
                                );
                            } else {
                                console.error('err :>> ', err);
                                this._toastService.openErrorToast(this._httpErrorPipe.transform(err));
                            }
                            this._spinnerService.hide();
                        },
                    });
                },
            },
            secondaryButton: {
                label: this.translate.instant('common.no'),
            },
        });
    }

    upsert(upsertKind: UpsertKind, organizationWithData?: OrganizationWithData): void {
        this._customDialogService
            .open(UpsertOrganizationModalComponent, {
                height: 'auto',
                data: {
                    upsertKind,
                    organization: organizationWithData
                        ? {
                              _id: organizationWithData._id,
                              name: organizationWithData.name,
                              verifiedEmailsForCampaigns: organizationWithData.verifiedEmailsForCampaigns,
                              createdAt: organizationWithData.createdAt,
                              updatedAt: organizationWithData.updatedAt,
                          }
                        : undefined,
                    users:
                        organizationWithData?.users.map((user) => ({
                            _id: user._id,
                            name: user.name,
                            lastname: user.lastname,
                            email: user.email,
                            organizationIds: user.organizationIds,
                        })) ?? [],
                },
            })
            .afterClosed()
            .subscribe((result: OrganizationUpsertForm) => {
                if (!result) {
                    return;
                }
                this._spinnerService.show();

                const body = {
                    name: result.name,
                    limit: result.limit,
                    verifiedEmailsForCampaigns: [],
                };

                const organizationId$ =
                    upsertKind === UpsertKind.INSERT
                        ? this._getCreateOrganization$(body).pipe(map((res) => res.id))
                        : this._getUpdateOrganization$(organizationWithData?._id, body).pipe(map((res) => res._id));
                organizationId$
                    .pipe(
                        switchMap((organizationId) =>
                            forkJoin(this._getUpdateUsers$(organizationId, organizationWithData?.users ?? [], result.users))
                        )
                    )
                    .subscribe({
                        complete: () => {
                            this._store.dispatch({ type: UserActions.loadUser.type });
                            this.refresh$.next();
                        },
                        error: (err) => {
                            console.error('err :>> ', err);
                            this._spinnerService.hide();
                            this._toastService.openErrorToast(this._httpErrorPipe.transform(err));
                        },
                    });
            });
    }

    private _getCreateOrganization$(body: CreateOrganizationRequestBodyDto): Observable<CreateOrganizationResponseBodyDto> {
        return this._organizationsService.create(body).pipe(map((res) => res.data));
    }

    private _getUpdateOrganization$(organizationId: string | undefined, partial: Partial<Organization>): Observable<Organization> {
        if (organizationId) {
            return this._organizationsService.update(organizationId, partial).pipe(map((res) => res.data));
        }
        return throwError(() => new Error('Organization id is missing'));
    }

    private _getUpdateUsers$(organizationId: string, currentUsers: OrganizationWithData['users'], newUsers: User[]): Observable<any>[] {
        const usersToAdd = differenceBy(newUsers, currentUsers, '_id');
        const usersToRemove = differenceBy(currentUsers, newUsers, '_id');

        return [
            ...usersToAdd.map((user) =>
                this._adminService.updateAccount(user._id, {
                    organizationIds: [...(user.organizationIds ?? []), organizationId],
                })
            ),
            ...usersToRemove.map((user) =>
                this._usersService.updateUserOrganizations(user._id, {
                    organizationIds: user.organizationIds?.filter((id) => id !== organizationId) || [],
                })
            ),
        ];
    }
}
