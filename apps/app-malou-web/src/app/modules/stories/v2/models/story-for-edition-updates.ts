import { isUndefined, omitBy } from 'lodash';
import { DateTime } from 'luxon';

import { GetStoryMediaForEditionResponseDto, UpdateStoryBodyDto } from '@malou-io/package-dto';
import { PlatformKey, PostPublicationStatus, PostUserTag, RecurrentStoryFrequency, RemoveMethodsFromClass } from '@malou-io/package-utils';

import { SubmitPublicationStatus } from ':shared/components/posts-v2/posts-v2.interface';

type StoryForEditionUpdatesProps = RemoveMethodsFromClass<StoryForEditionUpdates>;

/**
 * Immutable
 */
export class StoryForEditionUpdates {
    readonly platformKeys?: PlatformKey[];
    readonly submitPublicationStatus?: SubmitPublicationStatus;
    readonly plannedPublicationDate?: Date;
    readonly medias?: {
        uploadedMedia: GetStoryMediaForEditionResponseDto;
        editedMedia?: GetStoryMediaForEditionResponseDto | null;
        serializedPinturaEditorOptions?: string | null;
    }[];
    readonly userTagsList?: (PostUserTag[] | null)[];
    readonly recurrentStoryFrequency?: RecurrentStoryFrequency;
    readonly feedbacksId?: string;

    constructor(data: StoryForEditionUpdatesProps) {
        this.platformKeys = data.platformKeys;
        this.submitPublicationStatus = data.submitPublicationStatus;
        this.plannedPublicationDate = data.plannedPublicationDate;
        this.medias = data.medias;
        this.userTagsList = data.userTagsList;
        this.recurrentStoryFrequency = data.recurrentStoryFrequency;
        this.feedbacksId = data.feedbacksId;
    }

    clone(data: StoryForEditionUpdatesProps): StoryForEditionUpdates {
        const dataWithoutUndefined = omitBy(data, isUndefined);
        return new StoryForEditionUpdates({
            platformKeys: this.platformKeys,
            submitPublicationStatus: this.submitPublicationStatus,
            plannedPublicationDate: this.plannedPublicationDate,
            medias: this.medias,
            userTagsList: this.userTagsList,
            recurrentStoryFrequency: this.recurrentStoryFrequency,
            feedbacksId: this.feedbacksId,
            ...dataWithoutUndefined,
        });
    }

    toUpdateStoryBodyDto(): UpdateStoryBodyDto {
        let published: PostPublicationStatus | undefined;
        let plannedPublicationDateOverride: string | null = null;
        switch (this.submitPublicationStatus) {
            case SubmitPublicationStatus.DRAFT:
                published = PostPublicationStatus.DRAFT;
                break;
            case SubmitPublicationStatus.SCHEDULE:
                published = PostPublicationStatus.PENDING;
                break;
            case SubmitPublicationStatus.NOW:
                published = PostPublicationStatus.PENDING;
                plannedPublicationDateOverride = DateTime.now().toJSDate().toISOString();
                break;
        }
        return {
            platformKeys: this.platformKeys,
            published,
            plannedPublicationDate: plannedPublicationDateOverride ?? this.plannedPublicationDate?.toISOString(),
            medias: this.medias?.map((media) => ({
                uploadedMediaId: media.uploadedMedia.id,
                editedMediaId: media.editedMedia?.id,
                serializedPinturaEditorOptions: media.serializedPinturaEditorOptions,
            })),
            userTagsList: this.userTagsList,
            recurrentStoryFrequency: this.recurrentStoryFrequency ?? undefined,
            feedbacksId: this.feedbacksId,
        };
    }

    isEmpty(): boolean {
        return (
            !this.platformKeys &&
            !this.submitPublicationStatus &&
            !this.plannedPublicationDate &&
            !this.medias &&
            !this.userTagsList &&
            !this.recurrentStoryFrequency &&
            !this.feedbacksId
        );
    }
}
