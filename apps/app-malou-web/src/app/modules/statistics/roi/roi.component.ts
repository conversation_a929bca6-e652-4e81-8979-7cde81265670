import { Async<PERSON>ipe, NgClass, NgTemplateOutlet } from '@angular/common';
import {
    AfterViewInit,
    ChangeDetectionStrategy,
    Component,
    computed,
    DestroyRef,
    inject,
    OnInit,
    Signal,
    signal,
    WritableSignal,
} from '@angular/core';
import { takeUntilDestroyed, toSignal } from '@angular/core/rxjs-interop';
import { MatButtonModule } from '@angular/material/button';
import { ActivatedRoute } from '@angular/router';
import { Store } from '@ngrx/store';
import { TranslateModule } from '@ngx-translate/core';
import { BehaviorSubject, combineLatest, filter, forkJoin, fromEvent, map, Observable, of, switchMap, take, tap } from 'rxjs';

import {
    HeapEventName,
    isNotNil,
    NotificationType,
    numberOfMonthsSinceGivenDate,
    PlatformFilterPage,
    PlatformKey,
    RoiPageVisibilityStatus,
    SimilarRestaurantCategory,
} from '@malou-io/package-utils';

import { NotificationCenterContext } from ':core/components/notification-center/context/notification-center.context';
import { HeapService } from ':core/services/heap.service';
import { PlatformsService } from ':core/services/platforms.service';
import { RestaurantsService } from ':core/services/restaurants.service';
import { ScreenSizeService } from ':core/services/screen-size.service';
import { SimilarRestaurantsService } from ':core/services/similar-restaurants.service';
import { RoiContext } from ':modules/roi/roi.context';
import { RoiInsightsCreationState, RoiService } from ':modules/roi/roi.service';
import { UpdateRoiSettingsModalComponent } from ':modules/roi/update-roi-settings-modal/update-roi-settings-modal.component';
import { UpsertRoiSettingsComponent } from ':modules/roi/upsert-roi-settings/upsert-roi-settings.component';
import { getRoiPageDateLimits, isBeforeLimitDate } from ':modules/roi/utils/get-roi-page-date-limits';
import { NoConnectedPlatformsComponent } from ':shared/components-v3/no-connected-platforms/no-connected-platforms.component';
import { LoaderPageComponent } from ':shared/components/loader-page/loader-page.component';
import { DatePickerType } from ':shared/enums/date-pickers';
import { getSelectedMonthsNumberFromTimeScaleFilter, MalouTimeScalePeriod, Restaurant } from ':shared/models';
import { Illustration, IllustrationPathResolverPipe } from ':shared/pipes/illustration-path-resolver.pipe';
import { CustomDialogService } from ':shared/services/custom-dialog.service';
import { ScrollableContentService } from ':shared/services/scrollable-content.service';

import { FiltersComponent } from '../filters/filters.component';
import * as StatisticsSelector from '../store/statistics.selectors';
import { EstimatedCustomersBannerComponent } from './estimated-customers/estimated-customers-banner/estimated-customers-banner.component';
import { EstimatedCustomersComponent } from './estimated-customers/estimated-customers.component';
import { MonthlyEstimatedCustomersAndPerformanceChartComponent } from './monthly-estimated-customers-and-performance-chart/monthly-estimated-customers-and-performance-chart.component';
import { PerformanceScoreComponent } from './performance-score/performance-score.component';
import { SavedTimeComponent } from './saved-time/saved-time.component';
import { TipsComponent } from './tips/tips.component';

@Component({
    selector: 'app-roi',
    templateUrl: './roi.component.html',
    styleUrls: ['./roi.component.scss'],
    changeDetection: ChangeDetectionStrategy.OnPush,
    imports: [
        NgClass,
        MatButtonModule,
        UpsertRoiSettingsComponent,
        NgTemplateOutlet,
        TranslateModule,
        SavedTimeComponent,
        EstimatedCustomersComponent,
        AsyncPipe,
        IllustrationPathResolverPipe,
        NoConnectedPlatformsComponent,
        FiltersComponent,
        TipsComponent,
        PerformanceScoreComponent,
        MonthlyEstimatedCustomersAndPerformanceChartComponent,
        LoaderPageComponent,
        EstimatedCustomersBannerComponent,
    ],
})
export class RoiComponent implements OnInit, AfterViewInit {
    private readonly _store = inject(Store);
    private readonly _destroyRef = inject(DestroyRef);
    private readonly _screenService = inject(ScreenSizeService);
    private readonly _restaurantsService = inject(RestaurantsService);
    private readonly _customDialogService = inject(CustomDialogService);
    private readonly _similarRestaurantsService = inject(SimilarRestaurantsService);
    private readonly _roiService = inject(RoiService);
    private readonly _heapService = inject(HeapService);
    private readonly _activatedRoute = inject(ActivatedRoute);
    private readonly _notificationCenterContext = inject(NotificationCenterContext);
    private readonly _scrollableContentService = inject(ScrollableContentService);

    readonly platformsService = inject(PlatformsService);
    readonly roiContext = inject(RoiContext);

    readonly PlatformKey = PlatformKey;
    readonly Illustration = Illustration;
    readonly DatePickerType = DatePickerType;
    readonly PlatformFilterPage = PlatformFilterPage;

    readonly restaurant: WritableSignal<Restaurant | null> = signal(null);

    readonly selectedTimeScaleFilter$ = this._store.select(StatisticsSelector.selectTimeScaleFilter);
    readonly selectedMonths$ = this.selectedTimeScaleFilter$.pipe(
        map((timeScale) => getSelectedMonthsNumberFromTimeScaleFilter(timeScale))
    );
    readonly selectedMonths: Signal<number> = toSignal(this.selectedMonths$, {
        initialValue: getSelectedMonthsNumberFromTimeScaleFilter(MalouTimeScalePeriod.LAST_SIX_MONTHS),
    });

    readonly reload$: BehaviorSubject<boolean> = new BehaviorSubject(false);

    readonly isRecentRestaurant: WritableSignal<boolean> = signal(true);
    readonly MIN_MONTHS_TO_SHOW_HALF_YEAR_OF_INSIGHTS = 6;

    readonly initialCategory$: Observable<SimilarRestaurantCategory | null> = this._similarRestaurantsService
        .get(this._restaurantsService.currentRestaurant?._id)
        .pipe(map((res) => res.data?.restaurantCategory ?? null));
    readonly initialCategory: Signal<SimilarRestaurantCategory | null> = toSignal(this.initialCategory$, { initialValue: null });
    readonly alreadyHasSettings: Signal<boolean> = computed(
        () => this.isLoading() || (this.roiContext.isRoiSettingsComplete()(this.roiContext.roiSettings()) && !!this.initialCategory())
    );

    readonly restaurantLimitDate: Signal<{
        limitDateToShowRoi: Date;
        limitDateToShowPartialRoi: Date;
        limitDateToShowAdditionalClientsBanner: Date;
    } | null> = toSignal(
        this._restaurantsService.restaurantSelected$.pipe(
            filter(isNotNil),
            map((restaurant) => {
                this.restaurant.set(restaurant);
                return getRoiPageDateLimits(restaurant);
            })
        ),
        { initialValue: null }
    );

    readonly isBeforeLimitDateToShowRoi: Signal<boolean> = computed(() =>
        isBeforeLimitDate(this.restaurantLimitDate()?.limitDateToShowRoi, { addMonths: 1 })
    );

    readonly isBeforeLimitDateToShowPartialRoi: Signal<boolean> = computed(() =>
        isBeforeLimitDate(this.restaurantLimitDate()?.limitDateToShowPartialRoi)
    );

    readonly shouldShowAdditionalClientsBanner: Signal<boolean> = computed(
        () => !isBeforeLimitDate(this.restaurantLimitDate()?.limitDateToShowAdditionalClientsBanner)
    );

    readonly isRestaurantRoiActivated: Signal<boolean> = toSignal(
        this._restaurantsService.restaurantSelected$.pipe(
            filter(isNotNil),
            map((restaurant) => restaurant?.roiActivated)
        ),
        { initialValue: false }
    );

    readonly shouldShowPartialRoiPage: Signal<boolean> = computed(
        () =>
            (this.isBeforeLimitDateToShowRoi() && !this.shouldShowAdditionalClientsBanner()) ||
            (!this.isBeforeLimitDateToShowRoi() && !this.isRestaurantRoiActivated())
    );

    readonly now: Date = new Date();
    readonly isLoading: WritableSignal<boolean> = signal(true);
    readonly creationStartDate: WritableSignal<Date | null> = signal(null);
    readonly creationEstimatedTime: WritableSignal<number> = signal(Number.MAX_SAFE_INTEGER);
    readonly isCreatingRoiInsights = computed(() => !!this.creationStartDate());
    readonly hasRoiInsights: WritableSignal<boolean> = signal(true);
    readonly hasAlreadyScrolled: WritableSignal<boolean> = signal(false);
    private readonly _hasRetriedFetchingRoiInsights: WritableSignal<boolean> = signal(false);

    ngOnInit(): void {
        this._trackRoiNotificationButtonClick();
        this._fetchLastRoiSettings();
        this._checkIfHasRoiInsights();
        this._initCreationStartDateAndEstimationTime();
    }

    ngAfterViewInit(): void {
        const target = this._scrollableContentService.getElement();
        if (!target) {
            return;
        }
        fromEvent(target, 'scroll')
            .pipe(take(1))
            .subscribe(() => {
                this._heapService.track(HeapEventName.ROI_SCROLL_TRACKING, {
                    venuesIds: this.restaurant()?.id.toString(),
                });
            });
    }

    openUpdateRoiSettingsModal(): void {
        this._customDialogService
            .open(UpdateRoiSettingsModalComponent, {
                panelClass: this._screenService.isPhoneScreen ? 'malou-dialog-panel--full' : 'malou-dialog-panel--fit-content',
                data: {
                    roiSettings: this.roiContext.roiSettings,
                },
            })
            .afterClosed()
            .subscribe({
                next: (res) => {
                    if (res?.needsToBeUpdated) {
                        this.reload$.next(true);
                    }
                },
            });
    }

    private _checkIfHasRoiInsights(): void {
        combineLatest([this._restaurantsService.restaurantSelected$, this._roiService.roiInsightsCreationState$])
            .pipe(
                filter(([restaurant, _]) => isNotNil(restaurant)),
                switchMap(([restaurant, _]) =>
                    forkJoin([
                        of(restaurant),
                        this._roiService.checkHasInsights(restaurant!._id, {
                            visibility: this.isBeforeLimitDateToShowRoi() ? RoiPageVisibilityStatus.PARTIAL : RoiPageVisibilityStatus.FULL,
                        }),
                    ])
                ),
                takeUntilDestroyed(this._destroyRef)
            )
            .subscribe({
                next: ([restaurant, res]) => {
                    const hasRoiInsights = res.data;
                    if (
                        !this.isCreatingRoiInsights() &&
                        !this.isBeforeLimitDateToShowRoi() &&
                        !hasRoiInsights &&
                        this.alreadyHasSettings() &&
                        !this._hasRetriedFetchingRoiInsights()
                    ) {
                        this._retryFetchingRoiInsights(restaurant!._id);
                        this._hasRetriedFetchingRoiInsights.set(true);
                    } else {
                        this.hasRoiInsights.set(res.data);
                    }
                },
            });
    }

    private _retryFetchingRoiInsights(restaurantId: string): void {
        this._roiService.retryFetchingRoiInsights(restaurantId).subscribe({
            next: () => this._roiService.startInsightsWatcher(restaurantId),
        });
    }

    private _fetchLastRoiSettings(): void {
        combineLatest([this._restaurantsService.restaurantSelected$.pipe(filter(isNotNil)), this.reload$])
            .pipe(
                tap(() => this.isLoading.set(true)),
                switchMap(([restaurant]) => {
                    const monthsSinceCreation = numberOfMonthsSinceGivenDate(new Date(restaurant.createdAt));
                    this.isRecentRestaurant.set(monthsSinceCreation < this.MIN_MONTHS_TO_SHOW_HALF_YEAR_OF_INSIGHTS);
                    return this.roiContext.getRoiSettings(restaurant._id);
                }),
                takeUntilDestroyed(this._destroyRef)
            )
            .subscribe({
                next: () => this.isLoading.set(false),
                error: () => this.isLoading.set(false),
            });
    }

    private _initCreationStartDateAndEstimationTime(): void {
        combineLatest([this._roiService.roiInsightsCreationState$, this._restaurantsService.restaurantSelected$])
            .pipe(filter(([_, restaurant]) => isNotNil(restaurant)))
            .subscribe({
                next: ([creationCurrentState, restaurant]: [
                    {
                        [restaurantId: string]: RoiInsightsCreationState;
                    },
                    Restaurant,
                ]) => {
                    this.creationStartDate.set(creationCurrentState[restaurant._id]?.creationStartDate ?? null);
                    this.creationEstimatedTime.set(creationCurrentState[restaurant._id]?.creationEstimatedTime ?? Number.MAX_SAFE_INTEGER);
                },
            });
    }

    private _trackRoiNotificationButtonClick(): void {
        this._activatedRoute.queryParamMap.subscribe((queryParams) => {
            const utmSource = queryParams.get('utm_source');
            const type = queryParams.get('type');
            if (utmSource === 'email' && type === NotificationType.ROI_ACTIVATED) {
                this._notificationCenterContext.trackNotification({
                    heapEventName: HeapEventName.NOTIFICATION_ROI_ACTIVATED_TRACKING_EMAIL_BUTTON_CLICKED,
                    notificationId: queryParams.get('notificationId') ?? '',
                    properties: {
                        notificationType: NotificationType.ROI_ACTIVATED,
                    },
                });
            }
        });
    }
}
