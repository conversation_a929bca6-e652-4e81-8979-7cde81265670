<div class="align-center flex h-full w-full flex-col gap-4 px-8.5 py-4 pb-8">
    <ng-container
        [ngTemplateOutlet]="isCreatingRoiInsights() && alreadyHasSettings() ? generatingRoiInsights : roiPageTemplate"></ng-container>
</div>

<ng-template #roiPageTemplate>
    @if (isBeforeLimitDateToShowPartialRoi()) {
        <ng-container [ngTemplateOutlet]="noInsightsPage"></ng-container>
    } @else {
        <ng-container [ngTemplateOutlet]="alreadyHasSettings() ? checkPlatformsTemplate : addSettingsTemplate"></ng-container>
    }
</ng-template>

<ng-template #checkPlatformsTemplate>
    <ng-container
        [ngTemplateOutlet]="
            (platformsService.isOneOfThesePlatformsConnected$([PlatformKey.INSTAGRAM, PlatformKey.GMB, PlatformKey.FACEBOOK]) | async)
                ? roiStatisticsTemplate
                : platformsNotConnected
        "></ng-container>
</ng-template>

<ng-template #platformsNotConnected>
    <app-no-connected-platforms
        [illustration]="Illustration.Cook"
        [message]="'roi.platforms_not_connected' | translate"></app-no-connected-platforms>
</ng-template>

<ng-template #addSettingsTemplate>
    <div class="align-center flex h-full w-full flex-col justify-center">
        <app-upsert-roi-settings
            class="flex items-center justify-center sm:items-start"
            (onSave)="reload$.next(true)"></app-upsert-roi-settings>
    </div>
</ng-template>

<ng-template #roiStatisticsTemplate>
    <ng-container [ngTemplateOutlet]="hasRoiInsights() ? roiInsightsPage : noInsightsPage"></ng-container>
</ng-template>

<ng-template #noInsightsPage>
    <div class="flex items-center justify-end sm:flex-col sm:items-center">
        <button
            class="malou-btn-raised--secondary--alt btn-xl !h-12.5 sm:mt-2 sm:w-full"
            mat-raised-button
            (click)="openUpdateRoiSettingsModal()">
            {{ 'roi.settings.settings' | translate }}
        </button>
    </div>
    <div class="grid min-h-[68vh] place-items-center">
        <div class="flex flex-col items-center">
            <div class="mb-10 w-32 md:mb-5 md:w-24">
                <img alt="Googles illustration" [src]="Illustration.Goggles | illustrationPathResolver" />
            </div>
            @if (isBeforeLimitDateToShowPartialRoi()) {
                <div class="px-6 text-center">
                    <h3 class="malou-text-14--bold malou-color-text-1 mb-2">
                        {{ 'roi.no_access_before_first_month' | translate }}
                    </h3>
                    <p class="malou-text-10--regular">{{ 'roi.contact_us_if_unusual' | translate }}</p>
                </div>
            } @else {
                <div class="px-6 text-center">
                    <h3 class="malou-text-14--bold malou-color-text-1 mb-2">
                        {{ 'roi.no_insights_error' | translate }}
                    </h3>
                    <p class="malou-text-10--regular">{{ 'roi.contact_us' | translate }}</p>
                </div>
            }
        </div>
    </div>
</ng-template>

<ng-template #roiInsightsPage>
    <ng-container [ngTemplateOutlet]="filtersTemplate"></ng-container>
    @if (shouldShowPartialRoiPage()) {
        <ng-container [ngTemplateOutlet]="roiInsightsWithoutAdditionalCustomersTemplate"></ng-container>
    } @else {
        <ng-container [ngTemplateOutlet]="roiInsightsWithAdditionalCustomersTemplate"></ng-container>
    }
</ng-template>

<ng-template #filtersTemplate>
    <div class="flex items-center justify-between sm:flex-col sm:items-center">
        <div class="w-full flex-1">
            <app-statistics-filters
                [platformFilterPage]="PlatformFilterPage.ROI"
                [isRecentRestaurant]="isRecentRestaurant()"
                [datePickerType]="DatePickerType.TIME_SCALE"
                [timeScaleMinAcceptedDate]="isBeforeLimitDateToShowRoi() ? now : restaurantLimitDate()?.limitDateToShowRoi">
            </app-statistics-filters>
        </div>
        <button
            class="malou-btn-raised--secondary--alt btn-xl !h-12.5 sm:mt-2 sm:w-full"
            mat-raised-button
            (click)="openUpdateRoiSettingsModal()">
            {{ 'roi.settings.settings' | translate }}
        </button>
    </div>
</ng-template>

<ng-template #generatingRoiInsights>
    <app-loader-page
        class="h-full w-full"
        [generationStartDate]="creationStartDate()"
        [generationEstimatedTime]="creationEstimatedTime()"
        [title]="'roi.settings.creation_in_progress' | translate"
        [footerText]="'roi.settings.you_can_leave_while_creating' | translate"></app-loader-page>
</ng-template>

<ng-template #roiInsightsWithoutAdditionalCustomersTemplate>
    <div class="flex h-fit w-full gap-4 sm:flex-col">
        <div class="flex w-[30%] flex-col justify-between sm:w-full">
            <div class="mb-4" [ngClass]="{ 'saved-time-container': !isLoading(), '!w-full': isBeforeLimitDateToShowRoi() }">
                <app-saved-time
                    [isParentLoading]="isLoading()"
                    [isBeforeLimitDateToShowRoi]="isBeforeLimitDateToShowRoi()"></app-saved-time>
            </div>
            <div>
                <app-performance-score
                    [isParentLoading]="isLoading()"
                    [isBeforeLimitDateToShowRoi]="isBeforeLimitDateToShowRoi()"></app-performance-score>
            </div>
        </div>

        <div class="w-[70%] sm:w-full">
            <app-tips [isParentLoading]="isLoading()" [isBeforeRoiActivation]="isBeforeLimitDateToShowRoi()"></app-tips>
        </div>
    </div>
    @if (!isBeforeLimitDateToShowRoi()) {
        <app-monthly-estimated-customers-and-performance-chart
            [isParentLoading]="isLoading()"></app-monthly-estimated-customers-and-performance-chart>
    }
</ng-template>

<ng-template #roiInsightsWithAdditionalCustomersTemplate>
    <div class="flex w-full gap-4 sm:flex-col">
        <div class="h-full flex-1">
            @if (isBeforeLimitDateToShowRoi()) {
                <app-estimated-customers-banner
                    [isParentLoading]="isLoading()"
                    [limitDate]="restaurantLimitDate()?.limitDateToShowAdditionalClientsBanner">
                </app-estimated-customers-banner>
            } @else {
                <app-estimated-customers [isParentLoading]="isLoading()"></app-estimated-customers>
            }
        </div>
        <div class="h-full w-[30%] sm:w-full sm:flex-1" [ngClass]="{ 'saved-time-container': !isLoading() }">
            <app-saved-time [isParentLoading]="isLoading()" [isBeforeLimitDateToShowRoi]="isBeforeLimitDateToShowRoi()"></app-saved-time>
        </div>
    </div>

    <div class="flex gap-4 sm:flex-col">
        <div class="w-[32%] sm:w-full">
            <app-performance-score
                [isParentLoading]="isLoading()"
                [isBeforeLimitDateToShowRoi]="isBeforeLimitDateToShowRoi()"></app-performance-score>
        </div>
        <div class="w-[68%] sm:w-full">
            <app-tips [isParentLoading]="isLoading()" [isBeforeRoiActivation]="isBeforeLimitDateToShowRoi()"></app-tips>
        </div>
    </div>
    @if (!isBeforeLimitDateToShowRoi()) {
        <app-monthly-estimated-customers-and-performance-chart
            [isParentLoading]="isLoading()"></app-monthly-estimated-customers-and-performance-chart>
    }
</ng-template>
