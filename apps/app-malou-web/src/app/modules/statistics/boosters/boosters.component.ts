import { NgTemplateOutlet } from '@angular/common';
import {
    AfterViewInit,
    Component,
    computed,
    DestroyRef,
    ElementRef,
    inject,
    OnInit,
    Signal,
    signal,
    ViewChild,
    WritableSignal,
} from '@angular/core';
import { takeUntilDestroyed, toObservable, toSignal } from '@angular/core/rxjs-interop';
import { MatButtonModule } from '@angular/material/button';
import { MatMenuItem } from '@angular/material/menu';
import { Sort } from '@angular/material/sort';
import { Store } from '@ngrx/store';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { LazyLoadImageModule } from 'ng-lazyload-image';
import { combineLatest, EMPTY, forkJoin, Observable, of } from 'rxjs';
import { debounceTime, distinctUntilChanged, filter, map, switchMap, take, tap } from 'rxjs/operators';

import { ReviewResponseDto } from '@malou-io/package-dto';
import { CsvInsightChart, InsightsChart, InsightsTab, isNotNil, MalouComparisonPeriod, PlatformFilterPage } from '@malou-io/package-utils';

import { ExperimentationService } from ':core/services/experimentation.service';
import { PrivateReviewsService } from ':core/services/private-reviews.service';
import { RestaurantsService } from ':core/services/restaurants.service';
import { ScreenSizeService } from ':core/services/screen-size.service';
import { ToastService } from ':core/services/toast.service';
import {
    PrivateReviewStatisticsDataV2,
    WheelOfFortuneGiftsStatisticsData,
} from ':modules/aggregated-statistics/boosters/booster.interface';
import { BoostersStatisticsDataV2 } from ':modules/statistics/boosters/boosters.interface';
import { PrivateReviewCountV2Component } from ':modules/statistics/boosters/private-review-count-v2/private-review-count.component';
import { ScanCountV2Component } from ':modules/statistics/boosters/scan-count-v2/scan-count.component';
import { BoostersDataFetchingServiceV2 } from ':modules/statistics/boosters/services/get-boosters-data-v2.service';
import { TotemsEstimatedReviewCountV2Component } from ':modules/statistics/boosters/totems-estimated-review-count-v2/totems-estimated-review-count.component';
import { WheelOfFortuneGiftsDistributionV2Component } from ':modules/statistics/boosters/wheel-of-fortune-gifts-distribution-v2/wheel-of-fortune-gifts-distribution.component';
import { WheelOfFortuneGiftsKpisV2Component } from ':modules/statistics/boosters/wheel-of-fortune-gifts-kpis-v2/wheel-of-fortune-gifts-kpis.component';
import { DownloadInsightsSummaryService } from ':modules/statistics/download-insights/download-insights-summary.service';
import { FiltersComponent } from ':modules/statistics/filters/filters.component';
import { StatisticsFiltersContext } from ':modules/statistics/filters/filters.context';
import * as StatisticsActions from ':modules/statistics/store/statistics.actions';
import * as StatisticsSelectors from ':modules/statistics/store/statistics.selectors';
import {
    DownloadInsightsModalComponent,
    DownloadInsightsModalData,
} from ':shared/components/download-insights-modal/download-insights-modal.component';
import { ChartOptions } from ':shared/components/download-insights-modal/download-insights.interface';
import { MenuButtonV2Component } from ':shared/components/menu-button-v2/menu-button-v2.component';
import { MenuButtonSize } from ':shared/components/menu-button-v2/menu-button-v2.interface';
import { ViewBy } from ':shared/enums/view-by.enum';
import { isDateSetOrGenericPeriod } from ':shared/helpers';
import { DatesAndPeriod, LightNfc, Nfc, Restaurant } from ':shared/models';
import { EnumTranslatePipe } from ':shared/pipes/enum-translate.pipe';
import { CustomDialogService } from ':shared/services/custom-dialog.service';

@Component({
    selector: 'app-boosters',
    templateUrl: './boosters.component.html',
    styleUrls: ['./boosters.component.scss'],
    imports: [
        NgTemplateOutlet,
        LazyLoadImageModule,
        TranslateModule,
        FiltersComponent,
        MatButtonModule,
        ScanCountV2Component,
        TotemsEstimatedReviewCountV2Component,
        WheelOfFortuneGiftsKpisV2Component,
        WheelOfFortuneGiftsDistributionV2Component,
        PrivateReviewCountV2Component,
        MenuButtonV2Component,
        EnumTranslatePipe,
        MatMenuItem,
    ],
})
export class BoostersComponent implements AfterViewInit, OnInit {
    @ViewChild('topOfComponent') topOfComponent: ElementRef<HTMLElement>;

    private readonly _privateReviewsService = inject(PrivateReviewsService);
    private readonly _store = inject(Store);
    private readonly _destroyRef = inject(DestroyRef);
    private readonly _restaurantsService = inject(RestaurantsService);
    private readonly _translateService = inject(TranslateService);
    private readonly _toastService = inject(ToastService);
    private readonly _customDialogService = inject(CustomDialogService);
    private readonly _statisticsFiltersContext = inject(StatisticsFiltersContext);
    private readonly _experimentationService = inject(ExperimentationService);
    private readonly _boostersDataFetchingServiceV2 = inject(BoostersDataFetchingServiceV2);
    private readonly _downloadInsightsSummaryService = inject(DownloadInsightsSummaryService);

    public readonly screenSizeService = inject(ScreenSizeService);

    readonly InsightsTab = InsightsTab;
    readonly MenuButtonSize = MenuButtonSize;
    readonly PlatformFilterPage = PlatformFilterPage;

    readonly isLoadingBoosters: WritableSignal<boolean> = signal(false);
    readonly isErrorBoosters: WritableSignal<boolean> = signal(false);
    readonly isLoadingGifts: WritableSignal<boolean> = signal(false);
    readonly isErrorGifts: WritableSignal<boolean> = signal(false);
    readonly isPrivateReviewsLoading: WritableSignal<boolean> = signal(false);
    readonly isPrivateReviewsError: WritableSignal<boolean> = signal(false);

    readonly isLoading = computed(() => this.isLoadingBoosters() || this.isLoadingGifts());

    readonly selectedLightTotems$: Observable<LightNfc[]> = this._statisticsFiltersContext.selectedLightTotems$;
    readonly selectedTotems$: Observable<Nfc[]> = this._statisticsFiltersContext.selectedTotems$;
    readonly dates$: Observable<DatesAndPeriod> = this._store
        .select(StatisticsSelectors.selectDatesFilter)
        .pipe(distinctUntilChanged((prev, curr) => JSON.stringify(prev) === JSON.stringify(curr)));
    readonly comparisonPeriod$: Observable<MalouComparisonPeriod> = this._store
        .select(StatisticsSelectors.selectComparisonPeriodFilter)
        .pipe(distinctUntilChanged());

    readonly boosterPackActivated = toSignal(
        this._restaurantsService.restaurantSelected$.pipe(
            filter(isNotNil),
            map((restaurant) => restaurant.boosterPack?.activated),
            takeUntilDestroyed(this._destroyRef)
        ),
        { initialValue: false }
    );

    readonly restaurantTotems: Signal<Nfc[]> = toSignal(this._statisticsFiltersContext.restaurantTotems$, {
        initialValue: [],
    });

    readonly restaurantLightTotems: Signal<LightNfc[]> = toSignal(this._statisticsFiltersContext.restaurantLightTotems$, {
        initialValue: [],
    });

    // Optimization with signals

    readonly boosterData: WritableSignal<BoostersStatisticsDataV2 | null> = signal(null);
    readonly boosterData$ = toObservable(this.boosterData);
    readonly estimatedReviewCountData = computed(() => {
        const boosterData = this.boosterData();
        if (!boosterData) {
            return {
                totemsData: null,
                wheelOfFortuneData: null,
            };
        }
        return {
            totemsData: {
                ...boosterData,
                scans: boosterData.scans.filter((scan) => !scan.isWheelOfFortuneRelated()),
                previousScans: boosterData.previousScans.filter((scan) => !scan.isWheelOfFortuneRelated()),
            },
            wheelOfFortuneData: {
                ...boosterData,
                scans: boosterData.scans.filter((scan) => scan.isWheelOfFortuneRelated()),
                previousScans: boosterData.previousScans.filter((scan) => scan.isWheelOfFortuneRelated()),
            },
        };
    });
    readonly giftsDataV2: WritableSignal<WheelOfFortuneGiftsStatisticsData | null> = signal(null);
    readonly privateReviewDataV2: WritableSignal<PrivateReviewStatisticsDataV2 | null> = signal(null);

    // --------------------------------------------
    readonly nfcIds: WritableSignal<string[]> = signal([]);
    readonly privateReviewCount: WritableSignal<number> = signal(0);
    readonly shouldDisplayWheelOfFortuneStats: WritableSignal<boolean> = signal(false);

    readonly shouldDisplayPrivateReviewsStats: Signal<boolean> = computed(
        () => this.restaurantTotems().length !== 0 && this.privateReviewCount() !== 0
    );

    // Chart options
    readonly InsightsChart = InsightsChart;
    readonly chartOptions: WritableSignal<ChartOptions> = signal({
        [InsightsChart.BOOSTERS_SCAN_COUNT]: {
            viewBy: ViewBy.DAY,
            hiddenDatasetIndexes: [],
        },
        [InsightsChart.BOOSTERS_TOTEMS_ESTIMATED_REVIEWS_COUNT]: {
            viewBy: ViewBy.DAY,
            hiddenDatasetIndexes: [],
        },
        [InsightsChart.BOOSTERS_WHEEL_OF_FORTUNE_GIFTS_DISTRIBUTION]: {
            tableSortOptions: undefined,
        },
        [InsightsChart.BOOSTERS_PRIVATE_REVIEWS_COUNT]: {
            viewBy: ViewBy.DAY,
            hiddenDatasetIndexes: [],
        },
    });

    readonly isDownloadStatisticsResumeEnabled: Signal<boolean> = toSignal(
        this._experimentationService.isFeatureEnabled$('release-download-statistics-resume'),
        {
            initialValue: false,
        }
    );

    ngOnInit(): void {
        this._initBoosterData();
        this._initGiftsData();
        this._initPrivateReviewData();
    }

    ngAfterViewInit(): void {
        setTimeout(() =>
            this.topOfComponent?.nativeElement.scrollIntoView({
                behavior: 'smooth',
                block: 'start',
                inline: 'nearest',
            })
        );
    }

    // Data Init

    private _initBoosterData(): void {
        combineLatest([this.dates$, this.comparisonPeriod$, this.selectedLightTotems$, this._restaurantsService.restaurantSelected$])
            .pipe(
                debounceTime(500),
                filter(([dates]) => isDateSetOrGenericPeriod(dates) || (isNotNil(dates.startDate) && isNotNil(dates.endDate))),
                tap(() => {
                    this.isErrorBoosters.set(false);
                    this.isLoadingBoosters.set(true);
                }),
                switchMap(([dates, comparisonPeriod, nfcs, _]: [DatesAndPeriod, MalouComparisonPeriod, LightNfc[], Restaurant]) => {
                    const data = this._boostersDataFetchingServiceV2.getChartsData({ nfcs, dates, comparisonPeriod });
                    this.onNfcChange(nfcs.map((nfc) => nfc.id));
                    return data;
                }),
                takeUntilDestroyed(this._destroyRef)
            )
            .subscribe({
                next: (data) => {
                    this.boosterData.set(data);
                    this._store.dispatch(StatisticsActions.editBoosterStatsData({ data }));
                    this.isLoadingBoosters.set(false);
                },
                error: (error) => {
                    console.error('error >>', error);
                    this.isErrorBoosters.set(true);
                    this.isLoadingBoosters.set(false);
                },
            });
    }

    private _initGiftsData(): void {
        combineLatest([this.dates$, this.comparisonPeriod$])
            .pipe(
                debounceTime(500),
                filter(([dates]) => isDateSetOrGenericPeriod(dates) && isNotNil(dates.startDate) && isNotNil(dates.endDate)),
                tap(() => {
                    this.isErrorGifts.set(false);
                    this.isLoadingGifts.set(true);
                }),
                switchMap(([dates, comparisonPeriod]: [DatesAndPeriod, MalouComparisonPeriod]) =>
                    this._boostersDataFetchingServiceV2.getGiftsData({
                        dates,
                        comparisonPeriod,
                    })
                ),
                takeUntilDestroyed(this._destroyRef)
            )
            .subscribe({
                next: (data: WheelOfFortuneGiftsStatisticsData) => {
                    this.giftsDataV2.set(data);
                    this.shouldDisplayWheelOfFortuneStats.set(!!data.giftsInsightsPerGift?.length);
                    this.isLoadingGifts.set(false);
                },
                error: (error) => {
                    console.error('error >>', error);
                    this.isErrorGifts.set(true);
                    this.isLoadingGifts.set(false);
                },
            });
    }

    private _initPrivateReviewData(): void {
        this.boosterData$
            .pipe(
                filter(isNotNil),
                tap(() => {
                    this.isPrivateReviewsError.set(false);
                    this.isPrivateReviewsLoading.set(true);
                }),
                switchMap(({ nfcs, scans, previousScans, startDate }) => {
                    const scanIds = [...scans.map((scan) => scan.id), ...previousScans.map((scan) => scan.id)];
                    const privateReviewsDtos$ = scanIds.length
                        ? this._privateReviewsService.search({ scanIds }).pipe(map((apiResult) => apiResult.data))
                        : of<ReviewResponseDto[]>([]);
                    const previousPrivateReviews$ = privateReviewsDtos$.pipe(
                        map((privateReviewDtos) =>
                            privateReviewDtos.filter((privateReviewDto) => new Date(privateReviewDto.socialCreatedAt) < startDate)
                        )
                    );
                    const currentPrivateReviews$ = privateReviewsDtos$.pipe(
                        map((privateReviewDtos) =>
                            privateReviewDtos.filter((privateReviewDto) => new Date(privateReviewDto.socialCreatedAt) >= startDate)
                        )
                    );
                    return forkJoin({
                        nfcs: of(nfcs),
                        scans: of(scans),
                        privateReviewsDto: currentPrivateReviews$,
                        previousPrivateReviewsDto: previousPrivateReviews$,
                    });
                }),
                takeUntilDestroyed(this._destroyRef)
            )
            .subscribe({
                next: (data) => {
                    this.privateReviewCount.set(data.privateReviewsDto.length);
                    this.privateReviewDataV2.set(data);
                    this.isPrivateReviewsLoading.set(false);
                },
                error: (error) => {
                    console.error('error >>', error);
                    this.isPrivateReviewsError.set(true);
                    this.isPrivateReviewsLoading.set(false);
                },
            });
    }

    // Download insights
    downloadInsightsSummary(): void {
        this._downloadInsightsSummaryService.downloadCsvInsightsSummary({
            insightsTab: InsightsTab.SUMMARY,
            csvChart: CsvInsightChart.INSIGHTS_SUMMARY,
        });
    }

    openStatisticsDownload(): void {
        combineLatest([this.dates$, this.comparisonPeriod$])
            .pipe(
                take(1),
                switchMap(([dates, comparisonPeriod]) => {
                    const { startDate, endDate } = dates;
                    if (!startDate || !endDate) {
                        this._toastService.openErrorToast(
                            this._translateService.instant('aggregated_statistics.download_insights_modal.please_select_dates')
                        );
                        return EMPTY;
                    }
                    return this._customDialogService
                        .open<DownloadInsightsModalComponent, DownloadInsightsModalData>(DownloadInsightsModalComponent, {
                            height: undefined,
                            data: {
                                tab: InsightsTab.BOOSTERS,
                                filters: {
                                    dates: { startDate, endDate },
                                    nfcIds: this.nfcIds(),
                                    comparisonPeriod,
                                },
                                chartOptions: this.chartOptions(),
                            },
                        })
                        .afterClosed();
                })
            )
            .subscribe();
    }

    onViewByChange(chart: InsightsChart, value: ViewBy): void {
        this.chartOptions.update((option) => ({
            ...option,
            [chart]: {
                ...option[chart],
                viewBy: value,
            },
        }));
    }

    onHiddenDatasetIndexesChange(chart: InsightsChart, value: number[]): void {
        this.chartOptions.update((option) => ({
            ...option,
            [chart]: {
                ...option[chart],
                hiddenDatasetIndexes: value,
            },
        }));
    }

    onNfcChange(value: string[]): void {
        this.nfcIds.set(value);
    }

    onTableSortOptionsChange(chart: InsightsChart, value: Sort): void {
        this.chartOptions.update((options) => ({
            ...options,
            [chart]: {
                ...options[chart],
                tableSortOptions: value,
            },
        }));
    }
}
