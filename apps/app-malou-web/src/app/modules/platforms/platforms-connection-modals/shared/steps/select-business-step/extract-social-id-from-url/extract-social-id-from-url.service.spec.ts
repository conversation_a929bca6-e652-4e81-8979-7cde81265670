import { HttpClientTestingModule } from '@angular/common/http/testing';
import { TestBed } from '@angular/core/testing';
import { provideMockStore } from '@ngrx/store/testing';

import { PlatformKey } from '@malou-io/package-utils';

import { ExtractSocialIdFromUrlService } from ':modules/platforms/platforms-connection-modals/shared/steps/select-business-step/extract-social-id-from-url/extract-social-id-from-url.service';

describe('ExtractSocialIdFromUrlService', () => {
    let service: ExtractSocialIdFromUrlService;

    beforeEach(() => {
        TestBed.configureTestingModule({
            imports: [HttpClientTestingModule],
            providers: [ExtractSocialIdFromUrlService, provideMockStore({})],
        });
        service = TestBed.inject(ExtractSocialIdFromUrlService);
    });

    describe('execute', () => {
        describe('Tripadvisor', () => {
            it('should extract social id from valid french url', () => {
                const url =
                    'https://www.tripadvisor.fr/Restaurant_Review-g227617-d15056174-Reviews-Andiamo-Thoiry_Ain_Auvergne_Rhone_Alpes.html';

                service.execute(PlatformKey.TRIPADVISOR, url).subscribe((socialId) => {
                    expect(socialId).toBe('15056174');
                });
            });

            it('should extract social id from valid uk url', () => {
                const url =
                    'https://www.tripadvisor.co.uk/Restaurant_Review-g227617-d15056174-Reviews-Andiamo-Thoiry_Ain_Auvergne_Rhone_Alpes.html';

                service.execute(PlatformKey.TRIPADVISOR, url).subscribe((socialId) => {
                    expect(socialId).toBe('15056174');
                });
            });
        });
    });
});
