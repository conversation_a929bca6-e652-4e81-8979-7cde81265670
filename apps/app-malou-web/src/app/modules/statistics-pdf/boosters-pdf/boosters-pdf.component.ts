import { AsyncPipe, CommonModule } from '@angular/common';
import { ChangeDetectionStrategy, Component, computed, DestroyRef, signal, WritableSignal } from '@angular/core';
import { takeUntilDestroyed, toObservable } from '@angular/core/rxjs-interop';
import { TranslateModule } from '@ngx-translate/core';
import { catchError, combineLatest, debounceTime, EMPTY, filter, forkJoin, map, Observable, of, switchMap, take, tap } from 'rxjs';

import { ReviewResponseDto } from '@malou-io/package-dto';
import { InsightsChart, isNotNil, MalouComparisonPeriod, MalouPeriod } from '@malou-io/package-utils';

import { NfcService } from ':core/services/nfc.service';
import { PrivateReviewsService } from ':core/services/private-reviews.service';
import { RestaurantsService } from ':core/services/restaurants.service';
import {
    PrivateReviewStatisticsDataV2,
    WheelOfFortuneGiftsStatisticsData,
} from ':modules/aggregated-statistics/boosters/booster.interface';
import { BoostersStatisticsDataV2 } from ':modules/statistics/boosters/boosters.interface';
import { PrivateReviewCountV2Component } from ':modules/statistics/boosters/private-review-count-v2/private-review-count.component';
import { ScanCountV2Component } from ':modules/statistics/boosters/scan-count-v2/scan-count.component';
import { BoostersDataFetchingServiceV2 } from ':modules/statistics/boosters/services/get-boosters-data-v2.service';
import { TotemsEstimatedReviewCountV2Component } from ':modules/statistics/boosters/totems-estimated-review-count-v2/totems-estimated-review-count.component';
import { WheelOfFortuneGiftsDistributionV2Component } from ':modules/statistics/boosters/wheel-of-fortune-gifts-distribution-v2/wheel-of-fortune-gifts-distribution.component';
import { WheelOfFortuneGiftsKpisV2Component } from ':modules/statistics/boosters/wheel-of-fortune-gifts-kpis-v2/wheel-of-fortune-gifts-kpis.component';
import { ChartOptions } from ':shared/components/download-insights-modal/download-insights.interface';
import { ViewBy } from ':shared/enums/view-by.enum';
import { isDateSetOrGenericPeriod } from ':shared/helpers';
import { parseInsightsRouteParams } from ':shared/helpers/extract-statistics-route-data';
import { DatesAndPeriod, LightNfc } from ':shared/models';
import { ApplyPurePipe } from ':shared/pipes/apply-fn.pipe';
import { FromToDateFormatterPipe } from ':shared/pipes/from-to-date-formatter.pipe';
import { IncludesPipe } from ':shared/pipes/includes.pipe';
import { StatisticsPdfRestaurantsFormatterPipe } from ':shared/pipes/statistics-pdf-restaurants-formatter.pipe';

@Component({
    selector: 'app-boosters-pdf',
    imports: [
        CommonModule,
        TranslateModule,
        ApplyPurePipe,
        IncludesPipe,
        AsyncPipe,
        StatisticsPdfRestaurantsFormatterPipe,
        ScanCountV2Component,
        TotemsEstimatedReviewCountV2Component,
        WheelOfFortuneGiftsKpisV2Component,
        WheelOfFortuneGiftsDistributionV2Component,
        PrivateReviewCountV2Component,
    ],
    templateUrl: './boosters-pdf.component.html',
    styleUrls: ['./boosters-pdf.component.scss'],
    changeDetection: ChangeDetectionStrategy.OnPush,
    providers: [FromToDateFormatterPipe],
})
export class BoostersPdfComponent {
    readonly isLoadingBoosters: WritableSignal<boolean> = signal(true);
    readonly isErrorBoosters: WritableSignal<boolean> = signal(false);
    readonly isLoadingGifts: WritableSignal<boolean> = signal(true);
    readonly isErrorGifts: WritableSignal<boolean> = signal(false);
    readonly isPrivateReviewsLoading: WritableSignal<boolean> = signal(true);
    readonly isPrivateReviewsError: WritableSignal<boolean> = signal(false);

    readonly InsightsChart = InsightsChart;
    readonly ViewBy = ViewBy;

    readonly chartOptions: WritableSignal<ChartOptions> = signal({});
    readonly displayedCharts: WritableSignal<InsightsChart[]> = signal([]);
    readonly startDate: WritableSignal<Date | null> = signal(null);
    readonly endDate: WritableSignal<Date | null> = signal(null);
    readonly comparisonPeriod: WritableSignal<MalouComparisonPeriod> = signal(MalouComparisonPeriod.PREVIOUS_PERIOD);
    readonly nfcIds: WritableSignal<string[]> = signal([]);

    readonly scanCountHasData: WritableSignal<boolean> = signal(true);
    readonly boostersScanCountHasData: WritableSignal<boolean> = signal(true);
    readonly boostersEstimatedReviewCountHasData: WritableSignal<boolean> = signal(true);
    readonly boostersPrivateReviewCountHasData: WritableSignal<boolean> = signal(true);
    readonly wheelOfFortuneGiftsDistributionHasData: WritableSignal<boolean> = signal(true);
    readonly wheelOfFortuneGiftsKpisHasData: WritableSignal<boolean> = signal(true);

    readonly boosterData: WritableSignal<BoostersStatisticsDataV2 | null> = signal(null);
    readonly boosterData$ = toObservable(this.boosterData);
    readonly estimatedReviewCountData = computed(() => {
        const boosterData = this.boosterData();
        if (!boosterData) {
            return {
                totemsData: null,
                wheelOfFortuneData: null,
            };
        }
        return {
            totemsData: {
                ...boosterData,
                scans: boosterData.scans.filter((scan) => !scan.isWheelOfFortuneRelated()),
                previousScans: boosterData.previousScans.filter((scan) => !scan.isWheelOfFortuneRelated()),
            },
            wheelOfFortuneData: {
                ...boosterData,
                scans: boosterData.scans.filter((scan) => scan.isWheelOfFortuneRelated()),
                previousScans: boosterData.previousScans.filter((scan) => scan.isWheelOfFortuneRelated()),
            },
        };
    });
    readonly giftsData: WritableSignal<WheelOfFortuneGiftsStatisticsData | null> = signal(null);
    readonly privateReviewData: WritableSignal<PrivateReviewStatisticsDataV2 | null> = signal(null);

    selectedRestaurantTitle$: Observable<string>;

    constructor(
        private readonly _restaurantsService: RestaurantsService,
        private readonly _destroyRef: DestroyRef,
        private readonly _boostersDataFetchingService: BoostersDataFetchingServiceV2,
        private readonly _privateReviewsService: PrivateReviewsService,
        private readonly _nfcService: NfcService,
        private readonly _fromToDateFormatterPipe: FromToDateFormatterPipe
    ) {
        this._getRouteParams();

        this._getRestaurantsTitle();

        this._initBoostersData();
        this._initGiftsData();
        this._initPrivateReviewsData();
    }

    getFormattedDates = ({ startDate, endDate }: { startDate: Date | null; endDate: Date | null }): string => {
        if (!startDate || !endDate) {
            return '';
        }
        return this._fromToDateFormatterPipe.transform({ startDate, endDate });
    };

    private _getRouteParams(): void {
        const parsedQueryParams = parseInsightsRouteParams();
        const { dates, displayedCharts, chartOptions, nfcIds, comparisonPeriod } = parsedQueryParams;

        this.chartOptions.set(chartOptions ?? {});
        this.displayedCharts.set(displayedCharts);

        this.startDate.set(dates.startDate);
        this.endDate.set(dates.endDate);
        this.comparisonPeriod.set(comparisonPeriod ?? MalouComparisonPeriod.PREVIOUS_PERIOD);

        this.nfcIds.set(nfcIds ?? []);
    }

    private _getRestaurantsTitle(): void {
        this.selectedRestaurantTitle$ = this._restaurantsService.restaurantSelected$.pipe(
            map((restaurant) => restaurant?.internalName ?? restaurant?.name ?? '')
        );
    }

    private _getSelectedLightTotems$(nfcIds: string[]): Observable<LightNfc[]> {
        return this._restaurantsService.restaurantSelected$.pipe(
            filter(isNotNil),
            take(1),
            switchMap((restaurant) => this._nfcService.getLightNfcByRestaurantIds([restaurant.id])),
            map((nfcs) => nfcs.filter((nfc) => nfcIds.includes(nfc.id)))
        );
    }

    private _initBoostersData(): void {
        combineLatest([
            this._getSelectedLightTotems$(this.nfcIds()),
            of({ startDate: this.startDate(), endDate: this.endDate(), period: MalouPeriod.CUSTOM }),
            of(this.comparisonPeriod()),
        ])
            .pipe(
                debounceTime(500),
                tap(() => this.isLoadingBoosters.set(false)),
                filter(([_, dates]) => isDateSetOrGenericPeriod(dates)),
                tap(() => {
                    this.isErrorBoosters.set(false);
                    this.isLoadingBoosters.set(true);
                }),
                filter(([_, dates]) => isNotNil(dates.startDate) && isNotNil(dates.endDate)),
                switchMap(([nfcs, dates, comparisonPeriod]: [LightNfc[], DatesAndPeriod, MalouComparisonPeriod]) => {
                    const data = this._boostersDataFetchingService.getChartsData({
                        nfcs,
                        dates,
                        comparisonPeriod,
                    });
                    return data;
                }),
                catchError((error) => {
                    console.error('error >>', error);
                    this.isErrorBoosters.set(true);
                    this.isLoadingBoosters.set(false);
                    return EMPTY;
                }),
                tap(() => setTimeout(() => this.isLoadingBoosters.set(false), 1000)),
                takeUntilDestroyed(this._destroyRef)
            )
            .subscribe({
                next: (data) => {
                    this.boosterData.set(data);
                    this.isLoadingBoosters.set(false);
                },
                error: (error) => {
                    console.error('error >>', error);
                    this.isErrorBoosters.set(true);
                },
            });
    }

    private _initGiftsData(): void {
        of({ startDate: this.startDate(), endDate: this.endDate(), period: MalouPeriod.CUSTOM })
            .pipe(
                filter((dates) => isDateSetOrGenericPeriod(dates)),
                take(1),
                tap(() => {
                    this.isErrorGifts.set(false);
                    this.isLoadingGifts.set(true);
                }),
                debounceTime(500),
                filter((dates) => isNotNil(dates.startDate) && isNotNil(dates.endDate)),
                switchMap((dates) => {
                    const data = this._boostersDataFetchingService.getGiftsData({ dates, comparisonPeriod: this.comparisonPeriod() });
                    return data;
                }),
                catchError(() => {
                    this.isErrorGifts.set(true);
                    this.isLoadingGifts.set(false);
                    return EMPTY;
                })
            )
            .subscribe({
                next: (data: WheelOfFortuneGiftsStatisticsData) => {
                    this.giftsData.set(data);
                    this.isLoadingGifts.set(false);
                },
                error: (error) => {
                    console.error('error >>', error);
                    this.isErrorGifts.set(true);
                    this.isLoadingGifts.set(false);
                },
            });
    }

    private _initPrivateReviewsData(): void {
        this.boosterData$
            .pipe(
                filter(isNotNil),
                tap(() => {
                    this.isPrivateReviewsError.set(false);
                    this.isPrivateReviewsLoading.set(true);
                }),
                switchMap(({ nfcs, scans, previousScans, startDate }) => {
                    const scanIds = [...scans.map((scan) => scan.id), ...previousScans.map((scan) => scan.id)];
                    const privateReviewsDtos$ = scanIds.length
                        ? this._privateReviewsService.search({ scanIds }).pipe(map((apiResult) => apiResult.data))
                        : of<ReviewResponseDto[]>([]);
                    const previousPrivateReviews$ = privateReviewsDtos$.pipe(
                        map((privateReviewDtos) =>
                            privateReviewDtos.filter((privateReviewDto) => new Date(privateReviewDto.socialCreatedAt) < startDate)
                        )
                    );
                    const currentPrivateReviews$ = privateReviewsDtos$.pipe(
                        map((privateReviewDtos) =>
                            privateReviewDtos.filter((privateReviewDto) => new Date(privateReviewDto.socialCreatedAt) >= startDate)
                        )
                    );
                    return forkJoin({
                        nfcs: of(nfcs),
                        scans: of(scans),
                        privateReviewsDto: currentPrivateReviews$,
                        previousPrivateReviewsDto: previousPrivateReviews$,
                    });
                }),
                takeUntilDestroyed(this._destroyRef)
            )
            .subscribe({
                next: (data) => {
                    this.privateReviewData.set(data);
                    this.isPrivateReviewsLoading.set(false);
                },
                error: (_error) => {
                    this.isPrivateReviewsError.set(true);
                    this.isPrivateReviewsLoading.set(false);
                },
            });
    }
}
