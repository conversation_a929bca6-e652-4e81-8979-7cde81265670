@if (isKeywordsInsightsV2Enabled()) {
    <ng-container [ngTemplateOutlet]="seoTemplateV2"></ng-container>
} @else {
    <ng-container [ngTemplateOutlet]="seoTemplate"></ng-container>
}

<ng-template #seoTemplate>
    <div class="flex flex-col gap-y-4 py-4">
        @if (!screenSizeService.isPhoneScreen) {
            <ng-container [ngTemplateOutlet]="filtersTemplate"></ng-container>
        }

        <div class="flex h-full flex-col gap-y-4 px-8.5">
            @if (screenSizeService.isPhoneScreen) {
                <ng-container [ngTemplateOutlet]="filtersTemplate"></ng-container>
            }
            @if (hasEnoughRestaurantsSelected()) {
                <ng-container [ngTemplateOutlet]="gmbTemplate"></ng-container>
                <ng-container [ngTemplateOutlet]="keywordsTemplate"></ng-container>
            } @else {
                <div class="flex flex-col items-center py-6">
                    <img class="mb-6 h-20 w-20" alt="Taster illustration" [src]="'Taster' | illustrationPathResolver" />
                    <span class="malou-text-14--bold mb-2">{{ 'common.no_data' | translate }}</span>
                    <span class="malou-text-10--regular">{{
                        'aggregated_statistics.errors.select_at_least_2_businesses' | translate
                    }}</span>
                </div>
            }
        </div>
    </div>
</ng-template>

<ng-template #filtersTemplate>
    <div class="flex items-end gap-4 px-8.5 sm:flex-col sm:items-center md:px-0">
        <div class="flex-1 sm:w-full">
            <app-aggregated-statistics-filters
                [showPlatformsFilter]="false"
                [page]="PlatformFilterPage.SEO"></app-aggregated-statistics-filters>
        </div>
        @if (isDownloadStatisticsResumeEnabled()) {
            <app-menu-button-v2 [text]="'statistics.common.download_statistics' | translate" [size]="MenuButtonSize.LARGE">
                <button
                    class="flex !h-12 !px-5"
                    mat-menu-item
                    [disabled]="!hasEnoughRestaurantsSelected() || isLoading()"
                    (click)="openDownloadStatisticsModal()">
                    <span class="malou-text-14--regular text-malou-color-text-2">
                        {{ InsightsTab.SEO | enumTranslate: 'insights_tab_name' }}
                    </span>
                </button>
                <button class="flex !h-12 !px-5" mat-menu-item [disabled]="isLoading()" (click)="downloadInsightsSummary()">
                    <span class="malou-text-14--regular text-malou-color-text-2">
                        {{ 'statistics.common.download_statistics_sub_text' | translate }}
                    </span>
                </button>
            </app-menu-button-v2>
        } @else {
            <button
                class="malou-btn-raised--primary !h-12 sm:w-full"
                mat-raised-button
                [disabled]="!hasEnoughRestaurantsSelected() || isLoading()"
                (click)="openDownloadStatisticsModal()">
                {{ 'aggregated_statistics.common.download_statistics' | translate }}
            </button>
        }
    </div>
</ng-template>

<ng-template #seoTemplateV2>
    <div class="flex h-full flex-col gap-y-4">
        <div class="flex h-full flex-col">
            <mat-tab-group
                class="malou-tab-group h-full"
                [disableRipple]="true"
                [selectedIndex]="selectedTabIndex()"
                (selectedIndexChange)="handleTabChange($event)">
                <mat-tab label="{{ 'statistics.seo.tabs.keywords' | translate }}">
                    <ng-container
                        [ngTemplateOutlet]="scrollableTab"
                        [ngTemplateOutletContext]="{ template: keywordsTemplate }"></ng-container>
                </mat-tab>
                <mat-tab label=" {{ 'statistics.seo.tabs.gmb_visibility' | translate }}">
                    <ng-container [ngTemplateOutlet]="scrollableTab" [ngTemplateOutletContext]="{ template: gmbTemplate }"></ng-container>
                </mat-tab>
            </mat-tab-group>
        </div>
    </div>
</ng-template>

<ng-template #filtersTemplateV2>
    <div class="flex items-end gap-4 sm:flex-col sm:items-center">
        <div class="flex-1 sm:w-full">
            <app-aggregated-statistics-filters
                [showPlatformsFilter]="false"
                [datePickerType]="datePickerType()"
                [page]="PlatformFilterPage.SEO">
            </app-aggregated-statistics-filters>
        </div>
        @if (isDownloadStatisticsResumeEnabled()) {
            <app-menu-button-v2 [text]="'statistics.common.download_statistics' | translate" [size]="MenuButtonSize.LARGE">
                <button
                    class="flex !h-12 items-center justify-center !px-5"
                    mat-menu-item
                    [disabled]="!hasEnoughRestaurantsSelected() || isLoading()"
                    (click)="openDownloadStatisticsModal()">
                    <span class="malou-text-14--regular text-malou-color-text-2">
                        {{ InsightsTab.SEO | enumTranslate: 'insights_tab_name' }}
                    </span>
                </button>
                <button
                    class="flex !h-12 items-center justify-center !px-5"
                    mat-menu-item
                    [disabled]="isLoading()"
                    (click)="downloadInsightsSummary()">
                    <span class="malou-text-14--regular text-malou-color-text-2">
                        {{ 'statistics.common.download_statistics_sub_text' | translate }}
                    </span>
                </button>
            </app-menu-button-v2>
        } @else {
            <button
                class="malou-btn-raised--primary !h-12 sm:w-full"
                mat-raised-button
                [disabled]="!hasEnoughRestaurantsSelected() || isLoading()"
                (click)="openDownloadStatisticsModal()">
                {{ 'aggregated_statistics.common.download_statistics' | translate }}
            </button>
        }
    </div>
</ng-template>

<ng-template let-template="template" #scrollableTab>
    <div class="flex h-full flex-col gap-4 px-8.5 py-4 sm:px-4">
        @if (!screenSizeService.isPhoneScreen) {
            <ng-container [ngTemplateOutlet]="filtersTemplateV2"></ng-container>
        }
        <div class="h-full overflow-y-auto">
            <div class="flex flex-col gap-4">
                @if (screenSizeService.isPhoneScreen) {
                    <ng-container [ngTemplateOutlet]="filtersTemplateV2"></ng-container>
                }
                @if (hasEnoughRestaurantsSelected()) {
                    <ng-container [ngTemplateOutlet]="template"></ng-container>
                } @else {
                    <div class="flex flex-col items-center py-6">
                        <img class="mb-6 h-20 w-20" alt="Taster illustration" [src]="Illustration.Taster | illustrationPathResolver" />
                        <span class="malou-text-14--bold mb-2">{{ 'common.no_data' | translate }}</span>
                        <span class="malou-text-10--regular">{{
                            'aggregated_statistics.errors.select_at_least_2_businesses' | translate
                        }}</span>
                    </div>
                }
            </div>
        </div>
    </div>
</ng-template>

<ng-template #keywordsTemplate>
    @if (isKeywordsInsightsV2Enabled()) {
        <div class="flex flex-col gap-4">
            @if (isAggregatedKeywordsInsightsV2Enabled()) {
                <app-keywords-v5
                    (tableSortOptionsChange)="onTableSortOptionsChange(InsightsChart.AGGREGATED_RANKINGS, $event)"
                    (isLoadingEvent)="isKeywordsLoading.set($event)">
                </app-keywords-v5>
            } @else {
                <app-keywords-v3
                    (tableSortOptionsChange)="onTableSortOptionsChange(InsightsChart.AGGREGATED_RANKINGS, $event)"
                    (isLoadingEvent)="isKeywordsLoading.set($event)">
                </app-keywords-v3>
            }

            @if (isAggregatedKeywordsInsightsV2Enabled()) {
                <app-top-keyword-search-impressions (isLoadingEvent)="isKeywordsSearchImpressionsLoading.set($event)">
                </app-top-keyword-search-impressions>
            }
        </div>
    } @else {
        <app-keywords-v3
            (tableSortOptionsChange)="onTableSortOptionsChange(InsightsChart.AGGREGATED_RANKINGS, $event)"
            (isLoadingEvent)="isKeywordsLoading.set($event)">
        </app-keywords-v3>
    }
</ng-template>

<ng-template #gmbTemplate>
    <div class="flex flex-col gap-y-4">
        <app-gmb-impressions-v2
            (hiddenDatasetIndexesChange)="onHiddenDatasetIndexesChange(InsightsChart.AGGREGATED_APPARITIONS, $event)"
            (sortByChange)="onSortByChange(InsightsChart.AGGREGATED_APPARITIONS, $event)">
        </app-gmb-impressions-v2>

        <app-gmb-actions-v2
            (hiddenDatasetIndexesChange)="onHiddenDatasetIndexesChange(InsightsChart.AGGREGATED_ACTIONS, $event)"
            (sortByChange)="onSortByChange(InsightsChart.AGGREGATED_ACTIONS, $event)">
        </app-gmb-actions-v2>
    </div>
</ng-template>
