import { NgTemplateOutlet } from '@angular/common';
import { Component, computed, DestroyRef, effect, inject, OnInit, Signal, signal, WritableSignal } from '@angular/core';
import { takeUntilDestroyed, toSignal } from '@angular/core/rxjs-interop';
import { MatButtonModule } from '@angular/material/button';
import { Sort } from '@angular/material/sort';
import { MatTabsModule } from '@angular/material/tabs';
import { MatTooltipModule } from '@angular/material/tooltip';
import { ActivatedRoute, Router } from '@angular/router';
import { Store } from '@ngrx/store';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { isInteger } from 'lodash';
import { combineLatest, EMPTY } from 'rxjs';
import { switchMap, take } from 'rxjs/operators';

import { CsvInsightChart, HeapEventName, InsightsChart, InsightsTab, PlatformFilterPage, PlatformKey } from '@malou-io/package-utils';

import { ExperimentationService } from ':core/services/experimentation.service';
import { HeapService } from ':core/services/heap.service';
import { ScreenSizeService } from ':core/services/screen-size.service';
import { ToastService } from ':core/services/toast.service';
import {
    AGGREGATED_STATISTICS_RESTAURANTS_COUNT_UI_LIMIT,
    MINIMUM_RESTAURANTS_SELECTED_THRESHOLD,
} from ':modules/aggregated-statistics/aggregated-statistics.constant';
import { AggregatedEReputationInsightsTabs } from ':modules/aggregated-statistics/e-reputation/e-reputation.constants';
import { ReviewsInsightsComponent } from ':modules/aggregated-statistics/e-reputation/reviews/reviews.component';
import { AggregatedSemanticAnalysisComponent } from ':modules/aggregated-statistics/e-reputation/semantic-analysis/aggregated-semantic-analysis.component';
import { AggregatedStatisticsFiltersComponent } from ':modules/aggregated-statistics/filters/filters.component';
import { AggregatedStatisticsFiltersContext } from ':modules/aggregated-statistics/filters/filters.context';
import * as AggregatedStatisticsActions from ':modules/aggregated-statistics/store/aggregated-statistics.actions';
import * as StatisticsSelectors from ':modules/aggregated-statistics/store/aggregated-statistics.selectors';
import { DownloadInsightsSummaryService } from ':modules/statistics/download-insights/download-insights-summary.service';
import {
    DownloadInsightsModalComponent,
    DownloadInsightsModalData,
} from ':shared/components/download-insights-modal/download-insights-modal.component';
import { ChartOptions, StatisticsDataViewMode } from ':shared/components/download-insights-modal/download-insights.interface';
import { MenuButtonV2Component } from ':shared/components/menu-button-v2/menu-button-v2.component';
import { MenuButtonSize } from ':shared/components/menu-button-v2/menu-button-v2.interface';
import { ReviewAnalysesV2Component } from ':shared/components/review-analyses-v2/review-analyses.component';
import { ChartSortBy } from ':shared/enums/sort.enum';
import { Restaurant } from ':shared/models';
import { EnumTranslatePipe } from ':shared/pipes/enum-translate.pipe';
import { Illustration, IllustrationPathResolverPipe } from ':shared/pipes/illustration-path-resolver.pipe';
import { CustomDialogService } from ':shared/services/custom-dialog.service';

@Component({
    selector: 'app-e-reputation',
    templateUrl: './e-reputation.component.html',
    styleUrls: ['./e-reputation.component.scss'],
    imports: [
        NgTemplateOutlet,
        AggregatedStatisticsFiltersComponent,
        ReviewsInsightsComponent,
        ReviewAnalysesV2Component,
        IllustrationPathResolverPipe,
        MatButtonModule,
        MatTabsModule,
        MatTooltipModule,
        TranslateModule,
        AggregatedSemanticAnalysisComponent,
        EnumTranslatePipe,
        MenuButtonV2Component,
    ],
})
export class EReputationComponent implements OnInit {
    private readonly _store = inject(Store);
    private readonly _customDialogService = inject(CustomDialogService);
    private readonly _toastService = inject(ToastService);
    private readonly _translateService = inject(TranslateService);
    private readonly _experimentationService = inject(ExperimentationService);
    private readonly _aggregatedStatisticsFiltersContext = inject(AggregatedStatisticsFiltersContext);
    private readonly _heapService = inject(HeapService);
    private readonly _activatedRoute = inject(ActivatedRoute);
    private readonly _destroyRef = inject(DestroyRef);
    private readonly _router = inject(Router);
    private readonly _downloadInsightsSummaryService = inject(DownloadInsightsSummaryService);
    public readonly screenSizeService = inject(ScreenSizeService);

    readonly PlatformFilterPage = PlatformFilterPage;
    readonly Illustration = Illustration;
    readonly InsightsChart = InsightsChart;
    readonly InsightsTab = InsightsTab;
    readonly MenuButtonSize = MenuButtonSize;

    readonly isNewAggregatedSemanticAnalysisFeatureEnabled = toSignal(
        this._experimentationService.isFeatureEnabled$('release-new-aggregated-semantic-analysis'),
        { initialValue: false }
    );

    readonly isDownloadStatisticsResumeEnabled: Signal<boolean> = toSignal(
        this._experimentationService.isFeatureEnabled$('release-download-statistics-resume'),
        {
            initialValue: false,
        }
    );

    readonly platformKeys: Signal<PlatformKey[]> = toSignal(
        this._store.select(StatisticsSelectors.selectPlatformsFilter({ page: PlatformFilterPage.E_REPUTATION })),
        { initialValue: [] }
    );
    readonly hasPlatformsSelected: Signal<boolean> = computed(() => this.platformKeys().length > 0);

    readonly restaurants: Signal<Restaurant[]> = toSignal(this._aggregatedStatisticsFiltersContext.selectedRestaurants$, {
        initialValue: [],
    });
    readonly isRestaurantsCountUiLimitExceeded: Signal<boolean> = computed(
        () => this.restaurants.length > AGGREGATED_STATISTICS_RESTAURANTS_COUNT_UI_LIMIT
    );
    readonly hasEnoughRestaurantsSelected: Signal<boolean> = computed(
        () => this.restaurants().length >= MINIMUM_RESTAURANTS_SELECTED_THRESHOLD
    );

    readonly chartOptions: WritableSignal<ChartOptions> = signal({
        [InsightsChart.AGGREGATED_REVIEWS_COUNT]: {
            chartSortBy: ChartSortBy.ALPHABETICAL,
        },
        [InsightsChart.AGGREGATED_REVIEWS_RATING_AVERAGE]: {
            chartSortBy: ChartSortBy.ALPHABETICAL,
        },
        [InsightsChart.AGGREGATED_REVIEW_RATINGS_KPIS]: {
            tableSortOptions: undefined,
        },
        ...(this.isNewAggregatedSemanticAnalysisFeatureEnabled()
            ? {
                  [InsightsChart.AGGREGATED_SEMANTIC_ANALYSIS_CHART]: {
                      tableSortOptions: undefined,
                  },
                  [InsightsChart.AGGREGATED_SEMANTIC_ANALYSIS_AVERAGE_BY_CATEGORY]: {
                      tableSortOptions: undefined,
                  },
                  [InsightsChart.AGGREGATED_SEMANTIC_ANALYSIS_BREAKDOWN_BY_RESTAURANT]: {
                      tableSortOptions: undefined,
                  },
              }
            : {
                  [InsightsChart.AGGREGATED_REVIEW_ANALYSES_TAG_EVOLUTION]: {
                      chartSortBy: ChartSortBy.ALPHABETICAL,
                  },
              }),
    });

    readonly isReviewsAnalysesLoading = signal(true);
    readonly areReviewsLoading = signal(true);
    readonly selectedIndex = signal(AggregatedEReputationInsightsTabs.REVIEWS);

    readonly isLoading = computed(() => this.areReviewsLoading() || this.isReviewsAnalysesLoading());

    constructor() {
        effect(() => {
            const currentTabIndex = this.selectedIndex();
            this._router.navigate([], {
                relativeTo: this._activatedRoute,
                queryParams: { tab: currentTabIndex },
            });
        });
    }

    ngOnInit(): void {
        this._store.dispatch(AggregatedStatisticsActions.editSelectedPage({ page: PlatformFilterPage.E_REPUTATION }));
        this._activatedRoute.queryParams.pipe(takeUntilDestroyed(this._destroyRef)).subscribe((params) => {
            const tab = params['tab'];
            const expectedValues = Object.values(AggregatedEReputationInsightsTabs).map((value) => value.toString());
            if (tab && expectedValues.includes(tab.toUpperCase())) {
                if (isInteger(+tab)) {
                    this.selectedIndex.set(tab);
                } else {
                    this.selectedIndex.set(
                        AggregatedEReputationInsightsTabs[tab.toUpperCase() as keyof typeof AggregatedEReputationInsightsTabs]
                    );
                }
            }
        });
    }

    downloadInsightsSummary(): void {
        this._downloadInsightsSummaryService.downloadAggregatedCsvInsightsSummary({
            insightsTab: InsightsTab.AGGREGATED_SUMMARY,
            csvChart: CsvInsightChart.AGGREGATED_INSIGHTS_SUMMARY,
        });
    }

    openDownloadStatisticsModal(): void {
        combineLatest([
            this._store.select(StatisticsSelectors.selectDatesFilter),
            this._store.select(StatisticsSelectors.selectComparisonPeriod),
            this._store.select(StatisticsSelectors.selectRestaurantIdsFilter),
            this._store.select(StatisticsSelectors.selectPlatformsFilter({ page: PlatformFilterPage.E_REPUTATION })),
        ])
            .pipe(
                take(1),
                switchMap(([{ startDate, endDate }, comparisonPeriod, restaurantIds, platforms]) => {
                    if (!startDate || !endDate) {
                        this._toastService.openErrorToast(
                            this._translateService.instant('aggregated_statistics.download_insights_modal.please_select_dates')
                        );
                        return EMPTY;
                    }
                    return this._customDialogService
                        .open<DownloadInsightsModalComponent, DownloadInsightsModalData>(DownloadInsightsModalComponent, {
                            height: undefined,
                            data: {
                                tab: this.isNewAggregatedSemanticAnalysisFeatureEnabled()
                                    ? InsightsTab.AGGREGATED_E_REPUTATION_WITH_NEW_SEMANTIC_ANALYSIS
                                    : InsightsTab.AGGREGATED_E_REPUTATION,
                                filters: {
                                    dates: { startDate, endDate },
                                    comparisonPeriod,
                                    restaurantIds,
                                    platforms,
                                },
                                chartOptions: this.chartOptions(),
                            },
                        })
                        .afterClosed();
                })
            )
            .subscribe();
    }

    onSortByChange(chart: InsightsChart, value: ChartSortBy): void {
        this.chartOptions.update((options) => ({
            ...options,
            [chart]: {
                ...options[chart],
                chartSortBy: value,
            },
        }));
    }

    onTableSortOptionsChange(chart: InsightsChart, value: Sort): void {
        this.chartOptions.update((options) => ({
            ...options,
            [chart]: {
                ...options[chart],
                tableSortOptions: value,
            },
        }));
    }

    onViewModeChange(chart: InsightsChart, value: StatisticsDataViewMode): void {
        this.chartOptions.update((options) => ({
            ...options,
            [chart]: {
                ...options[chart],
                viewMode: value,
            },
        }));
    }

    handleTabChange(event: number): void {
        switch (event) {
            case AggregatedEReputationInsightsTabs.REVIEWS:
                this._heapService.track(HeapEventName.TRACKING_CLICK_AGGREGATED_E_REPUTATION_INSIGHTS_REVIEWS_TAB);
                this.selectedIndex.set(AggregatedEReputationInsightsTabs.REVIEWS);
                break;
            case AggregatedEReputationInsightsTabs.SEMANTIC_ANALYSIS:
                this._heapService.track(HeapEventName.TRACKING_CLICK_AGGREGATED_E_REPUTATION_INSIGHTS_SEMANTIC_ANALYSES_TAB, {
                    isNewSemanticAnalysis: this.isNewAggregatedSemanticAnalysisFeatureEnabled(),
                });
                this.selectedIndex.set(AggregatedEReputationInsightsTabs.SEMANTIC_ANALYSIS);
                break;
        }
    }
}
