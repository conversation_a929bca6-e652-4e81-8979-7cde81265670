import { LowerCase<PERSON>ipe, NgTemplateOutlet } from '@angular/common';
import { ChangeDetectionStrategy, Component, computed, inject, input, OnInit, output, signal, Signal, WritableSignal } from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { FormControl } from '@angular/forms';
import { TranslateModule } from '@ngx-translate/core';
import { Chart, ChartDataset, ChartOptions, ChartType, LegendItem, Tick, TooltipItem } from 'chart.js';
import { isNumber } from 'lodash';
import { NgChartsModule } from 'ng2-charts';
import { startWith } from 'rxjs';

import { AggregatedBoostersStatisticsDataV2 } from ':modules/aggregated-statistics/boosters/booster.interface';
import { NumberEvolutionComponent } from ':shared/components/number-evolution/number-evolution.component';
import { SelectBaseDisplayStyle } from ':shared/components/select-abstract/select-base.component';
import { SelectComponent } from ':shared/components/select/select.component';
import { SkeletonComponent } from ':shared/components/skeleton/skeleton.component';
import { ChartSortBy } from ':shared/enums/sort.enum';
import {
    ChartDataArray,
    malouChartColorLighterPink,
    malouChartColorLightPink,
    malouChartColorLightPurple,
    malouChartColorPink,
    malouChartColorText2,
} from ':shared/helpers';
import { SMALL_TOOLTIP_TAB } from ':shared/helpers/default-chart-js-configuration';
import { Restaurant } from ':shared/models';
import { ApplyPurePipe } from ':shared/pipes/apply-fn.pipe';
import { EnumTranslatePipe } from ':shared/pipes/enum-translate.pipe';
import { Illustration, IllustrationPathResolverPipe } from ':shared/pipes/illustration-path-resolver.pipe';
import { PluralTranslatePipe } from ':shared/pipes/plural-translate.pipe';
import { ShortNumberPipe } from ':shared/pipes/short-number.pipe';
import { ShortTextPipe } from ':shared/pipes/short-text.pipe';

const RESTAURANT_NAME_MAX_LENGTH = 20;

type BarChartType = Extract<ChartType, 'bar'>;

interface ChartMetadata {
    reviewsPerRating: {
        rating: number;
        reviewsCount: {
            count: number;
            restaurantId: string;
        }[];
    };
}

@Component({
    selector: 'app-private-review-count-per-restaurant-and-rating',
    templateUrl: './private-review-count-per-restaurant-and-rating.component.html',
    styleUrls: ['./private-review-count-per-restaurant-and-rating.component.scss'],
    changeDetection: ChangeDetectionStrategy.OnPush,
    providers: [ShortTextPipe],
    imports: [
        NgChartsModule,
        NgTemplateOutlet,
        TranslateModule,
        NumberEvolutionComponent,
        SelectComponent,
        SkeletonComponent,
        IllustrationPathResolverPipe,
        ShortNumberPipe,
        ApplyPurePipe,
        LowerCasePipe,
    ],
})
export class PrivateReviewCountPerRestaurantAndRatingComponent implements OnInit {
    readonly isParentLoading = input.required<boolean>();
    readonly isParentError = input.required<boolean>();
    readonly data = input<AggregatedBoostersStatisticsDataV2['totemReviewsPerRestaurant'] | null>();
    readonly restaurants = input.required<Restaurant[]>();
    readonly showSortByTextInsteadOfSelector = input<boolean>(false);
    readonly chartSortBy = input<ChartSortBy | undefined>(ChartSortBy.DESC);

    readonly hiddenDatasetIndexesChange = output<number[]>();
    readonly chartSortByChange = output<ChartSortBy>();

    private readonly _shortTextPipe = inject(ShortTextPipe);
    private readonly _enumTranslatePipe = inject(EnumTranslatePipe);
    private readonly _pluralTranslatePipe = inject(PluralTranslatePipe);

    readonly Illustration = Illustration;
    readonly SelectBaseDisplayStyle = SelectBaseDisplayStyle;

    readonly CHART_TYPE: BarChartType = 'bar';

    readonly isLoading: WritableSignal<boolean> = signal(false);
    readonly isError: WritableSignal<boolean> = signal(false);

    readonly SORT_BY_OPTIONS = [ChartSortBy.DESC, ChartSortBy.ASC, ChartSortBy.ALPHABETICAL];
    readonly DEFAULT_SORT_BY = ChartSortBy.DESC;

    readonly hasData: Signal<boolean> = computed(() => {
        const data = this.data();
        if (data) {
            const hasData = data.privateReviewsPerRating.length > 0;
            return hasData;
        }
        return false;
    });

    readonly privateReviewsCountOnPeriod: Signal<number> = computed(() => {
        const data = this.data();
        if (data) {
            return data.privateReviewsPerRating.reduce(
                (totalCount, reviewsPerRating) => totalCount + reviewsPerRating.reviewsCount.reduce((acc, value) => acc + value.count, 0),
                0
            );
        }
        return 0;
    });
    readonly privateReviewCountDifferenceWithPreviousPeriod = computed(() => {
        const data = this.data();
        if (data) {
            return data.privateReviewCountDifferenceWithPreviousPeriod;
        }
        return 0;
    });

    readonly chartDataSets: Signal<
        (ChartDataset<BarChartType, ChartDataArray> & {
            metadata: ChartMetadata;
        })[]
    > = computed(() => this._computeChartData(this.data(), this.sortedRestaurants()));

    readonly sortedRestaurants = computed(() => {
        const sortBy = this.sortBy();
        const data = this.data();
        const restaurants = this.restaurants();

        if (!data) {
            return sortBy !== ChartSortBy.ALPHABETICAL ? restaurants : [...restaurants].sort((a, b) => a.name.localeCompare(b.name));
        }

        return [...restaurants].sort((a, b) => {
            if (sortBy === ChartSortBy.ALPHABETICAL) {
                return a.getDisplayName().localeCompare(b.getDisplayName());
            }
            const aReviewsCount = data.privateReviewsPerRating.reduce(
                (totalCount, privateReviewsPerRating) =>
                    totalCount + (privateReviewsPerRating.reviewsCount.find((value) => value.restaurantId === a.id)?.count ?? 0),
                0
            );
            const bReviewsCount = data.privateReviewsPerRating.reduce(
                (totalCount, privateReviewsPerRating) =>
                    totalCount + (privateReviewsPerRating.reviewsCount.find((value) => value.restaurantId === b.id)?.count ?? 0),
                0
            );
            return sortBy === ChartSortBy.DESC ? bReviewsCount - aReviewsCount : aReviewsCount - bReviewsCount;
        });
    });

    readonly chartLabels: Signal<string[]> = computed(() => this._computeChartLabels(this.sortedRestaurants()));
    readonly chartOptions: Signal<ChartOptions<BarChartType>> = computed(() => this._computeChartOptions(this.chartLabels()));

    readonly sortByControl: FormControl<ChartSortBy> = new FormControl<ChartSortBy>(this.DEFAULT_SORT_BY) as FormControl<ChartSortBy>;
    readonly sortBy = signal(ChartSortBy.DESC);

    private _hiddenDatasetIndexes: number[] = [];

    constructor() {
        this.sortByControl.valueChanges.pipe(startWith(ChartSortBy.DESC), takeUntilDestroyed()).subscribe((sortBy: ChartSortBy) => {
            this.sortBy.set(sortBy);
            this.chartSortByChange.emit(sortBy);
        });
    }

    ngOnInit(): void {
        const sortBy = this.chartSortBy();
        if (sortBy) {
            this.sortByControl.setValue(sortBy);
            this.sortBy.set(sortBy);
        }
    }

    sortByDisplayWith = (option: ChartSortBy): string => this._enumTranslatePipe.transform(option, 'chart_sort_by');

    private _computeChartData(
        data: AggregatedBoostersStatisticsDataV2['totemReviewsPerRestaurant'] | null | undefined,
        restaurants: Restaurant[]
    ): (ChartDataset<BarChartType, ChartDataArray> & {
        metadata: ChartMetadata;
    })[] {
        if (!data) {
            return [];
        }

        const sortedPrivateReviewsPerRating = this._sortPrivateReviewsPerRating(data.privateReviewsPerRating);

        return sortedPrivateReviewsPerRating.map((reviewsPerRating) => {
            const sortedReviewsCount = this._getSortedReviewsCount(reviewsPerRating.reviewsCount, restaurants);
            return {
                label: reviewsPerRating.rating.toString(),
                borderColor: this._getChartColor(reviewsPerRating.rating),
                backgroundColor: this._getChartColor(reviewsPerRating.rating),
                xAxisID: 'xAxis',
                yAxisID: 'yAxis',
                barThickness: 7,
                data: sortedReviewsCount.map((value) => value.count || null),
                metadata: {
                    reviewsPerRating: {
                        rating: reviewsPerRating.rating,
                        reviewsCount: sortedReviewsCount,
                    },
                },
            };
        });
    }

    private _getChartColor(rating: number): string {
        switch (rating) {
            case 1:
                return malouChartColorPink;
            case 2:
                return malouChartColorLightPink;
            case 3:
                return malouChartColorLighterPink;
            case 4:
                return malouChartColorLightPurple;
            default:
                return 'black';
        }
    }

    private _computeChartLabels(restaurants: Restaurant[]): string[] {
        return restaurants.map((e) => e.getDisplayName());
    }

    private _computeChartOptions(labels: string[]): ChartOptions<BarChartType> {
        return {
            plugins: {
                tooltip: {
                    mode: 'index',
                    intersect: true,
                    filter: (tooltipItem: TooltipItem<any>): boolean => tooltipItem.formattedValue !== '0',
                    itemSort: (a: TooltipItem<any>, b: TooltipItem<any>): number => b.datasetIndex - a.datasetIndex,
                    callbacks: {
                        title: (tooltipItem: TooltipItem<BarChartType>[]) => this._computeTooltipTitle(tooltipItem),
                        afterTitle: (tooltipItem: TooltipItem<BarChartType>[]) => this._computeTooltipAfterTitle(tooltipItem),
                        label: (tooltipItem: TooltipItem<any>) => this._computeTooltipLabel(tooltipItem),
                    },
                },
                legend: {
                    align: 'end',
                    labels: {
                        generateLabels: (chart: Chart) => this._computeLegendLabels(chart),
                    },
                    onClick: (_, legendItem, legend): void => {
                        const index = legendItem.datasetIndex;
                        if (!isNumber(index)) {
                            return;
                        }
                        const ci = legend.chart;
                        if (ci.isDatasetVisible(index)) {
                            ci.hide(index);
                            this._hiddenDatasetIndexes.push(index);
                            legendItem.hidden = true;
                        } else {
                            ci.show(index);
                            this._hiddenDatasetIndexes = this._hiddenDatasetIndexes.filter((i) => i !== index);
                            legendItem.hidden = false;
                        }
                        this.hiddenDatasetIndexesChange.emit(this._hiddenDatasetIndexes);
                    },
                },
            },
            scales: {
                xAxis: {
                    stacked: true,
                    axis: 'x',
                    type: 'category',
                    ticks: {
                        callback: (_, _index: number, _ticks: Tick[]): string => {
                            const label = labels[_index];
                            return this._shortTextPipe.transform(label, RESTAURANT_NAME_MAX_LENGTH);
                        },
                        font: {
                            size: this._getTicksFontSize(),
                        },
                    },
                },
                yAxis: {
                    stacked: true,
                    axis: 'y',
                    type: 'linear',
                    offset: false,
                    ticks: {
                        stepSize: 1,
                    },
                },
            },
        };
    }

    private _computeTooltipTitle(item: TooltipItem<BarChartType>[]): string | undefined {
        const index = item[0].dataIndex;
        return this.sortedRestaurants()[index].getDisplayName();
    }

    private _computeTooltipAfterTitle(item: TooltipItem<BarChartType>[]): string | undefined {
        const index = item[0].dataIndex;
        return this.sortedRestaurants()[index].address?.getAddressWithDistrict();
    }

    private _computeTooltipLabel(item: TooltipItem<any>): string {
        const restaurantIndex = this.chartLabels().findIndex((label) => label === item.label);
        const reviewsCount = item.dataset.metadata.reviewsPerRating.reviewsCount[restaurantIndex];
        const rating = item.dataset.metadata.reviewsPerRating.rating;
        const platformName = this._pluralTranslatePipe.transform('aggregated_statistics.boosters.totems.ratings', rating);
        return `${SMALL_TOOLTIP_TAB}${platformName} (${reviewsCount.count})`;
    }

    private _computeLegendLabels(chart: Chart): LegendItem[] {
        return (
            chart.data.datasets
                .map((dataset) => dataset.label)
                .map((label: string, index: number) => {
                    const rating = parseInt(label, 10);
                    let text = '';
                    let fillStyle = 'black';
                    if (isNumber(rating)) {
                        text = this._pluralTranslatePipe.transform('aggregated_statistics.boosters.totems.ratings', rating);
                        fillStyle = this._getChartColor(rating);
                    }

                    return {
                        text,
                        fillStyle,
                        lineWidth: 0,
                        fontColor: malouChartColorText2,
                        datasetIndex: index,
                    };
                }) || []
        );
    }

    private _getTicksFontSize(): number {
        const restaurantCount = this.sortedRestaurants().length;
        if (restaurantCount > 20) {
            return 8;
        }
        return restaurantCount > 10 ? 10 : 12;
    }

    private _sortPrivateReviewsPerRating(
        privateReviewsPerRating: AggregatedBoostersStatisticsDataV2['totemReviewsPerRestaurant']['privateReviewsPerRating']
    ): AggregatedBoostersStatisticsDataV2['totemReviewsPerRestaurant']['privateReviewsPerRating'] {
        return [...privateReviewsPerRating].sort((a, b) => a.rating - b.rating);
    }

    private _getSortedReviewsCount(
        reviewsCount: {
            count: number;
            restaurantId: string;
        }[],
        restaurants: Restaurant[]
    ): {
        count: number;
        restaurantId: string;
    }[] {
        return restaurants.map((restaurant) => {
            const foundReviewCount = reviewsCount.find((reviewCount) => reviewCount.restaurantId === restaurant.id);
            return foundReviewCount ?? { restaurantId: restaurant.id, count: 0 };
        });
    }
}
