import { NgClass } from '@angular/common';
import { ChangeDetectionStrategy, Component, input, output, signal, WritableSignal } from '@angular/core';

import { InsightsChart } from '@malou-io/package-utils';

import { PrivateReviewCountPerRestaurantAndRatingComponent } from ':modules/aggregated-statistics/boosters/aggregated-totems-estimated-review-count/private-review-count-per-restaurant-and-rating/private-review-count-per-restaurant-and-rating.component';
import { ReviewCountPerRestaurantAndPlatformComponent } from ':modules/aggregated-statistics/boosters/aggregated-totems-estimated-review-count/review-count-per-restaurant-and-platform/review-count-per-restaurant-and-platform.component';
import { AggregatedBoostersStatisticsDataV2 } from ':modules/aggregated-statistics/boosters/booster.interface';
import { ChartSortBy } from ':shared/enums/sort.enum';
import { Restaurant } from ':shared/models';
import { Illustration } from ':shared/pipes/illustration-path-resolver.pipe';

@Component({
    selector: 'app-aggregated-totems-estimated-review-count',
    templateUrl: './aggregated-totems-estimated-review-count.component.html',
    styleUrls: ['./aggregated-totems-estimated-review-count.component.scss'],
    changeDetection: ChangeDetectionStrategy.OnPush,
    imports: [NgClass, PrivateReviewCountPerRestaurantAndRatingComponent, ReviewCountPerRestaurantAndPlatformComponent],
})
export class AggregatedTotemsEstimatedReviewCountComponent {
    readonly isParentLoading = input.required<boolean>();
    readonly isParentError = input.required<boolean>();
    readonly data = input<AggregatedBoostersStatisticsDataV2['totemReviewsPerRestaurant'] | null>();
    readonly restaurants = input.required<Restaurant[]>();
    readonly hidePrivateReviewsChart = input<boolean>(false);
    readonly hideReviewsPerPlatformChart = input<boolean>(false);
    readonly hasTotemsReviewsData = input.required<boolean>();
    readonly hasPrivateReviewsData = input.required<boolean>();
    readonly showSortByTextInsteadOfSelector = input<boolean>(false);
    readonly reviewsChartSortBy = input<ChartSortBy | undefined>(ChartSortBy.DESC);
    readonly privateReviewsChartSortBy = input<ChartSortBy | undefined>(ChartSortBy.DESC);

    readonly hiddenDatasetIndexesChange = output<{ chart: InsightsChart; hiddenDatasetIndexes: number[] }>();
    readonly chartSortByChange = output<{ chart: InsightsChart; chartSortBy: ChartSortBy }>();
    readonly hasDataChange = output<boolean>();

    readonly Illustration = Illustration;
    readonly InsightsChart = InsightsChart;

    readonly isLoading: WritableSignal<boolean> = signal(false);
    readonly isError: WritableSignal<boolean> = signal(false);
}
