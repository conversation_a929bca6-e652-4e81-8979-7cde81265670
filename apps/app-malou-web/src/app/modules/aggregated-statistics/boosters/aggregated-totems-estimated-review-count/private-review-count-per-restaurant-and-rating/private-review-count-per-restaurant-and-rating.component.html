@if (!isParentError() && !isError()) {
    @if (!isParentLoading() && !isLoading()) {
        <div class="malou-simple-card flex w-full flex-col gap-3 px-6 py-3 md:px-2">
            <div>
                <ng-container [ngTemplateOutlet]="titleTemplate"></ng-container>
            </div>
            <ng-container [ngTemplateOutlet]="hasData() ? mainTemplate : noDataTemplate" [ngTemplateOutletContext]="{ data: data() }">
            </ng-container>
        </div>
    } @else {
        <app-skeleton skeletonClass="!h-[542px] secondary-bg"></app-skeleton>
    }
} @else {
    <div class="flex h-[442px] flex-col items-center justify-center py-6">
        <img class="mb-6 h-20 w-20" alt="Taster illustration" [src]="Illustration.Taster | illustrationPathResolver" />
        <span class="malou-text-14--bold mb-2">{{ 'common.unknown_error' | translate }}</span>
    </div>
}

<ng-template #titleTemplate>
    <div class="flex flex-wrap items-center justify-between gap-2">
        <div class="malou-text-section-title malou-color-text-1">
            {{ 'aggregated_statistics.boosters.totems.private_review_count.title' | translate }}
        </div>

        @if (!showSortByTextInsteadOfSelector()) {
            <app-select
                [values]="SORT_BY_OPTIONS"
                [theme]="SelectBaseDisplayStyle.WITH_BACKGROUND"
                [inputReadOnly]="true"
                [displayWith]="sortByDisplayWith"
                [formControl]="sortByControl">
            </app-select>
        } @else {
            <div class="flex items-center gap-2">
                <span class="malou-text-14--regular italic">
                    {{ 'common.sorted_by_order' | translate }}:
                    {{ sortByDisplayWith | applyPure: sortByControl.value | lowercase }}
                </span>
            </div>
        }
    </div>
</ng-template>

<ng-template #noDataTemplate>
    <div class="flex h-[442px] flex-col items-center justify-center py-6">
        <img class="mb-6 h-20 w-20" alt="Taster illustration" [src]="Illustration.Taster | illustrationPathResolver" />
        <div class="malou-text-14--bold mb-2">{{ 'statistics.errors.no_data' | translate }}</div>
    </div>
</ng-template>

<ng-template let-data="data" #mainTemplate>
    <div class="relative h-[350px]">
        <canvas baseChart [datasets]="chartDataSets()" [labels]="chartLabels()" [options]="chartOptions()" [type]="CHART_TYPE"></canvas>
    </div>

    <div class="malou-simple-card--light mr-3 flex flex-1 flex-col items-center py-3">
        <div class="flex items-center">
            <div class="malou-text-30--bold malou-color-text-1">
                {{ privateReviewsCountOnPeriod() | shortNumber }}
            </div>
            <app-number-evolution
                [value]="privateReviewCountDifferenceWithPreviousPeriod()"
                [displayedValue]="privateReviewCountDifferenceWithPreviousPeriod() | shortNumber"></app-number-evolution>
        </div>
        <div class="malou-text-12--regular malou-color-text-2 whitespace-nowrap">
            {{ 'aggregated_statistics.boosters.totems.totem_private_reviews' | translate }}
        </div>
    </div>
</ng-template>
