import { LowerCasePipe, NgTemplateOutlet } from '@angular/common';
import { ChangeDetectionStrategy, Component, computed, inject, input, OnInit, output, signal, Signal, WritableSignal } from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { FormControl } from '@angular/forms';
import { TranslateModule } from '@ngx-translate/core';
import { Chart, ChartDataset, ChartOptions, ChartType, LegendItem, Tick, TooltipItem } from 'chart.js';
import { isNumber } from 'lodash';
import { NgChartsModule } from 'ng2-charts';
import { startWith } from 'rxjs';

import { PlatformKey } from '@malou-io/package-utils';

import { AggregatedBoostersStatisticsDataV2 } from ':modules/aggregated-statistics/boosters/booster.interface';
import { NumberEvolutionComponent } from ':shared/components/number-evolution/number-evolution.component';
import { SelectBaseDisplayStyle } from ':shared/components/select-abstract/select-base.component';
import { SelectComponent } from ':shared/components/select/select.component';
import { SkeletonComponent } from ':shared/components/skeleton/skeleton.component';
import { ChartSortBy } from ':shared/enums/sort.enum';
import { ChartDataArray, malouChartColorText2, mapPlatformKeyToChartColor } from ':shared/helpers';
import { SMALL_TOOLTIP_TAB } from ':shared/helpers/default-chart-js-configuration';
import { Restaurant } from ':shared/models';
import { ApplyPurePipe } from ':shared/pipes/apply-fn.pipe';
import { EnumTranslatePipe } from ':shared/pipes/enum-translate.pipe';
import { Illustration, IllustrationPathResolverPipe } from ':shared/pipes/illustration-path-resolver.pipe';
import { ShortNumberPipe } from ':shared/pipes/short-number.pipe';
import { ShortTextPipe } from ':shared/pipes/short-text.pipe';

const RESTAURANT_NAME_MAX_LENGTH = 20;

type BarChartType = Extract<ChartType, 'bar'>;

interface ChartMetadata {
    reviewsPerPlatform: {
        key: PlatformKey;
        reviewsCount: {
            count: number;
            restaurantId: string;
        }[];
    };
}

@Component({
    selector: 'app-review-count-per-restaurant-and-platform',
    templateUrl: './review-count-per-restaurant-and-platform.component.html',
    styleUrls: ['./review-count-per-restaurant-and-platform.component.scss'],
    changeDetection: ChangeDetectionStrategy.OnPush,
    providers: [ShortTextPipe],
    imports: [
        NgChartsModule,
        NgTemplateOutlet,
        TranslateModule,
        NumberEvolutionComponent,
        SelectComponent,
        SkeletonComponent,
        IllustrationPathResolverPipe,
        ShortNumberPipe,
        ApplyPurePipe,
        LowerCasePipe,
    ],
})
export class ReviewCountPerRestaurantAndPlatformComponent implements OnInit {
    readonly isParentLoading = input.required<boolean>();
    readonly isParentError = input.required<boolean>();
    readonly data = input<AggregatedBoostersStatisticsDataV2['totemReviewsPerRestaurant'] | null>();
    readonly restaurants = input.required<Restaurant[]>();
    readonly showSortByTextInsteadOfSelector = input<boolean>(false);
    readonly chartSortBy = input<ChartSortBy | undefined>(ChartSortBy.DESC);

    readonly hiddenDatasetIndexesChange = output<number[]>();
    readonly chartSortByChange = output<ChartSortBy>();

    private readonly _shortTextPipe = inject(ShortTextPipe);
    private readonly _enumTranslatePipe = inject(EnumTranslatePipe);

    readonly Illustration = Illustration;
    readonly SelectBaseDisplayStyle = SelectBaseDisplayStyle;

    readonly CHART_TYPE: BarChartType = 'bar';

    readonly isLoading: WritableSignal<boolean> = signal(false);
    readonly isError: WritableSignal<boolean> = signal(false);

    readonly SORT_BY_OPTIONS = [ChartSortBy.DESC, ChartSortBy.ASC, ChartSortBy.ALPHABETICAL];
    readonly DEFAULT_SORT_BY = ChartSortBy.DESC;

    readonly hasData: Signal<boolean> = computed(() => {
        const data = this.data();
        if (data) {
            const hasData = Object.keys(data).length > 0;
            return hasData;
        }
        return false;
    });

    readonly reviewsCountOnPeriod: Signal<number> = computed(() => {
        const data = this.data();
        if (data) {
            return data.reviewsPerPlatform.reduce(
                (totalCount, reviewsPerPlatform) =>
                    totalCount + reviewsPerPlatform.reviewsCount.reduce((acc, value) => acc + value.count, 0),
                0
            );
        }
        return 0;
    });
    readonly reviewsCountDifferenceWithPreviousPeriod = computed(() => {
        const data = this.data();
        if (data) {
            return data.reviewCountDifferenceWithPreviousPeriod;
        }
        return 0;
    });

    readonly chartDataSets: Signal<
        (ChartDataset<BarChartType, ChartDataArray> & {
            metadata: ChartMetadata;
        })[]
    > = computed(() => this._computeChartData(this.data(), this.sortedRestaurants()));

    readonly sortedRestaurants = computed(() => {
        const sortBy = this.sortBy();
        const data = this.data();
        const restaurants = this.restaurants();

        if (!data) {
            return sortBy !== ChartSortBy.ALPHABETICAL ? restaurants : [...restaurants].sort((a, b) => a.name.localeCompare(b.name));
        }

        return [...restaurants].sort((a, b) => {
            if (sortBy === ChartSortBy.ALPHABETICAL) {
                return (a.internalName ?? a.name).localeCompare(b.internalName ?? b.name);
            }
            const aReviewsCount = data.reviewsPerPlatform.reduce(
                (totalCount, reviewsPerPlatform) =>
                    totalCount + (reviewsPerPlatform.reviewsCount.find((value) => value.restaurantId === a.id)?.count ?? 0),
                0
            );
            const bReviewsCount = data.reviewsPerPlatform.reduce(
                (totalCount, reviewsPerPlatform) =>
                    totalCount + (reviewsPerPlatform.reviewsCount.find((value) => value.restaurantId === b.id)?.count ?? 0),
                0
            );
            return sortBy === ChartSortBy.DESC ? bReviewsCount - aReviewsCount : aReviewsCount - bReviewsCount;
        });
    });

    readonly chartLabels: Signal<string[]> = computed(() => this._computeChartLabels(this.sortedRestaurants()));
    readonly chartOptions: Signal<ChartOptions<BarChartType>> = computed(() => this._computeChartOptions(this.chartLabels()));

    readonly sortByControl: FormControl<ChartSortBy> = new FormControl<ChartSortBy>(this.DEFAULT_SORT_BY) as FormControl<ChartSortBy>;
    readonly sortBy = signal(ChartSortBy.DESC);

    private _hiddenDatasetIndexes: number[] = [];

    constructor() {
        this.sortByControl.valueChanges.pipe(startWith(ChartSortBy.DESC), takeUntilDestroyed()).subscribe((sortBy: ChartSortBy) => {
            this.sortBy.set(sortBy);
            this.chartSortByChange.emit(sortBy);
        });
    }

    ngOnInit(): void {
        const sortBy = this.chartSortBy();
        if (sortBy) {
            this.sortByControl.setValue(sortBy);
            this.sortBy.set(sortBy);
        }
    }

    sortByDisplayWith = (option: ChartSortBy): string => this._enumTranslatePipe.transform(option, 'chart_sort_by');

    private _computeChartData(
        data: AggregatedBoostersStatisticsDataV2['totemReviewsPerRestaurant'] | null | undefined,
        restaurants: Restaurant[]
    ): (ChartDataset<BarChartType, ChartDataArray> & {
        metadata: ChartMetadata;
    })[] {
        if (!data) {
            return [];
        }

        return data.reviewsPerPlatform.map((reviewsPerPlatform) => {
            const sortedReviewsCount = this._getSortedReviewsCount(reviewsPerPlatform.reviewsCount, restaurants);
            return {
                label: reviewsPerPlatform.key,
                borderColor: this._getChartColor(reviewsPerPlatform.key),
                backgroundColor: this._getChartColor(reviewsPerPlatform.key),
                xAxisID: 'xAxis',
                yAxisID: 'yAxis',
                barThickness: 7,
                data: sortedReviewsCount.map((value) => value.count || null),
                metadata: {
                    reviewsPerPlatform: {
                        key: reviewsPerPlatform.key,
                        reviewsCount: sortedReviewsCount,
                    },
                },
            };
        });
    }

    private _getChartColor(key: PlatformKey): string {
        return mapPlatformKeyToChartColor(key);
    }

    private _computeChartLabels(restaurants: Restaurant[]): string[] {
        return restaurants.map((e) => e.getDisplayName());
    }

    private _computeChartOptions(labels: string[]): ChartOptions<BarChartType> {
        return {
            plugins: {
                tooltip: {
                    mode: 'index',
                    intersect: true,
                    filter: (tooltipItem: TooltipItem<any>): boolean => tooltipItem.formattedValue !== '0',
                    itemSort: (a: TooltipItem<any>, b: TooltipItem<any>): number => b.datasetIndex - a.datasetIndex,
                    callbacks: {
                        title: (tooltipItem: TooltipItem<BarChartType>[]) => this._computeTooltipTitle(tooltipItem),
                        afterTitle: (tooltipItem: TooltipItem<BarChartType>[]) => this._computeTooltipAfterTitle(tooltipItem),
                        label: (tooltipItem: TooltipItem<any>) => this._computeTooltipLabel(tooltipItem),
                    },
                },
                legend: {
                    align: 'end',
                    labels: {
                        generateLabels: (chart: Chart) => this._computeLegendLabels(chart),
                    },
                    onClick: (_, legendItem, legend): void => {
                        const index = legendItem.datasetIndex;
                        if (!isNumber(index)) {
                            return;
                        }
                        const ci = legend.chart;
                        if (ci.isDatasetVisible(index)) {
                            ci.hide(index);
                            this._hiddenDatasetIndexes.push(index);
                            legendItem.hidden = true;
                        } else {
                            ci.show(index);
                            this._hiddenDatasetIndexes = this._hiddenDatasetIndexes.filter((i) => i !== index);
                            legendItem.hidden = false;
                        }
                        this.hiddenDatasetIndexesChange.emit(this._hiddenDatasetIndexes);
                    },
                },
            },
            scales: {
                xAxis: {
                    stacked: true,
                    axis: 'x',
                    type: 'category',
                    ticks: {
                        callback: (_, _index: number, _ticks: Tick[]): string => {
                            const label = labels[_index];
                            return this._shortTextPipe.transform(label, RESTAURANT_NAME_MAX_LENGTH);
                        },
                        font: {
                            size: this._getTicksFontSize(),
                        },
                    },
                },
                yAxis: {
                    stacked: true,
                    axis: 'y',
                    type: 'linear',
                    offset: false,
                    ticks: {
                        stepSize: 1,
                    },
                },
            },
        };
    }

    private _computeTooltipTitle(item: TooltipItem<BarChartType>[]): string | undefined {
        const index = item[0].dataIndex;
        return this.sortedRestaurants()[index].getDisplayName();
    }

    private _computeTooltipAfterTitle(item: TooltipItem<BarChartType>[]): string | undefined {
        const index = item[0].dataIndex;
        return this.sortedRestaurants()[index].address?.getAddressWithDistrict();
    }

    private _computeTooltipLabel(item: TooltipItem<any>): string {
        const restaurantIndex = this.chartLabels().findIndex((label) => label === item.label);
        const reviewsCount = item.dataset.metadata.reviewsPerPlatform.reviewsCount[restaurantIndex];
        const platformName = this._enumTranslatePipe.transform(item.dataset.label, 'platform_key');
        return `${SMALL_TOOLTIP_TAB}${platformName} (${reviewsCount.count})`;
    }

    private _computeLegendLabels(chart: Chart): LegendItem[] {
        return (
            chart.data.datasets
                .map((dataset) => dataset.label)
                .map((label: PlatformKey, index: number) => {
                    const text = this._enumTranslatePipe.transform(label, 'platform_key');
                    const fillStyle = this._getChartColor(label);
                    return {
                        text,
                        fillStyle,
                        lineWidth: 0,
                        fontColor: malouChartColorText2,
                        datasetIndex: index,
                    };
                }) || []
        );
    }

    private _getTicksFontSize(): number {
        const restaurantCount = this.sortedRestaurants().length;
        if (restaurantCount > 20) {
            return 8;
        }
        return restaurantCount > 10 ? 10 : 12;
    }

    private _getSortedReviewsCount(
        reviewsCount: {
            count: number;
            restaurantId: string;
        }[],
        restaurants: Restaurant[]
    ): {
        count: number;
        restaurantId: string;
    }[] {
        return restaurants.map((restaurant) => {
            const foundReviewCount = reviewsCount.find((reviewCount) => reviewCount.restaurantId === restaurant.id);
            return foundReviewCount ?? { restaurantId: restaurant.id, count: 0 };
        });
    }
}
