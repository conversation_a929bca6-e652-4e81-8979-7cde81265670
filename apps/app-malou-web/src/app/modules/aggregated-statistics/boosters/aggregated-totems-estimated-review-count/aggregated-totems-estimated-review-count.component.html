<div class="flex gap-4 md:flex-col" [ngClass]="{ 'flex-col': restaurants().length > 15 }">
    @if (hasPrivateReviewsData() && !hidePrivateReviewsChart()) {
        <div class="min-w-0 flex-1">
            <app-private-review-count-per-restaurant-and-rating
                [isParentError]="isParentError()"
                [isParentLoading]="isParentLoading()"
                [data]="data()"
                [restaurants]="restaurants()"
                [showSortByTextInsteadOfSelector]="showSortByTextInsteadOfSelector()"
                [chartSortBy]="privateReviewsChartSortBy()"
                (hiddenDatasetIndexesChange)="
                    hiddenDatasetIndexesChange.emit({
                        chart: InsightsChart.AGGREGATED_BOOSTERS_PRIVATE_REVIEWS_COUNT,
                        hiddenDatasetIndexes: $event,
                    })
                "
                (chartSortByChange)="
                    chartSortByChange.emit({
                        chart: InsightsChart.AGGREGATED_BOOSTERS_PRIVATE_REVIEWS_COUNT,
                        chartSortBy: $event,
                    })
                "></app-private-review-count-per-restaurant-and-rating>
        </div>
    }
    @if (!hideReviewsPerPlatformChart() && hasTotemsReviewsData()) {
        <div class="min-w-0 flex-1">
            <app-review-count-per-restaurant-and-platform
                [isParentError]="isParentError()"
                [isParentLoading]="isParentLoading()"
                [data]="data()"
                [restaurants]="restaurants()"
                [showSortByTextInsteadOfSelector]="showSortByTextInsteadOfSelector()"
                [chartSortBy]="reviewsChartSortBy()"
                (hiddenDatasetIndexesChange)="
                    hiddenDatasetIndexesChange.emit({
                        chart: InsightsChart.AGGREGATED_BOOSTERS_REVIEWS_COUNT,
                        hiddenDatasetIndexes: $event,
                    })
                "
                (chartSortByChange)="
                    chartSortByChange.emit({ chart: InsightsChart.AGGREGATED_BOOSTERS_REVIEWS_COUNT, chartSortBy: $event })
                "></app-review-count-per-restaurant-and-platform>
        </div>
    }
</div>
