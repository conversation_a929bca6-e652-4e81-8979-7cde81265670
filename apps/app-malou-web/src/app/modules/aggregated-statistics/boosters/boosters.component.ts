import { Async<PERSON>ipe, NgTemplateOutlet } from '@angular/common';
import {
    AfterViewInit,
    ChangeDetectionStrategy,
    Component,
    computed,
    DestroyRef,
    ElementRef,
    inject,
    OnInit,
    Signal,
    signal,
    ViewChild,
    WritableSignal,
} from '@angular/core';
import { takeUntilDestroyed, toSignal } from '@angular/core/rxjs-interop';
import { MatButtonModule } from '@angular/material/button';
import { Sort } from '@angular/material/sort';
import { Store } from '@ngrx/store';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { combineLatest, debounceTime, EMPTY, filter, map, Observable, switchMap, take, tap } from 'rxjs';

import { CsvInsightChart, InsightsChart, InsightsTab, isNotNil, PlatformFilterPage } from '@malou-io/package-utils';

import { ExperimentationService } from ':core/services/experimentation.service';
import { ScreenSizeService } from ':core/services/screen-size.service';
import { ToastService } from ':core/services/toast.service';
import { AggregatedBoostersScanCountV2Component } from ':modules/aggregated-statistics/boosters/aggregated-boosters-scan-count-v2/aggregated-boosters-scan-count.component';
import { AggregatedTotemsEstimatedReviewCountComponent } from ':modules/aggregated-statistics/boosters/aggregated-totems-estimated-review-count/aggregated-totems-estimated-review-count.component';
import { AggregatedWheelOfFortuneEstimatedReviewCountV2Component } from ':modules/aggregated-statistics/boosters/aggregated-wheel-of-fortune-estimated-review-count-v2/aggregated-wheel-of-fortune-estimated-review-count.component';
import { AggregatedWheelOfFortuneGiftsDistributionV2Component } from ':modules/aggregated-statistics/boosters/aggregated-wheel-of-fortune-gifts-distribution-v2/aggregated-wheel-of-fortune-gifts-distribution.component';
import { AggregatedWheelOfFortuneGiftsKpisV2Component } from ':modules/aggregated-statistics/boosters/aggregated-wheel-of-fortune-gifts-kpis-v2/aggregated-wheel-of-fortune-gifts-kpis.component';
import { BoostersAggregatedDataFetchingServiceV2 } from ':modules/aggregated-statistics/boosters/services/get-boosters-aggregated-data-v2.service';
import { AggregatedStatisticsFiltersComponent } from ':modules/aggregated-statistics/filters/filters.component';
import { AggregatedStatisticsFiltersContext } from ':modules/aggregated-statistics/filters/filters.context';
import * as AggregatedStatisticsActions from ':modules/aggregated-statistics/store/aggregated-statistics.actions';
import { AggregatedStatisticsState } from ':modules/aggregated-statistics/store/aggregated-statistics.interface';
import * as AggregatedStatisticsSelectors from ':modules/aggregated-statistics/store/aggregated-statistics.selectors';
import { DownloadInsightsSummaryService } from ':modules/statistics/download-insights/download-insights-summary.service';
import { ButtonComponent } from ':shared/components/button/button.component';
import {
    DownloadInsightsModalComponent,
    DownloadInsightsModalData,
} from ':shared/components/download-insights-modal/download-insights-modal.component';
import { ChartOptions } from ':shared/components/download-insights-modal/download-insights.interface';
import { MenuButtonV2Component } from ':shared/components/menu-button-v2/menu-button-v2.component';
import { MenuButtonSize } from ':shared/components/menu-button-v2/menu-button-v2.interface';
import { ChartSortBy } from ':shared/enums/sort.enum';
import { isDateSetOrGenericPeriod } from ':shared/helpers';
import { LightNfc, Restaurant } from ':shared/models';
import { EnumTranslatePipe } from ':shared/pipes/enum-translate.pipe';
import { IllustrationPathResolverPipe } from ':shared/pipes/illustration-path-resolver.pipe';
import { CustomDialogService } from ':shared/services/custom-dialog.service';

import {
    AggregatedBoostersStatisticsData,
    AggregatedBoostersStatisticsDataV2,
    AggregatedWheelOfFortuneGiftsStatisticsData,
} from './booster.interface';

@Component({
    selector: 'app-boosters',
    templateUrl: './boosters.component.html',
    styleUrls: ['./boosters.component.scss'],
    imports: [
        NgTemplateOutlet,
        TranslateModule,
        AsyncPipe,
        IllustrationPathResolverPipe,
        AggregatedBoostersScanCountV2Component,
        ButtonComponent,
        AggregatedStatisticsFiltersComponent,
        MatButtonModule,
        AggregatedTotemsEstimatedReviewCountComponent,
        AggregatedWheelOfFortuneGiftsKpisV2Component,
        AggregatedWheelOfFortuneGiftsDistributionV2Component,
        AggregatedWheelOfFortuneEstimatedReviewCountV2Component,
        MenuButtonV2Component,
        EnumTranslatePipe,
    ],
    changeDetection: ChangeDetectionStrategy.OnPush,
})
export class BoostersComponent implements OnInit, AfterViewInit {
    @ViewChild('topOfComponent') topOfComponent: ElementRef<HTMLElement>;

    private readonly _store = inject(Store);
    private readonly _destroyRef = inject(DestroyRef);
    private readonly _translateService = inject(TranslateService);
    private readonly _toastService = inject(ToastService);
    private readonly _customDialogService = inject(CustomDialogService);
    private readonly _getBoostersAggregatedDataServiceV2 = inject(BoostersAggregatedDataFetchingServiceV2);
    private readonly _aggregatedStatisticsFiltersContext = inject(AggregatedStatisticsFiltersContext);
    private readonly _experimentationService = inject(ExperimentationService);
    private readonly _downloadInsightsSummaryService = inject(DownloadInsightsSummaryService);
    public readonly screenSizeService = inject(ScreenSizeService);

    readonly isLoadingBoosters: WritableSignal<boolean> = signal(false);
    readonly isErrorBoosters: WritableSignal<boolean> = signal(false);
    readonly isLoadingGifts: WritableSignal<boolean> = signal(false);
    readonly isErrorGifts: WritableSignal<boolean> = signal(false);
    readonly isLoadingTotemReviews: WritableSignal<boolean> = signal(false);
    readonly isErrorTotemReviews: WritableSignal<boolean> = signal(false);

    boostersData$: Observable<AggregatedBoostersStatisticsData>;
    wheelOfFortuneData$: Observable<AggregatedBoostersStatisticsData>;
    giftsData$: Observable<AggregatedWheelOfFortuneGiftsStatisticsData>;

    readonly PlatformFilterPage = PlatformFilterPage;
    readonly aggregatedStatsFilters$: Observable<AggregatedStatisticsState['filters']> = this._store.select(
        AggregatedStatisticsSelectors.selectFilters
    );
    readonly restaurants$: Observable<Restaurant[]> = this._aggregatedStatisticsFiltersContext.selectedRestaurants$;
    readonly restaurantsWithBoosterPackActivated$: Observable<Restaurant[]> = this.restaurants$.pipe(
        map((restaurants) => restaurants.filter((restaurant) => restaurant.boosterPack?.activated))
    );

    readonly restaurantsWithBoosterPackActivated = toSignal(this.restaurantsWithBoosterPackActivated$, { initialValue: [] });
    readonly atLeastOneBoosterPackActivated = toSignal(
        this.restaurantsWithBoosterPackActivated$.pipe(
            map((restaurants) => restaurants.length > 0),
            takeUntilDestroyed(this._destroyRef)
        ),
        { initialValue: false }
    );

    readonly nfcIds: WritableSignal<string[]> = signal([]);
    readonly restaurantsTotems: Signal<LightNfc[]> = toSignal(this._aggregatedStatisticsFiltersContext.restaurantsTotems$, {
        initialValue: [],
    });
    readonly boostersData: WritableSignal<AggregatedBoostersStatisticsDataV2 | null> = signal(null);
    readonly giftsData: WritableSignal<AggregatedWheelOfFortuneGiftsStatisticsData | null> = signal(null);
    readonly restaurants: Signal<Restaurant[]> = toSignal(this.restaurants$, { initialValue: [] });

    readonly wheelOfFortuneData: Signal<AggregatedBoostersStatisticsDataV2['estimatedReviewCountPerRestaurant'] | null> = computed(() => {
        const boosterData = this.boostersData();
        if (boosterData) {
            return boosterData.estimatedReviewCountPerRestaurant;
        }
        return null;
    });

    readonly totemReviewsData: Signal<AggregatedBoostersStatisticsDataV2['totemReviewsPerRestaurant'] | null> = computed(() => {
        const boosterData = this.boostersData();
        if (boosterData) {
            return boosterData.totemReviewsPerRestaurant;
        }
        return null;
    });

    readonly InsightsChart = InsightsChart;
    readonly MenuButtonSize = MenuButtonSize;
    readonly InsightsTab = InsightsTab;
    readonly CsvInsightChart = CsvInsightChart;

    readonly chartOptions: WritableSignal<ChartOptions> = signal({
        [InsightsChart.AGGREGATED_BOOSTERS_SCAN_COUNT]: {
            chartSortBy: ChartSortBy.DESC,
            nfcs: [],
        },
        [InsightsChart.AGGREGATED_BOOSTERS_WHEEL_OF_FORTUNE_GIFTS_DISTRIBUTION]: {
            tableSortOptions: undefined,
        },
        [InsightsChart.AGGREGATED_BOOSTERS_WHEEL_OF_FORTUNE_ESTIMATED_REVIEWS_COUNT]: {
            tableSortOptions: undefined,
        },
        [InsightsChart.AGGREGATED_BOOSTERS_PRIVATE_REVIEWS_COUNT]: {
            chartSortBy: ChartSortBy.DESC,
            hiddenDatasetIndexes: [],
        },
        [InsightsChart.AGGREGATED_BOOSTERS_REVIEWS_COUNT]: {
            chartSortBy: ChartSortBy.DESC,
            hiddenDatasetIndexes: [],
        },
    });

    readonly hasTotemsReviewsData: Signal<boolean> = computed(() => {
        const data = this.totemReviewsData();
        const reviewsPerPlatform = data?.reviewsPerPlatform ?? [];
        const hasReviews = reviewsPerPlatform.some((platform) => platform.reviewsCount.length > 0);
        return hasReviews;
    });

    readonly hasPrivateReviewsData: Signal<boolean> = computed(() => {
        const data = this.totemReviewsData();
        if (data) {
            const hasData = data.privateReviewsPerRating.length > 0;
            return hasData;
        }
        return false;
    });

    readonly selectedTotems = toSignal(this._aggregatedStatisticsFiltersContext.selectedTotems$, { initialValue: [] });

    readonly isLoading = computed(() => this.isLoadingBoosters() || this.isLoadingGifts());

    readonly isDownloadStatisticsResumeEnabled: Signal<boolean> = toSignal(
        this._experimentationService.isFeatureEnabled$('release-download-statistics-resume'),
        {
            initialValue: false,
        }
    );

    ngOnInit(): void {
        this._initBoostersData();
        this._initGiftsData();
    }

    onHiddenDatasetIndexesChange(chart: InsightsChart, value: number[]): void {
        this.chartOptions.update((chartOptions) => ({
            ...chartOptions,
            [chart]: {
                ...chartOptions[chart],
                hiddenDatasetIndexes: value,
            },
        }));
    }

    onChartSortByChange(chart: InsightsChart, value: ChartSortBy): void {
        this.chartOptions.update((chartOptions) => ({
            ...chartOptions,
            [chart]: {
                ...chartOptions[chart],
                chartSortBy: value,
            },
        }));
    }

    private _initBoostersData(): void {
        combineLatest([this.restaurants$, this.aggregatedStatsFilters$, this._aggregatedStatisticsFiltersContext.selectedTotems$])
            .pipe(
                filter(
                    ([restaurants, aggregatedStatsFilters, totems]) =>
                        restaurants.length > 0 && isDateSetOrGenericPeriod(aggregatedStatsFilters.dates) && totems.length > 0
                ),
                tap(() => {
                    this.isErrorBoosters.set(false);
                    this.isLoadingBoosters.set(true);
                    this.isErrorTotemReviews.set(false);
                    this.isLoadingTotemReviews.set(true);
                }),
                debounceTime(500),
                filter(
                    ([_, aggregatedStatsFilters]) =>
                        isNotNil(aggregatedStatsFilters.dates.startDate) && isNotNil(aggregatedStatsFilters.dates.endDate)
                ),
                switchMap(
                    ([restaurants, aggregatedStatsFilters, nfcs]: [Restaurant[], AggregatedStatisticsState['filters'], LightNfc[]]) => {
                        const { dates, comparisonPeriod } = aggregatedStatsFilters;
                        const data = this._getBoostersAggregatedDataServiceV2.getChartsData({
                            nfcs,
                            dates,
                            restaurantIds: restaurants.map((r) => r._id.toString()),
                            comparisonPeriod,
                        });
                        this.onNfcChange(nfcs.map((nfc) => nfc.id));
                        return data;
                    }
                ),
                takeUntilDestroyed(this._destroyRef)
            )
            .subscribe({
                next: (data) => {
                    this.boostersData.set(data);
                    this._store.dispatch(AggregatedStatisticsActions.editBoosterStatsData({ data }));
                    this.isLoadingBoosters.set(false);
                    this.isLoadingTotemReviews.set(false);
                },
                error: (error) => {
                    console.error('error >>', error);
                    this.isErrorBoosters.set(true);
                    this.isLoadingBoosters.set(false);
                    this.isErrorTotemReviews.set(true);
                    this.isLoadingTotemReviews.set(false);
                },
            });
    }

    private _initGiftsData(): void {
        combineLatest([this.restaurantsWithBoosterPackActivated$, this.aggregatedStatsFilters$])
            .pipe(
                filter(
                    ([restaurants, aggregatedStatsFilters]) =>
                        restaurants.length > 0 && isDateSetOrGenericPeriod(aggregatedStatsFilters.dates)
                ),
                tap(() => {
                    this.isErrorGifts.set(false);
                    this.isLoadingGifts.set(true);
                }),
                debounceTime(500),
                filter(
                    ([_, aggregatedStatsFilters]) =>
                        isNotNil(aggregatedStatsFilters.dates.startDate) && isNotNil(aggregatedStatsFilters.dates.endDate)
                ),
                switchMap(([restaurants, aggregatedStatsFilters]: [Restaurant[], AggregatedStatisticsState['filters']]) =>
                    this._getBoostersAggregatedDataServiceV2.getGiftsData({
                        dates: aggregatedStatsFilters.dates,
                        restaurantIds: restaurants.map((r) => r._id.toString()),
                        comparisonPeriod: aggregatedStatsFilters.comparisonPeriod,
                    })
                ),
                takeUntilDestroyed(this._destroyRef)
            )
            .subscribe({
                next: (data) => {
                    this.giftsData.set(data);
                    this.isLoadingGifts.set(false);
                },
                error: () => {
                    this.isErrorGifts.set(true);
                    this.isLoadingGifts.set(false);
                },
            });
    }

    ngAfterViewInit(): void {
        setTimeout(() =>
            this.topOfComponent?.nativeElement.scrollIntoView({
                behavior: 'smooth',
                block: 'start',
                inline: 'nearest',
            })
        );
        this._store.dispatch(AggregatedStatisticsActions.editSelectedPage({ page: PlatformFilterPage.BOOSTERS }));
    }

    downloadInsightsSummary(): void {
        this._downloadInsightsSummaryService.downloadAggregatedCsvInsightsSummary({
            insightsTab: InsightsTab.AGGREGATED_SUMMARY,
            csvChart: CsvInsightChart.AGGREGATED_INSIGHTS_SUMMARY,
        });
    }

    openDownloadStatisticsModal(): void {
        combineLatest([
            this._store.select(AggregatedStatisticsSelectors.selectDatesFilter),
            this._store.select(AggregatedStatisticsSelectors.selectComparisonPeriod),
            this._store.select(AggregatedStatisticsSelectors.selectRestaurantIdsFilter),
        ])
            .pipe(
                take(1),
                switchMap(([{ startDate, endDate }, comparisonPeriod, restaurantIds]) => {
                    if (!startDate || !endDate) {
                        this._toastService.openErrorToast(
                            this._translateService.instant('aggregated_statistics.download_insights_modal.please_select_dates')
                        );
                        return EMPTY;
                    }
                    return this._customDialogService
                        .open<DownloadInsightsModalComponent, DownloadInsightsModalData>(DownloadInsightsModalComponent, {
                            height: undefined,
                            data: {
                                tab: InsightsTab.AGGREGATED_BOOSTERS,
                                filters: {
                                    dates: { startDate, endDate },
                                    restaurantIds,
                                    comparisonPeriod,
                                    nfcIds: this.nfcIds(),
                                },
                                chartOptions: this.chartOptions(),
                            },
                        })
                        .afterClosed();
                })
            )
            .subscribe();
    }

    onSortByChange(chart: InsightsChart, value: ChartSortBy): void {
        this.chartOptions.update((options) => ({ ...options, [chart]: { ...options[chart], chartSortBy: value } }));
    }

    onNfcChange(value: string[]): void {
        this.nfcIds.set(value);
    }

    onTableSortOptionsChange(chart: InsightsChart, value: Sort): void {
        this.chartOptions.update((options) => ({
            ...options,
            [chart]: {
                ...options[chart],
                tableSortOptions: value,
            },
        }));
    }
}
