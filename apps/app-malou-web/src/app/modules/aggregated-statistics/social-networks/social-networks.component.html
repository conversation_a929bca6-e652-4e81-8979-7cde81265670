<div class="flex h-full flex-col gap-y-4 py-4">
    @if (!screenSizeService.isPhoneScreen) {
        <ng-container [ngTemplateOutlet]="filtersTemplate"></ng-container>
    }

    <div class="flex h-full flex-col gap-y-4 px-8.5">
        @if (screenSizeService.isPhoneScreen) {
            <ng-container [ngTemplateOutlet]="filtersTemplate"></ng-container>
        }
        @if (((restaurants$ | async)?.length ?? 0) > 1) {
            @if (((platformKeys$ | async)?.length ?? 0) !== 0 || !hasPlatformsLoadedOnce()) {
                <div class="flex flex-col gap-y-6">
                    <ng-container [ngTemplateOutlet]="top3Posts"></ng-container>
                    <ng-container [ngTemplateOutlet]="postsInsightsTable"></ng-container>
                </div>
            } @else {
                <div class="flex flex-col items-center py-6">
                    <img class="mb-6 h-20 w-20" alt="Taster illustration" [src]="'Taster' | illustrationPathResolver" />
                    <span class="malou-text-14--bold mb-2">{{ 'common.no_data' | translate }}</span>
                    <span class="malou-text-10--regular">{{ 'aggregated_statistics.errors.platforms_not_selected' | translate }}</span>
                </div>
            }
        } @else {
            <div class="flex flex-col items-center py-6">
                <img class="mb-6 h-20 w-20" alt="Taster illustration" [src]="'Taster' | illustrationPathResolver" />
                <span class="malou-text-14--bold mb-2">{{ 'common.no_data' | translate }}</span>
                <span class="malou-text-10--regular">{{ 'aggregated_statistics.errors.select_at_least_2_businesses' | translate }}</span>
            </div>
        }
    </div>
</div>

<ng-template #filtersTemplate>
    <div class="flex items-end gap-4 px-8.5 md:flex-col md:items-center md:px-0">
        <div class="flex-1 sm:w-full">
            <app-aggregated-statistics-filters [page]="PlatformFilterPage.SOCIAL_NETWORKS"></app-aggregated-statistics-filters>
        </div>
        @if (isDownloadStatisticsResumeEnabled()) {
            <app-menu-button-v2 [text]="'statistics.common.download_statistics' | translate" [size]="MenuButtonSize.LARGE">
                <button
                    class="flex !h-12 !px-5"
                    mat-menu-item
                    [disabled]="((restaurants$ | async)?.length ?? 0) < 2 || isLoading()"
                    (click)="openDownloadStatisticsModal()">
                    <span class="malou-text-14--regular text-malou-color-text-2">
                        {{ InsightsTab.SOCIAL_NETWORKS | enumTranslate: 'insights_tab_name' }}
                    </span>
                </button>
                <button class="flex !h-12 !px-5" mat-menu-item [disabled]="isLoading()" (click)="downloadInsightsSummary()">
                    <span class="malou-text-14--regular text-malou-color-text-2">
                        {{ 'statistics.common.download_statistics_sub_text' | translate }}
                    </span>
                </button>
            </app-menu-button-v2>
        } @else {
            <button
                class="malou-btn-raised--primary !h-12 sm:w-full"
                mat-raised-button
                [disabled]="((restaurants$ | async)?.length ?? 0) < 2 || isLoading()"
                (click)="openDownloadStatisticsModal()">
                {{ 'aggregated_statistics.common.download_statistics' | translate }}
            </button>
        }
    </div>
</ng-template>

<ng-template #top3Posts>
    @if (isPostInsightsV2Enabled()) {
        <app-aggregated-top-3-posts-v2 (isLoadingEvent)="isTop3PostsLoading.set($event)"></app-aggregated-top-3-posts-v2>
    } @else {
        <app-aggregated-top-3-posts (isLoadingEvent)="isTop3PostsLoading.set($event)"></app-aggregated-top-3-posts>
    }
</ng-template>

<ng-template #postsInsightsTable>
    @if (isPostInsightsV2Enabled()) {
        <app-aggregated-posts-insights-table-v2
            (tableSortOptionsChange)="onTableSortOptionsChange(InsightsChart.AGGREGATED_PUBLICATIONS_TABLE, $event)"
            (isLoadingEvent)="isInsightsLoading.set($event)">
        </app-aggregated-posts-insights-table-v2>
    } @else {
        <app-aggregated-posts-insights-table
            (tableSortOptionsChange)="onTableSortOptionsChange(InsightsChart.AGGREGATED_PUBLICATIONS_TABLE, $event)"
            (isLoadingEvent)="isInsightsLoading.set($event)">
        </app-aggregated-posts-insights-table>
    }
</ng-template>
