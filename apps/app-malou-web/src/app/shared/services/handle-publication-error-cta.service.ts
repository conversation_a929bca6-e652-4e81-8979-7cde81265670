import { inject, Injectable } from '@angular/core';
import { Router } from '@angular/router';
import { TranslateService } from '@ngx-translate/core';
import { Observable, Subject, switchMap } from 'rxjs';

import { PlatformKey, PublicationErrorCode } from '@malou-io/package-utils';

import { RestaurantsService } from ':core/services/restaurants.service';
import { ToastService } from ':core/services/toast.service';
import { SocialPostsV2Service } from ':modules/posts-v2/social-posts/social-posts.service';

@Injectable({ providedIn: 'root' })
export class HandlePublicationErrorCtaService {
    private readonly _router = inject(Router);
    private readonly _restaurantsService = inject(RestaurantsService);
    private readonly _socialPostsV2Service = inject(SocialPostsV2Service);
    private readonly _toastService = inject(ToastService);
    private readonly _translateService = inject(TranslateService);

    execute(
        mostRecentPublicationErrorCode: PublicationErrorCode,
        postId: string,
        platformKey?: PlatformKey
    ): Observable<'was_published_now' | 'open_edition_modal' | void> {
        const subject = new Subject<'was_published_now' | 'open_edition_modal'>();
        const publishNowSubscribeObject = {
            next: (): void => {
                subject.next('was_published_now');
                subject.complete();
            },
            error: (err: unknown): void => {
                console.error(err);
                const message = this._translateService.instant('social_post_item.publish_now_error');
                this._toastService.openErrorToast(message);
                subject.complete();
            },
        };
        switch (mostRecentPublicationErrorCode) {
            case PublicationErrorCode.CONNECTION_EXPIRED:
                this._router.navigate(['restaurants', this._restaurantsService.currentRestaurant._id, 'settings', 'platforms'], {
                    queryParams: { platformKey },
                });
                subject.complete();
                break;
            case PublicationErrorCode.USER_MISSING_APPROPRIATE_ROLE:
                window.open('https://help.malou.io/en/articles/3418242', '_blank');
                subject.complete();
                break;
            case PublicationErrorCode.USER_NEEDS_TO_LOG_IN:
                window.open('https://www.facebook.com', '_blank');
                subject.complete();
                break;
            case PublicationErrorCode.INVALID_LOCATION:
                subject.next('open_edition_modal');
                subject.complete();
                break;
            case PublicationErrorCode.USER_NOT_VISIBLE:
                this._socialPostsV2Service
                    .removeInstagramCollaboratorsAndTags$({ postId })
                    .pipe(switchMap(() => this._socialPostsV2Service.publishPostNow$({ postId })))
                    .subscribe(publishNowSubscribeObject);
                break;
            case PublicationErrorCode.USER_NEEDS_TO_LOG_IN_TO_IG_APP:
            case PublicationErrorCode.UNKNOWN_ERROR:
                this._socialPostsV2Service.publishPostNow$({ postId }).subscribe(publishNowSubscribeObject);
                break;
            default:
                console.error(`Unhandled publication error code: ${mostRecentPublicationErrorCode}`);
                subject.complete();
                break;
        }
        return subject.asObservable();
    }
}
