import { Injectable } from '@angular/core';
import { isNil, mergeWith } from 'lodash';
import { catchError, forkJoin, map, Observable, of, switchMap } from 'rxjs';

import { ReviewResponseDto } from '@malou-io/package-dto';
import { MalouPeriod } from '@malou-io/package-utils';

import { StarValue } from ':core/constants';
import { PrivateReviewsService } from ':core/services/private-reviews.service';
import { BoostersStatisticsDataV2 } from ':modules/statistics/boosters/boosters.interface';
import { BoostersDataFetchingServiceV2 } from ':modules/statistics/boosters/services/get-boosters-data-v2.service';
import { ScanForRestaurantInsights } from ':shared/models/scan';
import { SummarySectionsData, SummarySectionsDataFilters } from ':shared/services/csv-services/insights/summary/summary.interface';

const DEFAULT_REVIEWS_RATING = {
    [StarValue.UNKNOWN]: 0,
    [StarValue.ONE]: 0,
    [StarValue.TWO]: 0,
    [StarValue.THREE]: 0,
    [StarValue.FOUR]: 0,
    [StarValue.FIVE]: 0,
};

@Injectable({ providedIn: 'root' })
export class SummaryCsvInsightsBoosterSectionService {
    constructor(
        private readonly _boostersDataFetchingServiceV2: BoostersDataFetchingServiceV2,
        private readonly _privateReviewsService: PrivateReviewsService,
        private readonly _boostersDataFetchingService: BoostersDataFetchingServiceV2
    ) {}

    execute(filters: SummarySectionsDataFilters['booster']): Observable<{
        current: SummarySectionsData['booster'] | null;
        previous: SummarySectionsData['booster'] | null;
    }> {
        const { startDate, endDate, nfcs, comparisonPeriod } = filters;

        return forkJoin([
            this._boostersDataFetchingServiceV2.getChartsData({
                nfcs,
                dates: { startDate, endDate, period: MalouPeriod.CUSTOM },
                comparisonPeriod,
            }),
            of({
                startDate,
                endDate,
                period: MalouPeriod.CUSTOM,
            }),
        ]).pipe(
            switchMap(([boostersData, dateAndPeriod]) => {
                const { scans, previousScans } = boostersData;
                const scanIds = [...scans.map((scan) => scan.id), ...previousScans.map((scan) => scan.id)];
                const privateReviewsDtos$ = scanIds.length
                    ? this._privateReviewsService.search({ scanIds }).pipe(map((apiResult) => apiResult.data))
                    : of<ReviewResponseDto[]>([]);
                const previousPrivateReviews$ = privateReviewsDtos$.pipe(
                    map((privateReviewDtos) =>
                        privateReviewDtos.filter(
                            (privateReviewDto) => new Date(privateReviewDto.socialCreatedAt) < dateAndPeriod.startDate!
                        )
                    )
                );
                const currentPrivateReviews$ = privateReviewsDtos$.pipe(
                    map((privateReviewDtos) =>
                        privateReviewDtos.filter(
                            (privateReviewDto) => new Date(privateReviewDto.socialCreatedAt) >= dateAndPeriod.startDate!
                        )
                    )
                );
                return forkJoin({
                    boosterData: of(boostersData),
                    privateReviewsDto: currentPrivateReviews$,
                    previousPrivateReviewsDto: previousPrivateReviews$,
                    giftsData: this._boostersDataFetchingService.getGiftsData({ dates: dateAndPeriod, comparisonPeriod }),
                });
            }),
            map(({ boosterData, privateReviewsDto, previousPrivateReviewsDto, giftsData }) => {
                const estimatedReviewCount = this._getEstimatedReviewCountOnPeriod(boosterData);

                const estimatedCurrentReviewCountPerRating = {
                    ...DEFAULT_REVIEWS_RATING,
                };
                const estimatedPreviousReviewCountPerRating = {
                    ...DEFAULT_REVIEWS_RATING,
                };
                const estimatedCurrentPrivateReviewCountPerRating = {
                    ...DEFAULT_REVIEWS_RATING,
                };
                const estimatedPreviousPrivateReviewCountPerRating = {
                    ...DEFAULT_REVIEWS_RATING,
                };

                boosterData.scans.reduce((acc, curr) => {
                    const starValue = curr.matchedReview?.rating ?? curr.starClicked ?? StarValue.UNKNOWN;
                    acc[this._getStarValueFromNumber(starValue)] += 1;
                    return acc;
                }, estimatedCurrentReviewCountPerRating);

                boosterData.previousScans.reduce((acc, curr) => {
                    const starValue = curr.matchedReview?.rating ?? curr.starClicked ?? StarValue.UNKNOWN;
                    acc[this._getStarValueFromNumber(starValue)] += 1;
                    return acc;
                }, estimatedPreviousReviewCountPerRating);

                privateReviewsDto.reduce((acc, curr) => {
                    const starValue = curr.rating || StarValue.UNKNOWN;
                    acc[this._getStarValueFromNumber(starValue)] += 1;
                    return acc;
                }, estimatedCurrentPrivateReviewCountPerRating);

                previousPrivateReviewsDto.reduce((acc, curr) => {
                    const starValue = curr.rating || StarValue.UNKNOWN;
                    acc[this._getStarValueFromNumber(starValue)] += 1;
                    return acc;
                }, estimatedPreviousPrivateReviewCountPerRating);

                const mergeCurrentReviewCountPerRating = mergeWith(
                    estimatedCurrentReviewCountPerRating,
                    estimatedCurrentPrivateReviewCountPerRating,
                    (objValue, srcValue) => objValue + srcValue
                );

                const mergePreviousReviewCountPerRating = mergeWith(
                    estimatedPreviousReviewCountPerRating,
                    estimatedPreviousPrivateReviewCountPerRating,
                    (objValue, srcValue) => objValue + srcValue
                );

                const totalWinningGifts = giftsData.giftsInsightsPerGift.reduce((acc, curr) => acc + (curr.giftDrawCount ?? 0), 0);
                const totalRetrievedGifts = giftsData.giftsInsightsPerGift.reduce(
                    (acc, curr) => acc + (curr.retrievedGiftDrawCount ?? 0),
                    0
                );
                const previousTotalWinningGifts = giftsData.giftsInsightsPerGift.reduce(
                    (acc, curr) => acc + (curr.previousGiftDrawCount ?? 0),
                    0
                );
                const previousTotalRetrievedGifts = giftsData.giftsInsightsPerGift.reduce(
                    (acc, curr) => acc + (curr.previousRetrievedGiftDrawCount ?? 0),
                    0
                );

                const currentTop3Totems = this._getTopTotems(boosterData.scans);
                const previousTop3Totems = this._getTopTotems(boosterData.previousScans);

                return {
                    current: {
                        gainedPrivateReviewCount: privateReviewsDto.length,
                        gainedPublicReviewCount: estimatedReviewCount.estimatedTotemsCurrentReviewCountOnPeriod,
                        totalTotemScanCount: boosterData.scans.length,
                        totalWofScanCount: boosterData.wheelOfFortuneCount,
                        giftCount: totalRetrievedGifts,
                        winnerCount: totalWinningGifts,
                        gainedReviewCountPerRating: mergeCurrentReviewCountPerRating,
                        totemCountEvolution: estimatedReviewCount.estimatedTotemsReviewCountDifferenceWithPreviousPeriod,
                        topTotems: currentTop3Totems,
                    },
                    previous: {
                        gainedPrivateReviewCount: previousPrivateReviewsDto.length,
                        gainedPublicReviewCount: estimatedReviewCount.estimatedTotemsPreviousTotemsReviewCount,
                        totalTotemScanCount: boosterData.previousScans.length,
                        totalWofScanCount: boosterData.previousWheelOfFortuneCount,
                        giftCount: previousTotalRetrievedGifts,
                        winnerCount: previousTotalWinningGifts,
                        gainedReviewCountPerRating: mergePreviousReviewCountPerRating,
                        totemCountEvolution: estimatedReviewCount.estimatedTotemsReviewCountDifferenceWithPreviousPeriod,
                        topTotems: previousTop3Totems,
                    },
                };
            }),
            catchError(() => of({ current: null, previous: null }))
        );
    }

    private _getEstimatedReviewCountOnPeriod(boostersStatisticsData: BoostersStatisticsDataV2): {
        estimatedTotemsCurrentReviewCountOnPeriod: number | null;
        estimatedTotemsPreviousTotemsReviewCount: number | null;
        estimatedTotemsReviewCountDifferenceWithPreviousPeriod: number | null;
    } {
        const totemData = {
            ...boostersStatisticsData,
            scans: boostersStatisticsData.scans.filter((scan) => !scan.isWheelOfFortuneRelated()),
            previousScans: boostersStatisticsData.previousScans.filter((scan) => !scan.isWheelOfFortuneRelated()),
        };

        if (isNil(totemData)) {
            return {
                estimatedTotemsCurrentReviewCountOnPeriod: null,
                estimatedTotemsPreviousTotemsReviewCount: null,
                estimatedTotemsReviewCountDifferenceWithPreviousPeriod: null,
            };
        }
        const { scans, previousScans } = totemData;

        const estimatedTotemsCurrentReviewCountOnPeriod = scans.filter((scan) => scan.matchedReview?.id).length;
        const estimatedTotemsPreviousTotemsReviewCount = previousScans.filter((scan) => scan.matchedReview?.id).length;
        const estimatedTotemsReviewCountDifferenceWithPreviousPeriod = this._computeEstimatedReviewCountDifferenceWithPreviousPeriod(
            estimatedTotemsCurrentReviewCountOnPeriod,
            previousScans
        );

        return {
            estimatedTotemsCurrentReviewCountOnPeriod: estimatedTotemsCurrentReviewCountOnPeriod,
            estimatedTotemsPreviousTotemsReviewCount: estimatedTotemsPreviousTotemsReviewCount,
            estimatedTotemsReviewCountDifferenceWithPreviousPeriod: estimatedTotemsReviewCountDifferenceWithPreviousPeriod,
        };
    }

    private _computeEstimatedReviewCountDifferenceWithPreviousPeriod(
        currentPeriodCount: number,
        previousPeriodScans: ScanForRestaurantInsights[]
    ): number | null {
        if (!currentPeriodCount) {
            return null;
        }
        const previousScanCount = previousPeriodScans.filter((scan) => scan.matchedReview?.id).length;
        return currentPeriodCount - previousScanCount;
    }

    private _getStarValueFromNumber(star: number): StarValue {
        return {
            [-1]: StarValue.UNKNOWN,
            [1]: StarValue.ONE,
            [2]: StarValue.TWO,
            [3]: StarValue.THREE,
            [4]: StarValue.FOUR,
            [5]: StarValue.FIVE,
        }[star] as StarValue;
    }

    private _getTopTotems(scans: ScanForRestaurantInsights[]): string[] {
        const scanCountByTotem = scans.reduce((acc, curr) => {
            const totemName = curr.nfcSnapshot.name ?? curr.nfcSnapshot.chipName;
            if (totemName) {
                if (!acc[totemName]) {
                    acc[totemName] = 0;
                }
                acc[totemName] += 1;
            }
            return acc;
        }, {});

        return Object.keys(scanCountByTotem)
            .sort((a, b) => scanCountByTotem[b] - scanCountByTotem[a])
            .slice(0, 3)
            .map((totemName) => totemName);
    }
}
