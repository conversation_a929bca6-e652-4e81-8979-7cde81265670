import { PlatformAccessStatus, PlatformCategory, PlatformGroup, PlatformKey } from '@malou-io/package-utils';

import { PlatformState } from './platform';

export interface SimpleMappedPlatform {
    key: PlatformKey;
    fullName: string;
    platformData: any;
}

export interface MappedPlatform<Category> {
    category: Category;
    displayedCategoryName: string;
    key: PlatformKey;
    fullName: string;
    platformData?: any;
    state?: PlatformState;
    accessStatus?: PlatformAccessStatus;
}

export type MappedPlatforms<T extends PlatformCategory | PlatformGroup> = {
    [key in T]?: MappedPlatform<T>[];
};

export type MappedPlatformsCategory = MappedPlatforms<PlatformCategory>;

export type MappedPlatformsGroup = MappedPlatforms<PlatformGroup>;
