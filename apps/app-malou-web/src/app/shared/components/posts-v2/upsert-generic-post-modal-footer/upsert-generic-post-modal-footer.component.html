<div class="flex items-center justify-between border-t border-malou-color-border-primary px-6 py-4">
    <button class="malou-text-14--semibold !text-malou-color-text-2" mat-button [attr.data-testid]="cancelBtnTestId()" (click)="onCancel()">
        {{ 'common.cancel' | translate }}
    </button>

    <div class="flex items-center gap-x-3">
        @if (selectedOption() !== SubmitPublicationStatus.NOW) {
            <app-post-date-picker
                [selectedDate]="selectedDate() ?? null"
                [size]="PostDatePickerSize.BIG"
                [withBorder]="true"
                [disabled]="isDisabled()"
                [isStory]="isStory()"
                [recurrentStoryFrequency]="recurrentStoryFrequency()"
                [restaurantId]="restaurantId"
                [sourceForPostSchedule]="PostSource.SOCIAL"
                [programmedPostPlatformKeys]="programmedPostPlatformKeys()"
                (selectedDateChange)="onSelectedDateChange($event)"
                (recurrentStoryFrequencyChange)="onRecurrentStoryFrequencyChange($event)"></app-post-date-picker>
        }

        <div class="wrapper relative">
            @if (finalPostErrors().length > 0) {
                <div class="post-errors-tooltip flex flex-col gap-y-2">
                    @for (error of finalPostErrors(); track error) {
                        <div class="malou-text-12--regular text-white">- {{ error }}</div>
                    }
                </div>
            }
            <app-dropdown-button
                [(selectedOption)]="selectedOption"
                [options]="DROPDOWN_OPTIONS"
                [displayOption]="displayPublicationStatusOptionFn()"
                [size]="MenuButtonSize.LARGE"
                [loading]="isSubmitting()"
                [disabled]="disabled() || isDisabled()"
                [btnTestId]="'upsert-social-post-save-dropdown-btn'"
                [menuTestId]="'upsert-social-post-save-dropdown-menu-btn'"
                (buttonClick)="onDropdownButtonClick($event)">
            </app-dropdown-button>
        </div>
    </div>
</div>
