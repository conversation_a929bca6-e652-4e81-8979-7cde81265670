{"author": "", "dependencies": {"@malou-io/package-models": "workspace:*", "@malou-io/package-utils": "workspace:*", "dotenv": "^16.4.5", "glob": "^10.3.12", "luxon": "^3.5.0", "mongoose": "^6.10.4", "tslib": "^2.8.1", "uuid": "^8.3.2"}, "description": "", "devDependencies": {"@malou-io/package-config": "workspace:*", "@playwright/test": "^1.46.0", "@trivago/prettier-plugin-sort-imports": "^5.2.0", "@types/eslint": "^8.48.0", "@types/luxon": "^1.27.1", "@types/node": "^20.14.11", "@types/uuid": "^8.3.4", "@typescript-eslint/eslint-plugin": "^7.0.0", "@typescript-eslint/parser": "^7.0.0", "eslint": "^8.48.0", "prettier": "^3.5.3", "tsc-alias": "^1.8.8"}, "keywords": [], "license": "ISC", "name": "@malou-io/app-e2e", "scripts": {"build": "tsc --build && tsc-alias", "e2e": "playwright test tests/main/", "format": "prettier --write \"**/*.{ts,tsx,md}\"", "format:check": "prettier \"**/*.{ts,tsx,md}\" --check", "lint": "eslint \"**/*.ts\"", "lint-fix": "eslint \"**/*.ts\" --fix", "lint-staged": "lint-staged --no-stash", "playwright-ui": "playwright test --ui", "tce2e": "playwright test tests/time-consuming-tests/"}, "version": "1.0.0"}