export function getRestaurantsIds(): {
    leNinaRestaurantId: string;
    leNinoRestaurantId: string;
    leNinaBrandRestaurantId: string;
    leBaratieBrandRestaurantId: string;
} {
    const leNinaRestaurantId = process.env.RESTAURANT_ID_LE_NINA;
    const leNinoRestaurantId = process.env.RESTAURANT_ID_LE_NINO;
    const leNinaBrandRestaurantId = process.env.RESTAURANT_ID_LE_NINA_BRAND;
    const leBaratieBrandRestaurantId = process.env.RESTAURANT_ID_LE_BARATIE_BRAND;
    if (!leNinaRestaurantId || !leNinoRestaurantId || !leNinaBrandRestaurantId || !leBaratieBrandRestaurantId) {
        throw new Error('Restaurant ids envs not initialized, exist.... ');
    }
    return {
        leNinaRestaurantId,
        leNinoRestaurantId,
        leNinaBrandRestaurantId,
        leBaratieBrandRestaurantId,
    };
}
