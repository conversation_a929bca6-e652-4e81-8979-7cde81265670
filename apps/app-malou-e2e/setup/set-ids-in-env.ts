import { RestaurantModel, ReviewModel } from '@malou-io/package-models';

import { REVIEW_SOCIAL_IDS } from ':constants';

async function setIdsInEnv() {
    await setRestaurantIdsInEnv();
    await setReviewsIdsToEnv();
}

async function setRestaurantIdsInEnv() {
    const leNinaRestaurant = await RestaurantModel.findOne(
        {
            placeId: process.env.LE_NINA_SOCIAL_ID!,
        },
        {
            _id: 1,
        }
    );

    if (!leNinaRestaurant) {
        throw new Error('Restaurant E2E leNinaRestaurant not found ! exit...');
    }

    process.env.RESTAURANT_ID_LE_NINA = leNinaRestaurant._id.toString();

    const leNinoRestaurant = await RestaurantModel.findOne(
        {
            placeId: process.env.LE_NINO_SOCIAL_ID!,
        },
        {
            _id: 1,
        }
    );

    if (!leNinoRestaurant) {
        throw new Error('Restaurant E2E leNinoRestaurant not found ! exit...');
    }

    process.env.RESTAURANT_ID_LE_NINO = leNinoRestaurant._id.toString();

    const leNinaBrandRestaurant = await RestaurantModel.findOne(
        {
            placeId: process.env.LE_NINA_BRAND_SOCIAL_ID!,
        },
        {
            _id: 1,
        }
    );

    if (!leNinaBrandRestaurant) {
        throw new Error('Restaurant E2E leNinaBrandRestaurant not found ! exit...');
    }

    process.env.RESTAURANT_ID_LE_NINA_BRAND = leNinaBrandRestaurant._id.toString();

    const leBaratieBrandRestaurant = await RestaurantModel.findOne(
        {
            placeId: process.env.LE_BARATIE_BRAND_SOCIAL_ID!,
        },
        {
            _id: 1,
        }
    );

    if (!leBaratieBrandRestaurant) {
        throw new Error('Restaurant E2E leBaratieBrandRestaurant not found ! exit...');
    }

    process.env.RESTAURANT_ID_LE_BARATIE_BRAND = leBaratieBrandRestaurant._id.toString();
}

async function setReviewsIdsToEnv() {
    const facebookEnglishReview = await ReviewModel.findOne(
        {
            socialId: REVIEW_SOCIAL_IDS.FACEBOOK_ENGLISH_REVIEW,
            restaurantId: process.env.RESTAURANT_ID_LE_NINA,
        },
        { _id: 1 }
    );

    if (!facebookEnglishReview) {
        throw new Error('Review E2E facebookEnglishReview not found ! exit...');
    }

    process.env.FACEBOOK_ENGLISH_REVIEW_ID = facebookEnglishReview._id.toString();

    const gestionClientsReview = await ReviewModel.findOne(
        {
            socialId: REVIEW_SOCIAL_IDS.CLIENTS_MANAGER,
        },
        { _id: 1 }
    );

    if (!gestionClientsReview) {
        throw new Error('Review E2E gestionClientsReview not found ! exit...');
    }

    process.env.GESTION_CLIENTS_GMB_REVIEW_ID = gestionClientsReview._id.toString();

    const heleneReview = await ReviewModel.findOne(
        {
            socialId: REVIEW_SOCIAL_IDS.HELENE,
        },
        { _id: 1 }
    );

    if (!heleneReview) {
        throw new Error('Review E2E heleneReview not found ! exit...');
    }

    process.env.HELENE_GMB_REVIEW_ID = heleneReview._id.toString();
}

export default setIdsInEnv;
