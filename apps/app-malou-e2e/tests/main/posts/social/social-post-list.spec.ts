import { expect, it } from 'baseTest';

import { PlatformKey, PostPublicationStatus } from '@malou-io/package-utils';

it("should display the restaurant's social posts", async ({ page }) => {
    await page.goto(`/restaurants/${process.env.RESTAURANT_ID_LE_BARATIE_BRAND}/dashboard`);

    // Get all the posts
    const posts = page.locator('app-social-post-item');
    await expect(posts).toHaveCount(4);

    // Check posts one by one
    const draftSocialPost = posts.nth(0);
    const publishedInstagramSocialPost = posts.nth(1);
    const publishedFacebookSocialPost = posts.nth(2);
    const pendingSocialPost = posts.nth(3);

    // Text
    await expect(draftSocialPost).toHaveText(/Draft social post/);
    await expect(publishedInstagramSocialPost).toHaveText(/Published Instagram social post/);
    await expect(publishedFacebookSocialPost).toHaveText(/Published Facebook social post/);
    await expect(pendingSocialPost).toHaveText(/Pending social post/);

    // Status
    await expect(draftSocialPost.getByTestId(`social-post-status-${PostPublicationStatus.DRAFT}`)).toBeVisible();
    await expect(publishedInstagramSocialPost.getByTestId(`social-post-status-${PostPublicationStatus.PUBLISHED}`)).toBeVisible();
    await expect(publishedFacebookSocialPost.getByTestId(`social-post-status-${PostPublicationStatus.PUBLISHED}`)).toBeVisible();
    await expect(pendingSocialPost.getByTestId(`social-post-status-${PostPublicationStatus.PENDING}`)).toBeVisible();

    // Platforms
    await expect(draftSocialPost.getByTestId(`social-post-platform-${PlatformKey.INSTAGRAM}`)).toBeVisible();
    await expect(draftSocialPost.getByTestId(`social-post-platform-${PlatformKey.FACEBOOK}`)).toBeVisible();
    await expect(draftSocialPost.getByTestId(`social-post-platform-${PlatformKey.TIKTOK}`)).toBeVisible();
    await expect(draftSocialPost.getByTestId(`social-post-platform-${PlatformKey.MAPSTR}`)).toBeVisible();
    await expect(publishedInstagramSocialPost.getByTestId(`social-post-platform-${PlatformKey.INSTAGRAM}`)).toBeVisible();
    await expect(publishedFacebookSocialPost.getByTestId(`social-post-platform-${PlatformKey.FACEBOOK}`)).toBeVisible();
    await expect(pendingSocialPost.getByTestId(`social-post-platform-${PlatformKey.FACEBOOK}`)).toBeVisible();

    // Author
    await expect(draftSocialPost.getByText('AC')).toBeVisible();
    await expect(publishedInstagramSocialPost.getByText('AC')).toBeVisible();
    await expect(publishedFacebookSocialPost.getByText('AC')).toBeVisible();
    await expect(pendingSocialPost.getByText('AC')).toBeVisible();
});
