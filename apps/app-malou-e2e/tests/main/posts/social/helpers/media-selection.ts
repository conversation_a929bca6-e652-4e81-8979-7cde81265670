import { expect } from ':baseTest';
import { Locator, Page } from '@playwright/test';

export async function addMediaToSocialPost({
    page,
    folderName,
    imgCount,
    videoCount,
}: {
    page: Page;
    folderName?: string;
    imgCount: number;
    videoCount: number;
}) {
    await expect(async () => {
        await page.getByTestId('add-first-media-menu-btn').click();
        await page.getByTestId('add-media-from-gallery-btn').click();

        const modalPicker = page.locator('app-media-picker-modal');
        if (folderName) {
            await modalPicker.getByText(folderName).click();
        }

        // used to wait for weird loading image refresh
        await page.waitForTimeout(2000);

        const items = await page.getByTestId('media-picker-item').all();

        const videoItems: Locator[] = [];
        const imgItems: Locator[] = [];
        for (const item of items) {
            const videoIcon = item.getByTestId('media-picker-item-video-icon');
            if ((await videoIcon.count()) > 0) {
                videoItems.push(item);
            } else {
                imgItems.push(item);
            }
        }

        if (imgItems.length < imgCount) {
            throw new Error(`Not enough images in folder ${folderName}, expected ${imgCount} but got ${imgItems.length}`);
        }
        if (videoItems.length < videoCount) {
            throw new Error(`Not enough videos in folder ${folderName}, expected ${videoCount} but got ${videoItems.length}`);
        }

        for (let i = 0; i < imgCount; i++) {
            await imgItems[i]!.click();
        }

        for (let i = 0; i < videoCount; i++) {
            await videoItems[i]!.click();
        }

        await modalPicker.getByTestId('media-picker-add-media-btn').click();
    }).toPass();
}

export async function waitForMediasToBeUploaded({ page }: { page: Page }) {
    await expect(async () => {
        const uploadingMedias = await page.getByTestId('media_thumbnail_list_uploading_media').all();
        for (const media of uploadingMedias) {
            await expect(media).not.toBeVisible();
        }
    }).toPass();
}
