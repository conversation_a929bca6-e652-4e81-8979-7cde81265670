import { expect, it } from 'baseTest';

it('should open post creation modal from dashboard', async ({ page }) => {
    await page.goto(`/restaurants/${process.env.RESTAURANT_ID_LE_NINA}/dashboard`);

    // Go to next week to be sure to be on the future
    await page.getByTestId('dashboard-next-week-or-month-btn').click();

    // Wait for the calendar to be loaded
    const skeletons = await page.locator('app-skeleton').all();
    for (const skeleton of skeletons) {
        await expect(skeleton).not.toBeVisible();
    }

    // Hover on the first day of the week
    const firstDay = page.locator('.calendar-day').first();
    await firstDay.hover();

    await firstDay.getByTestId('dashboard-create-post-menu-btn').click();
    await page.getByTestId('dashboard-create-social-post-btn').click();

    const modal = page.locator('app-upsert-social-post-modal');

    // we target close-upsert-social-post-btn because it's one of the first button to be visible
    // it ensures that the modal is loaded
    await expect(modal.getByTestId('close-upsert-social-post-btn')).toBeVisible();

    // Cancel the post creation to prevent polluting the database
    await modal.getByTestId('cancel-upsert-social-post-btn').first().click();
});

it('should open reel creation modal from dashboard', async ({ page }) => {
    await page.goto(`/restaurants/${process.env.RESTAURANT_ID_LE_NINA}/dashboard`);

    // Go to next week to be sure to be on the future
    await page.getByTestId('dashboard-next-week-or-month-btn').click();

    // Wait for the calendar to be loaded
    const skeletons = await page.locator('app-skeleton').all();
    for (const skeleton of skeletons) {
        await expect(skeleton).not.toBeVisible();
    }

    // Hover on the first day of the week
    const firstDay = page.locator('.calendar-day').first();
    await firstDay.hover();

    await firstDay.getByTestId('dashboard-create-post-menu-btn').click();
    await page.getByTestId('dashboard-create-reel-btn').click();

    const modal = page.locator('app-upsert-social-post-modal');

    // we target close-upsert-social-post-btn because it's one of the first button to be visible
    // it ensures that the modal is loaded
    await expect(modal.getByTestId('close-upsert-social-post-btn')).toBeVisible();

    // Cancel the post creation to prevent polluting the database
    await modal.getByTestId('cancel-upsert-social-post-btn').first().click();
});
