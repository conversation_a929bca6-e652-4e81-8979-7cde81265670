import { DateTime } from 'luxon';
import { getRestaurantsIds } from 'setup/utils';
import { v4 } from 'uuid';

import { IPost, newDbId, PostModel, toDbId } from '@malou-io/package-models';
import {
    PlatformKey,
    PostPublicationStatus,
    PostSource,
    PostType,
    SeoPostTopic,
    SocialAttachmentsMediaTypes,
} from '@malou-io/package-utils';

async function setupPosts() {
    const { leBaratieBrandRestaurantId } = getRestaurantsIds();

    await PostModel.deleteMany({ restaurantId: leBaratieBrandRestaurantId });

    const postsToCreate = getPostsToCreate(leBaratieBrandRestaurantId);

    await PostModel.create(postsToCreate);
}

function getPostsToCreate(restaurantId: string): IPost[] {
    return [...getSocialPostsToCreate(restaurantId), ...getStoriesToCreate(restaurantId), ...getSeoPostsToCreate(restaurantId)];
}

export const DRAFT_SOCIAL_POST_TEXT = 'Draft social post';
export const PENDING_SOCIAL_POST_TEXT = 'Pending social post';
export const PUBLISHED_INSTAGRAM_SOCIAL_POST_TEXT = 'Published Instagram social post';
export const PUBLISHED_FACEBOOK_SOCIAL_POST_TEXT = 'Published Facebook social post';

function getSocialPostsToCreate(restaurantId: string): IPost[] {
    const in2Days = DateTime.now().plus({ days: 2 }).toJSDate();
    const tomorrow = DateTime.now().plus({ days: 1 }).toJSDate();
    const yesterday = DateTime.now().minus({ days: 1 }).toJSDate();
    return [
        // Draft social posts
        {
            _id: newDbId(),
            keys: [PlatformKey.FACEBOOK, PlatformKey.INSTAGRAM, PlatformKey.TIKTOK, PlatformKey.MAPSTR],
            text: DRAFT_SOCIAL_POST_TEXT,
            isStory: false,
            postTopic: SeoPostTopic.STANDARD,
            published: PostPublicationStatus.DRAFT,
            tries: 0,
            plannedPublicationDate: DateTime.fromJSDate(in2Days).set({ hour: 10, minute: 0 }).toJSDate(),
            author: {
                _id: newDbId(),
                name: 'Ana',
                lastname: 'Candellier',
                picture: null,
            },
            authors: [
                {
                    _id: newDbId(),
                    name: 'Ana',
                    lastname: 'Candellier',
                    picture: null,
                },
            ],
            restaurantId: toDbId(restaurantId),
            attachments: [],
            postType: PostType.IMAGE,
            bindingId: v4(),
            shouldDuplicateInOtherPlatforms: false,
            userTagsList: [],
            source: PostSource.SOCIAL,
            socialAttachments: [],
            userTags: [],
            createdAt: new Date('2022-06-24T09:52:28.804+00:00'),
            updatedAt: new Date('2022-06-24T09:52:28.804+00:00'),
            callToAction: undefined,
            event: undefined,
            offer: undefined,
            feedbackId: undefined,
            sortDate: DateTime.fromJSDate(in2Days).set({ hour: 10, minute: 0 }).toJSDate(),
        },
        // Pending social posts
        {
            _id: newDbId(),
            keys: [PlatformKey.FACEBOOK],
            text: PENDING_SOCIAL_POST_TEXT,
            isStory: false,
            postTopic: SeoPostTopic.STANDARD,
            published: PostPublicationStatus.PENDING,
            tries: 0,
            plannedPublicationDate: DateTime.fromJSDate(tomorrow).set({ hour: 10, minute: 0 }).toJSDate(),
            author: {
                _id: newDbId(),
                name: 'Ana',
                lastname: 'Candellier',
                picture: null,
            },
            authors: [
                {
                    _id: newDbId(),
                    name: 'Ana',
                    lastname: 'Candellier',
                    picture: null,
                },
            ],
            restaurantId: toDbId(restaurantId),
            attachments: [],
            postType: PostType.IMAGE,
            bindingId: v4(),
            shouldDuplicateInOtherPlatforms: false,
            userTagsList: [],
            source: PostSource.SOCIAL,
            socialAttachments: [],
            userTags: [],
            createdAt: new Date('2022-06-24T09:52:28.804+00:00'),
            updatedAt: new Date('2022-06-24T09:52:28.804+00:00'),
            callToAction: undefined,
            event: undefined,
            offer: undefined,
            feedbackId: undefined,
            sortDate: DateTime.fromJSDate(tomorrow).set({ hour: 10, minute: 0 }).toJSDate(),
        },
        // Published social posts
        {
            _id: newDbId(),
            keys: [],
            key: PlatformKey.FACEBOOK,
            text: PUBLISHED_FACEBOOK_SOCIAL_POST_TEXT,
            isStory: false,
            postTopic: SeoPostTopic.STANDARD,
            published: PostPublicationStatus.PUBLISHED,
            tries: 0,
            plannedPublicationDate: DateTime.fromJSDate(yesterday).set({ hour: 10, minute: 0 }).toJSDate(),
            author: {
                _id: newDbId(),
                name: 'Ana',
                lastname: 'Candellier',
                picture: null,
            },
            authors: [
                {
                    _id: newDbId(),
                    name: 'Ana',
                    lastname: 'Candellier',
                    picture: null,
                },
            ],
            restaurantId: toDbId(restaurantId),
            attachments: [],
            postType: PostType.IMAGE,
            bindingId: v4(),
            shouldDuplicateInOtherPlatforms: false,
            userTagsList: [],
            source: PostSource.SOCIAL,
            socialAttachments: [],
            userTags: [],
            createdAt: new Date('2022-06-24T09:52:28.804+00:00'),
            updatedAt: new Date('2022-06-24T09:52:28.804+00:00'),
            callToAction: undefined,
            event: undefined,
            offer: undefined,
            feedbackId: undefined,
            sortDate: DateTime.fromJSDate(yesterday).set({ hour: 10, minute: 0 }).toJSDate(),
        },
        {
            _id: newDbId(),
            keys: [],
            key: PlatformKey.INSTAGRAM,
            text: PUBLISHED_INSTAGRAM_SOCIAL_POST_TEXT,
            isStory: false,
            postTopic: SeoPostTopic.STANDARD,
            published: PostPublicationStatus.PUBLISHED,
            tries: 0,
            plannedPublicationDate: DateTime.fromJSDate(yesterday).set({ hour: 9, minute: 0 }).toJSDate(),
            author: {
                _id: newDbId(),
                name: 'Ana',
                lastname: 'Candellier',
                picture: null,
            },
            authors: [
                {
                    _id: newDbId(),
                    name: 'Ana',
                    lastname: 'Candellier',
                    picture: null,
                },
            ],
            restaurantId: toDbId(restaurantId),
            attachments: [],
            postType: PostType.IMAGE,
            bindingId: v4(),
            shouldDuplicateInOtherPlatforms: false,
            userTagsList: [],
            source: PostSource.SOCIAL,
            socialAttachments: [
                {
                    type: SocialAttachmentsMediaTypes.IMAGE,
                    urls: {
                        original: 'https://picsum.photos/200/300',
                    },
                },
            ],
            userTags: [],
            createdAt: new Date('2022-06-24T09:52:28.804+00:00'),
            updatedAt: new Date('2022-06-24T09:52:28.804+00:00'),
            callToAction: undefined,
            event: undefined,
            offer: undefined,
            feedbackId: undefined,
            sortDate: DateTime.fromJSDate(yesterday).set({ hour: 9, minute: 0 }).toJSDate(),
        },
    ];
}

function getStoriesToCreate(_restaurantId: string): IPost[] {
    return [];
}

function getSeoPostsToCreate(_restaurantId: string): IPost[] {
    return [];
}

export default setupPosts;
