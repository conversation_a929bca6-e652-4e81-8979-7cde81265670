import fs from 'fs';
import semver from 'semver';

const fileReader = (filePath) => {
    if (!filePath || typeof filePath !== 'string') {
        throw new Error('invalid filePath');
    }
    const raw = fs.readFileSync(filePath, { encoding: 'utf-8' });
    // Split into lines, trim whitespace, ignore empty lines and comments
    const lines = raw
        .split('\n')
        .map((line) => line.trim())
        .filter((line) => line && !line.startsWith('#'));

    if (lines.length === 0) {
        throw new Error('No valid Node version found in .nvmrc');
    }

    return lines[0]; // e.g. "22.15.0"
};

const nvChecker = () => {
    const nodeVersion = fileReader('../../.nvmrc');
    if (!semver.satisfies(process.version, `>=${nodeVersion}`)) {
        throw new Error(`Your current node version: ${process.version} does not satisfy the required version ${nodeVersion}`);
    }
};

nvChecker();
