{
    "extends": "../../tsconfig.options.json",
    "compilerOptions": {
        "rootDir": ".",
        "outDir": "./dist",
        "target": "es2017",
        "lib": ["es2021"],
        "noImplicitAny": false,
        "strict": true,
        "strictNullChecks": true,
        "declarationMap": false,
        "declaration": false,
        "composite": false,
        "types": ["jest", "node"],
        // Set `sourceRoot` to  "/" to strip the build path prefix
        // from generated source code references.
        // This improves issue grouping in Sentry.
        "sourceRoot": "/",
        "paths": {
            ":env": ["./src/env.ts"],
            ":config": ["./src/config.ts"],
            ":main": ["./src/main.ts"],
            ":helpers/*": ["./src/helpers/*"],
            ":services/*": ["./src/services/*"],
            ":microservices/*": ["./src/microservices/*"],
            ":plugins/*": ["./src/plugins/*"],
            ":modules/*": ["./src/modules/*"],
            ":providers/*": ["./src/providers/*"],
            ":agenda-jobs/*": ["./src/agenda-jobs/*"],
            ":tasks/*": ["./src/tasks/*"],
            ":queues/*": ["./src/queues/*"],
            ":di": ["./src/di.ts"]
        }
    },
    "references": [
        {
            "path": "../../packages/malou-package-models"
        },
        {
            "path": "../../packages/service-interfaces"
        },
        {
            "path": "../../packages/malou-utils"
        },
        {
            "path": "../../packages/malou-dto"
        },
        {
            "path": "../../packages/malou-emails"
        },
        {
            "path": "../../packages/crawlers"
        }
    ],
    "include": ["src", "fixtures"],
    "exclude": ["src/tasks/**/*.js"]
}
