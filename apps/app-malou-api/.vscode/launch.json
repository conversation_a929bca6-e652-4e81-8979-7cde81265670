{"configurations": [{"name": "start local", "type": "node", "nodeVersionHint": 22.15, "request": "launch", "runtimeVersion": "22.15.0", "runtimeExecutable": "pnpm", "experimentalNetworking": "off", "env": {"FORCE_COLOR": "1"}, "runtimeArgs": ["run", "start-local"], "smartStep": true, "outputCapture": "std"}, {"name": "start dev", "type": "node", "nodeVersionHint": 22.15, "request": "launch", "runtimeVersion": "22.15.0", "runtimeExecutable": "pnpm", "experimentalNetworking": "off", "env": {"FORCE_COLOR": "1"}, "runtimeArgs": ["run", "start-dev"], "smartStep": true, "outputCapture": "std"}, {"name": "start staging", "type": "node", "nodeVersionHint": 22.15, "request": "launch", "runtimeVersion": "22.15.0", "runtimeExecutable": "pnpm", "experimentalNetworking": "off", "env": {"FORCE_COLOR": "1"}, "runtimeArgs": ["run", "start-staging"], "smartStep": true, "outputCapture": "std"}, {"name": "start production", "type": "node", "nodeVersionHint": 22.15, "request": "launch", "runtimeVersion": "22.15.0", "runtimeExecutable": "pnpm", "experimentalNetworking": "off", "env": {"FORCE_COLOR": "1"}, "runtimeArgs": ["run", "start-production"], "smartStep": true, "outputCapture": "std"}, {"type": "node", "request": "launch", "name": "Jest: debug Integration", "runtimeVersion": "22.15.0", "runtimeExecutable": "pnpm", "experimentalNetworking": "off", "runtimeArgs": ["run", "test:integration:debug"], "console": "integratedTerminal"}, {"type": "node", "request": "launch", "name": "Jest: debug Unit", "runtimeVersion": "22.15.0", "runtimeExecutable": "pnpm", "experimentalNetworking": "off", "runtimeArgs": ["run", "test:unit:debug"], "console": "integratedTerminal"}, {"name": "Jest: debug unit current file", "type": "node", "request": "launch", "outputCapture": "std", "runtimeVersion": "18.19.0", "runtimeExecutable": "pnpm", "experimentalNetworking": "off", "runtimeArgs": ["run", "test:unit:single", "${file}"]}, {"type": "node", "request": "launch", "name": "Jest: debug integration current file", "runtimeVersion": "22.15.0", "runtimeExecutable": "pnpm", "experimentalNetworking": "off", "runtimeArgs": ["run", "test:integration:single", "${file}"], "console": "integratedTerminal"}]}