export const Config = {
    env: process.env.NODE_ENV,
    iAmA: process.env.I_AM_A,
    settings: {
        adminEmail: process.env.ADMIN_APP_EMAIL,
        adminUpdatesNotificationEmail: '<EMAIL>',
        adminAnalyticsEmail: '<EMAIL>',
        maloupeUrl: process.env.MALOUPE_URL,
        sendEmail: process.env.SEND_EMAIL === 'true',
        sessionExpireTimeMs: 1000 * 60 * 10,
        apiRoute: {
            v1: '/api/v1',
            maloupe: '/api/maloupe',
        },
    },
    services: {
        agenda: {
            isConsumingJobs: process.env.START_AGENDA_CONSUMER === 'true',
        },
        aiHashtagGenService: {
            functionName: process.env.AI_HASHTAG_GEN_SERVICE_FUNCTION_NAME ?? 'serverlessHashtagGeneratorDevelopment',
        },
        aiKeywordsBreakdownService: {
            functionName: process.env.AI_KEYWORDS_BREAKDOWN_SERVICE_FUNCTION_NAME ?? 'serverlessKeywordsBreakdownDevelopment',
        },
        aiTextGenerationService: {
            functionName: process.env.AI_TEXT_GENERATION_SERVICE_FUNCTION_NAME ?? 'aiTextGenerationDevelopment',
        },
        aiMediaDescriptionGenerationService: {
            functionName: process.env.AI_MEDIA_DESCRIPTION_GENERATION_SERVICE_FUNCTION_NAME ?? 'serverlessAiImageAnalysisDevelopment',
        },
        aiStoreLocatorContentGenerationService: {
            functionName:
                process.env.AI_STORE_LOCATOR_CONTENT_GENERATION_SERVICE_FUNCTION_NAME ?? 'storeLocatorContentGeneratorDevelopment',
        },
        aiTextDuplicationService: {
            functionName: process.env.AI_TEXT_DUPLICATION_FUNCTION_NAME ?? 'serverlessAiPostDuplicationDevelopment',
        },
        aiReviewsService: {
            functionName: process.env.AI_REVIEWS_FUNCTION_NAME ?? 'serverlessAiReviewsDevelopment',
        },
        aiSemanticAnalysisService: {
            functionName: process.env.AI_SEMANTIC_ANALYSIS_FUNCTION_NAME ?? 'serverlessAiSemanticAnalysisDevelopment',
        },
        aws: {
            region: process.env.AWS_REGION,
            key: process.env.AWS_KEY,
            secret: process.env.AWS_SECRET,
        },
        awsMediaConvert: {
            role: process.env.AWS_MEDIA_CONVERT_ROLE ?? 'arn:aws:iam::515192135070:role/service-role/MediaConvert_Default_Role',
        },
        browserless: {
            baseEndpoint: process.env.BROWSERLESS_BROWSER_WS_ENDPOINT,
            token: process.env.BROWSERLESS_TOKEN,
        },
        diagnosticKeywordsGenerator: {
            functionName: process.env.DIAGNOSTIC_KEYWORDS_GENERATOR_FUNCTION_NAME ?? 'serverlessKeywordDiagnosisDevelopment',
        },
        fixedIpCaller: {
            url: process.env.FIXED_IP_CALLER_URL,
        },
        github: {
            appId: '1177309',
            installationId: '62588651',
            clientId: 'Iv23liCBtH9NXtZ6VYyv',
            clientSecret: process.env.GITHUB_APP_CLIENT_SECRET,
            privateKey: process.env.GITHUB_APP_PRIVATE_KEY,
        },
        keywordsGeneratorV2: {
            functionName: process.env.KEYWORDS_GENERATOR_FUNCTION_NAME_V2 ?? 'keywordsGeneratorV2Development',
        },
        keywordsScore: {
            functionName: process.env.KEYWORDS_SCORE_FUNCTION_NAME ?? 'keywordsScoreDevelopment',
        },
        mapstr: {
            bearerToken: process.env.MAPSTR_BEARER_TOKEN ?? 'bear',
        },
        nodeCrawlers: {
            apiKey: process.env.NODE_CRAWLER_API_KEY,
        },
        openai: {
            reviewAnalysisPromptInstructions:
                'Analyze customer reviews, breaking them down into the smallest meaningful chunks, and classify each chunk into one of these categories Food, Service, Price, Atmosphere, Hygiene, Expeditiousness and Overall Experience. Also, determine the sentiment of each chunk. Return results in JSON format including text, category, sentiment, and probability of accuracy. Make sure that all results have the exact same format and that each chunk is categorized under only one category. the expected format should be as follows: \n\n [{ "text": "chunk text", "category": "chunk category", "sentiment": "chunk sentiment", "probability": number }]. \n\n',
            apiKey: process.env.OPENAI_API_KEY,
            requestsPerMinuteLimit: 20,
            defaultResult: [
                {
                    segment: 'The food was delicious',
                    tag: 'food',
                    sentiment: 'positive',
                },
                {
                    segment: 'The service was friendly',
                    tag: 'service',
                    sentiment: 'positive',
                },
                {
                    segment: 'The prices were reasonable',
                    tag: 'price',
                    sentiment: 'positive',
                },
                {
                    segment: 'The atmosphere was cozy',
                    tag: 'atmosphere',
                    sentiment: 'positive',
                },
                {
                    segment: 'The restaurant was clean',
                    tag: 'hygiene',
                    sentiment: 'positive',
                },
                {
                    segment: 'The food was served quickly',
                    tag: 'expeditiousness',
                    sentiment: 'positive',
                },
                {
                    segment: 'I had a great overall experience',
                    tag: 'overall_experience',
                    sentiment: 'positive',
                },
            ],
        },
        openTelemetry: {
            collector: {
                host: process.env.OPEN_TELEMETRY_COLLECTOR_HOST,
                port: process.env.OPEN_TELEMETRY_COLLECTOR_PORT ?? 4318, // 4318 is for HTTP/Proto protocol
                resourceDetectors: process.env.OTEL_NODE_RESOURCE_DETECTORS, // Use to define auto discovered metrics attributes, https://opentelemetry.io/docs/zero-code/js/configuration/#sdk-resource-detector-configuration
                debug: process.env.OPEN_TELEMETRY_COLLECTOR_DEBUG ?? false,
                attributes: process.env.OTEL_RESOURCE_ATTRIBUTES,
            },
        },
        platformsScrapper: {
            url: process.env.PLATFORMS_SCRAPPER_URL,
            functionName: process.env.PLATFORMS_SCRAPPER_FUNCTION_NAME,
            authorization: process.env.PLATFORMS_SCRAPPER_AUTHORIZATION,
            retries: 3,
        },
        pubsub: {
            startSubscription: process.env.START_PUBSUB_SUBSCRIPTION === 'true',
        },
        puppeteer: {
            arn: process.env.PUPPETEER_SERVICE_ARN,
            authorization: process.env.PUPPETEER_SERVICE_AUTHORIZATION,
            retries: 3,
        },
        puppeteerV2: {
            functionName: process.env.PUPPETEER_SERVICE_FUNCTION_NAME ?? 'puppeteerLambdaLocal',
        },
        reviewsReport: {
            apiKey: process.env.REVIEWS_REPORT_API_KEY ?? 'ScFVF7Dvyw',
        },
        reviewsSemanticAnalysisOverview: {
            functionName: process.env.REVIEWS_SEMANTIC_ANALYSIS_OVERVIEW_FUNCTION_NAME ?? 'reviewsSemanticAnalysisOverviewDevelopment',
        },
        similarRestaurants: {
            functionName: process.env.SIMILAR_RESTAURANTS_FUNCTION_NAME ?? 'similarRestaurantsIdentificationDevelopment',
        },
        slack: {
            webhookUrls: {
                appAlerts: process.env.SLACK_APP_ALERTS_WEBHOOK_URL,
                slackAlerts: process.env.SLACK_ALERTS_WEBHOOK_URL,
            },
            techBot: {
                token: process.env.SLACK_TECH_BOT_TOKEN,
                signingSecret: process.env.SLACK_TECH_BOT_SIGNING_SECRET,
            },
        },
        searchKeywordsImpressions: {
            functionName: process.env.SEARCH_KEYWORDS_IMPRESSIONS_FUNCTION_NAME ?? 'searchKeywordsImpressionsDevelopment',
        },
        sqs: {
            awsMediaConvertVideoProgressQueueUrl:
                process.env.AWS_MEDIA_CONVERT_VIDEO_PROGRESS_QUEUE_URL ??
                'http://localhost:9324/000000000000/malou_sqs_aws_media_convert_video_progress',
            retryEmptyTranslationsQueueUrl:
                process.env.RETRY_EMPTY_TRANSLATIONS_QUEUE_URL ?? 'http://localhost:9324/000000000000/malou_sqs_retry_empty_translations',
            fetchKeywordsVolumeQueueUrl:
                process.env.FETCH_KEYWORDS_VOLUME_QUEUE_URL ?? 'http://localhost:9324/000000000000/malou_sqs_fetch_keywords_volume',
            fetchKeywordsVolumeQueueArn:
                process.env.FETCH_KEYWORDS_VOLUME_QUEUE_ARN ?? 'http://localhost:9324/000000000000/malou_sqs_fetch_keywords_volume',
            fetchKeywordsVolumeArnRole:
                'arn:aws:iam::515192135070:role/service-role/monthly_fetch_keywords_volume_production_scheduler_role',
            startSqsConsumer: process.env.START_SQS_CONSUMER === 'true',
            scrapperApiQueueUrl: process.env.SCRAPPER_API_QUEUE_URL ?? 'http://localhost:9324/000000000000/malou_sqs_scrapper_api',
            reviewsQueueUrl: process.env.REVIEWS_QUEUE_URL ?? 'http://localhost:9324/000000000000/malou_sqs_reviews_collect',
            publishPostQueueUrl: process.env.PUBLISH_POST_QUEUE_URL ?? 'http://localhost:9324/000000000000/malou_sqs_posts',
            thumbnailGeneratorQueueUrl:
                process.env.THUMBNAIL_GENERATOR_QUEUE_URL ?? 'http://localhost:9324/000000000000/malou_sqs_thumbnail_generator',
            reviewBoosterSnsQueueUrl:
                process.env.REVIEW_BOOSTER_SNS_QUEUE_URL ?? 'http://localhost:9324/000000000000/malou_sqs_review_booster_sns',
            dailyReviewsReportsStartQueueUrl:
                process.env.DAILY_REVIEWS_REPORTS_START_QUEUE_URL ??
                'http://localhost:9324/000000000000/malou_sqs_start_daily_reviews_reports',
            dailyReviewsReportsSendQueueUrl:
                process.env.DAILY_REVIEWS_REPORTS_SEND_QUEUE_URL ??
                'http://localhost:9324/000000000000/malou_sqs_send_daily_reviews_report',
            weeklyReviewsReportsStartQueueUrl:
                process.env.WEEKLY_REVIEWS_REPORTS_START_QUEUE_URL ??
                'http://localhost:9324/000000000000/malou_sqs_start_weekly_reviews_reports',
            weeklyReviewsReportsSendQueueUrl:
                process.env.WEEKLY_REVIEWS_REPORTS_SEND_QUEUE_URL ??
                'http://localhost:9324/000000000000/malou_sqs_send_weekly_reviews_report',
            weeklyPerformanceReportsStartQueueUrl:
                process.env.WEEKLY_PERFORMANCE_REPORTS_START_QUEUE_URL ??
                'http://localhost:9324/000000000000/malou_sqs_start_weekly_performance_reports',
            weeklyPerformanceReportsSendQueueUrl:
                process.env.WEEKLY_PERFORMANCE_REPORTS_SEND_QUEUE_URL ??
                'http://localhost:9324/000000000000/malou_sqs_send_weekly_performance_report',
            monthlyPerformanceReportsStartQueueUrl:
                process.env.MONTHLY_PERFORMANCE_REPORTS_START_QUEUE_URL ??
                'http://localhost:9324/000000000000/malou_sqs_start_monthly_performance_reports',
            monthlyPerformanceReportsSendQueueUrl:
                process.env.MONTHLY_PERFORMANCE_REPORTS_SEND_QUEUE_URL ??
                'http://localhost:9324/000000000000/malou_sqs_send_monthly_performance_report',
            createMessageQueuesDailyInsightsQueueUrl:
                process.env.CREATE_MESSAGE_QUEUES_DAILY_INSIGHTS_QUEUE_URL ??
                'http://localhost:9324/000000000000/malou_sqs_create_message_queues_daily_insights',
            dailySaveInsightsQueueUrl:
                process.env.DAILY_SAVE_INSIGHTS_QUEUE_URL ?? 'http://localhost:9324/000000000000/malou_sqs_daily_save_insights',
            keywordsGenerationProcessingQueueUrl:
                process.env.KEYWORDS_GENERATION_PROCESSING_QUEUE_URL ??
                'http://localhost:9324/000000000000/malou_sqs_keywords_generation_processing',
            keywordsFailedGenerationProcessingQueueUrl:
                process.env.KEYWORDS_FAILED_GENERATION_PROCESSING_QUEUE_URL ??
                'http://localhost:9324/000000000000/malou_sqs_keywords_failed_generation_processing',
            createMessageQueuesMonthlySaveRoiInsightsQueueUrl:
                process.env.CREATE_MESSAGE_QUEUES_MONTHLY_SAVE_ROI_INSIGHTS_QUEUE_URL ??
                'http://localhost:9324/000000000000/malou_sqs_create_message_queues_monthly_save_roi_insights',
            monthlySaveRoiInsightsQueueUrl:
                process.env.MONTHLY_ROI_SAVE_INSIGHTS_QUEUE_URL ?? 'http://localhost:9324/000000000000/malou_sqs_monthly_save_roi_insights',
            createMessagesToPerformMonthlyUpdateSimilarRestaurantsQueueUrl:
                process.env.CREATE_MESSAGES_MONTHLY_UPDATE_SIMILAR_RESTAURANTS_QUEUE_URL ??
                'http://localhost:9324/000000000000/malou_sqs_create_messages_monthly_update_similar_restaurants',
            monthlyUpdateSimilarRestaurantsQueueUrl:
                process.env.MONTHLY_UPDATE_SIMILAR_RESTAURANTS_QUEUE_URL ??
                'http://localhost:9324/000000000000/malou_sqs_monthly_update_similar_restaurants',
            reviewsCatchUpQueueUrl:
                process.env.REVIEWS_CATCH_UP_QUEUE_URL ?? 'http://localhost:9324/000000000000/malou_sqs_reviews_catch_up',
            ratingsCatchUpQueueUrl:
                process.env.RATINGS_CATCH_UP_QUEUE_URL ?? 'http://localhost:9324/000000000000/malou_sqs_ratings_catch_up',
            refreshStalePlatformReviewsQueueUrl:
                process.env.REFRESH_STALE_PLATFORM_REVIEWS_QUEUE_URL ??
                'http://localhost:9324/000000000000/malou_sqs_refresh_stale_platform_reviews',
            monthlyCheckRestaurantsEligibilityToActivateRoiQueueUrl:
                process.env.MONTHLY_CHECK_RESTAURANTS_ELIGIBILITY_TO_ACTIVATE_ROI ??
                'http://localhost:9324/000000000000/malou_sqs_monthly_check_restaurants_eligibility_to_activate_roi',
            dailyReviewReminderNotificationsQueueUrl:
                process.env.DAILY_NEGATIVE_REVIEWS_EMAIL_NOTIFICATIONS_QUEUE_URL ??
                'http://localhost:9324/000000000000/malou_sqs_daily_nagative_reviews_email_notifications',
            createNewReviewsNotificationQueueUrl:
                process.env.CREATE_NEW_REVIEWS_NOTIFICATION_QUEUE_URL ??
                'http://localhost:9324/000000000000/malou_sqs_create_new_reviews_notification',
            createPostErrorNotificationQueueUrl:
                process.env.CREATE_POST_ERROR_NOTIFICATION_QUEUE_URL ??
                'http://localhost:9324/000000000000/malou_sqs_create_post_error_notification',
            createCommentsNotificationQueueUrl:
                process.env.CREATE_COMMENTS_NOTIFICATION_QUEUE_URL ??
                'http://localhost:9324/000000000000/malou_sqs_create_comments_notification',
            createMessageNotificationQueueUrl:
                process.env.CREATE_MESSAGE_NOTIFICATION_QUEUE_URL ??
                'http://localhost:9324/000000000000/malou_sqs_create_message_notification',
            createMentionsNotificationQueueUrl:
                process.env.CREATE_MENTIONS_NOTIFICATION_QUEUE_URL ??
                'http://localhost:9324/000000000000/malou_sqs_create_mentions_notification',
            synchronizeRecentPostsQueueUrl:
                process.env.SYNCHRONIZE_RECENT_POSTS_QUEUE_URL ?? 'http://localhost:9324/000000000000/malou_sqs_synchronize_recent_posts',
            createPlatformDisconnectedNotificationQueueUrl:
                process.env.CREATE_PLATFORM_DISCONNECTED_NOTIFICATION_QUEUE_URL ??
                'http://localhost:9324/000000000000/malou_sqs_create_platform_disconnected_notification',
            createInfoUpdateErrorNotificationQueueUrl:
                process.env.CREATE_INFO_UPDATE_ERROR_NOTIFICATION_QUEUE_URL ??
                'http://localhost:9324/000000000000/malou_sqs_create_info_update_error_notification',
            fetchReviewSemanticAnalysisQueueUrl:
                process.env.FETCH_REVIEW_SEMANTIC_ANALYSIS_QUEUE_URL ??
                'http://localhost:9324/000000000000/malou_sqs_fetch_review_semantic_analysis',
            fetchReviewSemanticAnalysisFifoQueueUrl:
                process.env.FETCH_REVIEW_SEMANTIC_ANALYSIS_FIFO_QUEUE_URL ??
                'http://localhost:9324/000000000000/malou_sqs_fetch_review_semantic_analysis.fifo',
            createMessageMonthlySaveSearchKeywordImpressionsQueueUrl:
                process.env.CREATE_MESSAGE_MONTHLY_SAVE_SEARCH_KEYWORD_IMPRESSIONS_QUEUE_URL ??
                'http://localhost:9324/000000000000/malou_sqs_create_message_monthly_save_keyword_search_impressions',
            monthlySaveKeywordSearchImpressionsQueueUrl:
                process.env.MONTHLY_SAVE_KEYWORD_SEARCH_IMPRESSIONS_QUEUE_URL ??
                'http://localhost:9324/000000000000/malou_sqs_monthly_save_keyword_search_impressions',
            previousReviewsAnalysisFifoQueueUrl:
                process.env.PREVIOUS_REVIEWS_ANALYSIS_FIFO_QUEUE_URL ??
                'http://localhost:9324/000000000000/malou_sqs_previous_reviews_analysis.fifo',
            dailyRefreshPostInsightsQueueUrl:
                process.env.DAILY_REFRESH_POST_INSIGHTS_QUEUE_URL ??
                'http://localhost:9324/000000000000/malou_sqs_daily_refresh_post_insights',
            generatePublicBusinessIdFifoQueueUrl:
                process.env.GENERATE_PUBLIC_BUSINESS_ID_FIFO_QUEUE_URL ??
                'http://localhost:9324/000000000000/malou_sqs_generate_public_business_id.fifo',
            synchronizeStoriesQueueUrl:
                process.env.SYNCHRONIZE_STORIES_QUEUE_URL ?? 'http://localhost:9324/000000000000/malou_sqs_synchronize_stories',
        },
        s3: {
            bucketName: process.env.AWS_BUCKET ?? 'malou-dev',
            reportsBucketName: 'malou-reports',
        },
        textTranslator: {
            functionName: process.env.TEXT_TRANSLATOR_FUNCTION_NAME ?? 'serverlessTextTranslatorDevelopment',
        },
        brightData: {
            ispProxyUsername: 'brd-customer-hl_94fbfe64-zone-isp_proxy1',
            ispProxyPassword: process.env.BRIGHT_DATA_ISP_PROXY_PASSWORD,
            residentialProxyUsername: 'brd-customer-hl_94fbfe64-zone-residential_proxy1',
            residentialProxyPassword: process.env.BRIGHT_DATA_RESIDENTIAL_PROXY_PASSWORD,
            proxyHost: 'brd.superproxy.io',
            proxyPort: 22225,
            residentialProxyUSUsername: 'brd-customer-hl_94fbfe64-zone-residential_proxy2',
            residentialProxyUSPassword: process.env.BRIGHT_DATA_RESIDENTIAL_PROXY_US_PASSWORD,
            residentialProxyUSPort: 33335,
        },
    },
    cryptoJs: {
        secret: process.env.ENCRYPT_SECRET,
    },
    platforms: {
        yelp: {
            api: {
                params: {
                    operationName: 'GetBusinessClaimability',
                    extensions: {
                        documentId: '6b7fa4a41d203c44429f854f8b57ce25f0ed61f765e1db7c04c30b5593239f74',
                    },
                },
                headers: {
                    'X-Requested-With': 'XMLHttpRequest',
                    'content-type': 'application/json',
                    'user-agent':
                        'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_5) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/85.0.4183.83 Safari/537.36',
                },
            },
        },
        foursquare: {
            api: {
                clientId: process.env.FOURSQUARE_CLIENT_ID,
                clientSecret: process.env.FOURSQUARE_CLIENT_SECRET,
            },
        },
        facebook: {
            api: {
                appId: process.env.FB_APP_ID,
                appSecret: process.env.FB_CLIENT_SECRET,
                appToken: process.env.FB_APP_TOKEN,
                apiVersion: process.env.FB_API_VERSION,
                redirectUri: process.env.FB_REDIRECT_URI,
                timeoutThresholdInMs: 30000,
                postsLimit: process.env.FB_POSTS_LIMIT ?? '25',
                nextCount: process.env.FB_NEXT_COUNT ?? '8',
            },
            webhooks: {
                token: process.env.FB_WEBHOOK_TOKEN,
            },
        },
        instagram: {
            publicApi: {
                url: 'https://www.instagram.com/web/search/topsearch/?context=blended',
            },
            api: {
                clientId: process.env.INSTAGRAM_CLIENT_ID,
                clientSecret: process.env.INSTAGRAM_CLIENT_SECRET,
            },
        },
        lafourchette: {
            api: {
                url: 'https://www.thefork.fr',
                params: {
                    search: 'autocomplete?sort%5BbrandId%5D=1&query%5Btext%5D=',
                    overview:
                        '?include=photos%2Cmenus%2Ctags%2CtagCategories%2Coffers%2Ccity%2CratingStats&filter%5Btags.category.id%5D=11%2C4%2C19%2C20%2C21%2C17%2C6%2C2%2C15%2C1%2C9%2C11%2C5%2C10%2C12%2C8%2C16',
                    reviews: '/relationships/reviews?include=author&sort=-mealDate&page%5Bnumber%5D=1&page%5Bsize%5D=4000',
                    recentReviews: '/relationships/reviews?include=author&sort=-mealDate&page%5Bnumber%5D=1&page%5Bsize%5D=100',
                },
                headers: {
                    'accept-language': 'fr-FR,fr;q=0.9,en-US;q=0.8,en;q=0.7',
                    'user-agent':
                        'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_14_6) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.87 Safari/537.36',
                    accept: '*/*',
                },
            },
            gqlApi: {
                reviewsLimitPerRequest: 50,
                maxReviewsPerLang: 1000,
                minReviewsPerLang: 100,
            },
            clientApi: {
                token: process.env.THEFORK_CLIENT_TOKEN,
                clientId: process.env.THEFORK_CLIENT_ID,
                clientSecret: process.env.THEFORK_CLIENT_SECRET,
            },
        },
        tripadvisor: {
            api: {
                params: {
                    search: '/TypeAheadJson?details=true&types=eat&matchKeywords=true&max=7&nearPages=true&action=API&query=',
                },
                headers: {
                    'Content-type': 'application/x-www-form-urlencoded; charset=utf-8',
                    'X-Requested-With': 'XMLHttpRequest',
                    'accept-language': 'fr-FR,fr;q=0.9,en-US;q=0.8,en;q=0.7',
                    Connection: 'keep-alive',
                    origin: 'https://www.tripadvisor.fr',
                    Cookie: 'TAUnique=%1%enc%3AEXq9CNnvpxn9E4QZPzgXnAbLGL%2Bqb5oicPosvdWVkSp6LxgY9CENLzcUVR4amhhJNox8JbUSTxk%3D; datadome=lwvW9aTs4wtP1kAB0t2ZQZCIk28_RfM9BJL1StWLDMV3stPLqeNKz1c2H2TnWJgy93j_kKhVWvbVTiyWLjbxJUk1hq4vsdQ1O4oSgZALZsPBV2TBHIfl_paTXxT8EfPU; SRT=TART_SYNC; TADCID=29dxs1uUvkFKbshyABQCmq6heh9ZSU2yA8SXn9Wv5H3VcjWg965HMkTP5dC1oegncimvEI0399h6yoyuaExFlHsQSdvb3x-B-Hg; TART=%1%enc%3AtQdxWoCiW%2BLEBKhAFVEu3C2a4Z%2BFfP2qPC%2BOfGzJSk0IRWX%2F1eR3XwK2CS6lfB030TQmULQR31s%3D; TASID=6B961F854D1D420E86D27293D3A446F6; TASameSite=1; __vt=gdwUGV5W5RBcyUgNABQCwRB1grfcRZKTnW7buAoPsS2zEhCOQhcl6MaCH1EV90q_FQ05rPAY3rpVQihpVzu1BfEDDpDmrbt-VKiP40u-18Q0MACe2NbJXfL6hJWmaPrGtu4LPeOxcC7FGrjPGi7daJA',
                    'user-agent':
                        'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/100.0.4896.127 Safari/537.36',
                },
            },
        },
        gmb: {
            api: {
                clientId: process.env.GMB_CLIENT_ID,
                clientSecret: process.env.GMB_CLIENT_SECRET,
                redirectUris: process.env.GMB_REDIRECT_URIS,
            },
        },
        pagesjaunes: {
            scrapWebsite: process.env.SCRAP_PAGES_JAUNES === 'true',
        },
        tiktok: {
            api: {
                clientId: process.env.TIKTOK_CLIENT_ID,
                clientSecret: process.env.TIKTOK_CLIENT_SECRET,
                redirectUri: process.env.TIKTOK_REDIRECT_URI,
                baseUri: 'https://open.tiktokapis.com/v2',
                authorizeUri: 'https://www.tiktok.com/v2/auth/authorize',
            },
        },
        ubereats: {
            api: {
                client: {
                    baseUri: 'https://api.uber.com/v1',
                    id: process.env.UBEREATS_CLIENT_ID,
                    secret: process.env.UBEREATS_CLIENT_SECRET,
                    redirectUri: process.env.UBEREATS_REDIRECT_URI,
                },
                graphQlUrl: 'https://merchants.ubereats.com/manager/graphql',
                managerUrl: 'https://merchants.ubereats.com/manager/api',
                headers: {
                    'Content-Type': 'application/json',
                    'Accept-Encoding': 'gzip, deflate, br',
                    Connection: 'keep-alive',
                    Authority: 'merchants.ubereats.com',
                    Accept: '*/*',
                    'Accept-Language': 'fr-FR,fr;q=0.9,en-US;q=0.8,en;q=0.7',
                    Origin: 'https://merchants.ubereats.com',
                    'User-Agent':
                        'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/100.0.4896.127 Safari/537.36',
                    'X-Csrf-Token': 'x',
                },
                accountId: '1ca22115-0bcb-4615-b187-6ad2bb8119a7',
            },
        },
        appleBusinessConnect: {
            api: {
                url: process.env.APPLE_BUSINESS_CONNECT_API_URL,
                version: process.env.APPLE_BUSINESS_CONNECT_API_VERSION || 'v3',
                companyId: process.env.APPLE_BUSINESS_CONNECT_COMPANY_ID,
                clientId: process.env.APPLE_BUSINESS_CONNECT_CLIENT_ID,
                clientSecret: process.env.APPLE_BUSINESS_CONNECT_CLIENT_SECRET,
            },
        },
        resy: {
            api: {
                apiKey: process.env.RESY_API_KEY,
                authUrl: 'https://auth.resy.com/1',
                publicUrl: 'https://api.resy.com/3/',
                ratingsUrl: 'https://api.resy.com/3/',
            },
        },
    },
    multer: {
        limits: {
            fileSize: {
                image: process.env.MAX_FILE_SIZE ?? 8 * 1000000 * 8,
                video: process.env.MAX_FILE_SIZE_VIDEO ?? 50 * 1000000 * 8,
            },
        },
    },
    storeLocator: {
        googleAnalyticsId: 'G-BM0FYMV1RQ',
    },
    geolocation: {
        modulo: process.env.modulo ?? 5,
        degreeDecimal: process.env.DEGREE_DECIMAL ?? 3,
        keywordRadiusSearchMeters: process.env.RADIUS ?? 800,
        // using 0.005 degrees for lats and longs samples gives squares of max ~555m side ->
        // diagonal: 555*squareroot(2) = ~800m to cover full square
        gmapsUrl: 'https://maps.googleapis.com/maps/api/',
        gmapsHostname: 'maps.googleapis.com',
        gmapsEndpoint: '/maps/api',
        gmapsApiKey: process.env.GMAPS_API_KEY,
        maloupeGmapsApiKey: process.env.MALOUPE_GMAPS_API_KEY,
        fetchRankings: process.env.FETCH_RANKINGS === 'true',

        /**
         * We are testing the new Google Maps API.
         * This is the share of requests that will use the new API (between 0 and 1,
         * 0 by default).
         */
        newGmapsApiRollout: parseFloat(process.env.NEW_GMAPS_API_ROLLOUT ?? '0'),
    },
    vendors: {
        keywordVolume: {
            surfer: {
                url: 'https://db2.keywordsur.fr/keyword_surfer_keywords?country=',
                keywordQuery: '&keywords=',
            },
        },
        keywordTool: {
            baseUrl: 'https://api.keywordtool.io',
            apiKey: process.env.KEYWORD_TOOL_API_KEY,
            googleSearchVolumeEndpoint: '/v2/search/volume/google',
            quotaEndpoint: '/v2/quota',
            maxApiCallsPerDay: 100,
            maxApiCallsPerMinute: 10,
            maxKeywordsPerRequest: 1000,
        },
        scrapperProxy: {
            baseUrl: 'https://api.proxycrawl.com/?token=',
            queryParams: '&request_headers=accept-language%3Afr-fr&url=',
        },
        proxy: {
            getProxyUrl: 'http://pubproxy.com/api/proxy?type=http',
            amount: 20,
        },
        front: {
            chatUserVerificationSecret: {
                fr: process.env.FRONT_CHAT_USER_VERIFICATION_SECRET,
                en: process.env.FRONT_CHAT_USER_VERIFICATION_SECRET_EN,
            },
        },
        yext: {
            baseUrl: process.env.YEXT_API_BASE_URL,
            apiKey: process.env.YEXT_API_KEY,
            version: process.env.YEXT_API_VERSION,
            skus: {
                knowledgeEngineStarter: process.env.YEXT_KNOWLEDGE_ENGINE_STARTER_SKU,
                usKnowledgeEngineStarter: process.env.YEXT_US_KNOWLEDGE_ENGINE_STARTER_SKU,
            },
        },
        yextPartner: {
            baseUrl: process.env.YEXT_PARTNER_API_BASE_URL ?? 'https://www.optimizelocation.com/partner/api/',
            malouPartnerId: process.env.YEXT_PARTNER_MALOU_ID ?? '125563', // Malou's partner ID, value from their web page's call (eg: https://www.optimizelocation.com/partner/malou/listing-report.html?name=philipps%20Restaurant&address=Turnerstra%C3%9Fe%209&zip=20357&city=Hamburg&phone=+494063735108&country=DE)
        },
        hubspot: {
            baseUrl: 'https://api.hsforms.com',
            informationFormIdentifiers: {
                portalId: '25820972',
                formId: '6aeedd0b-1336-4ffd-83e2-236e2dafc366',
            },
        },
        jimo: {
            apiUrl: process.env.JIMO_API_URL ?? 'https://karabor-harbour.usejimo.com/sse/webhook',
            apiKey: process.env.JIMO_API_KEY,
        },
        hyperline: {
            baseUrl: process.env.HYPERLINE_API_BASE_URL ?? 'https://api.hyperline.co/v1',
            apiKey: process.env.HYPERLINE_API_KEY,
        },
    },
    bricks: {
        ttlSeconds: process.env.BRICKS_CACHING_TTL ? parseInt(process.env.BRICKS_CACHING_TTL, 10) : 3600,
    },
    tests: {
        mockRestaurantId: process.env.MOCK_RESTAURANT_ID ?? '61840a47292c6d568b6e775d',
        mockUserId: process.env.MOCK_USER_ID ?? '613f251d7e91c550b8ff7b2b',
        mockUserId2: '5f7350bf914fca23c10f7432',
        mockRestaurantIdWithoutPerms: '5f9bd85bbf83560c8f17c4f4',
        mockNfcId: '62cc24143fc1556221171d08',
    },
    businessNotificationsAccountKey: {
        project_id: process.env.BN_PROJECT_ID,
        private_key: process.env.BN_PRIVATE_KEY ?? 'default',
        client_email: process.env.BN_CLIENT_EMAIL,
    },
    firebaseAccountKey: {
        project_id: process.env.FIREBASE_PROJECT_ID,
        private_key: process.env.FIREBASE_PRIVATE_KEY,
        client_email: process.env.FIREBASE_CLIENT_EMAIL,
    },
    businessCommunications: {
        agentDefaultUpdateMask: 'businessMessagesAgent.conversationalSettings',
    },
    logger: {
        level: process.env.LOG_LEVEL ?? 'info',
        transports: {
            file: {
                filename: process.env.LOG_FILE,
                fileLevel: process.env.LOG_FILE_LEVEL ?? process.env.LOG_LEVEL ?? 'info',
            },
        },
    },
    baseAppUrl: process.env.BASE_URL ?? 'http://localhost:4200',
    baseApiUrl: process.env.BASE_API_URL ?? 'http://localhost:3000/api/v1',
    baseApiMaloupeUrl: process.env.BASE_API_MALOUPE_URL ?? 'http://localhost:3000/api/maloupe',
    gitCommitSha: process.env.GIT_COMMIT_SHA,
    branchName: process.env.BRANCH_NAME,
    cacheImplementation: process.env.CACHE_IMPLEMENTATION ?? 'redis',
    redis: {
        host: process.env.ELASTICACHE_URI ?? 'localhost',
        port: Number(process.env.ELASTICACHE_PORT ?? 6379),
    },
    malouInternalApiToken: process.env.MALOU_INTERNAL_API_TOKEN,
};
