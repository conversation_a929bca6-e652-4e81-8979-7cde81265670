import { ContactMode, ProviderClientCivility, ProviderClientPhone } from '@malou-io/package-utils';

import { TheForkCivility, TheForkOptins } from ':modules/clients/provider-clients/providers/thefork/thefork.interfaces';

export function mapTheForkCivilityToProviderClientCivility(civility: TheForkCivility | null): ProviderClientCivility | undefined {
    if (!civility) {
        return undefined;
    }
    switch (civility) {
        case TheForkCivility.MR:
        case TheForkCivility.LORD:
        case TheForkCivility.DUKE:
            return ProviderClientCivility.MALE;
        case TheForkCivility.MS:
        case TheForkCivility.MRS:
        case TheForkCivility.MADAM_PRESIDENT:
        case TheForkCivility.MADAM_AMBASSADOR:
            return ProviderClientCivility.FEMALE;
        case TheForkCivility.MX:
        case TheForkCivility.COMPANY:
        case TheForkCivility.HIGHNESS:
        case TheForkCivility.AMBASSADOR:
        case TheForkCivility.XX:
        case TheForkCivility.MR_AND_MRS:
        case TheForkCivility.MR_AND_MRS_2:
        case TheForkCivility.DOCTOR:
        case TheForkCivility.PROFESSOR:
            return ProviderClientCivility.OTHER;
    }
}

export function mapTheForkPhoneToProviderClientPhone(phone: string | null): ProviderClientPhone | undefined {
    if (!phone) {
        return undefined;
    }
    const phoneString = phone.toString().replace('+', '');
    const prefix = parseInt(phoneString.substring(0, 2), 10);
    const digits = parseInt(phoneString.substring(2), 10);
    return { prefix, digits };
}

export function mapTheForkOptinsToProviderClientContactOptions(optins: TheForkOptins): ContactMode[] {
    const contactOptions: ContactMode[] = [];
    if (optins.restaurantNewsletter) {
        contactOptions.push(ContactMode.EMAIL);
    }
    return contactOptions;
}
