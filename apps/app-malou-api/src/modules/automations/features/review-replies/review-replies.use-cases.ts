import { singleton } from 'tsyringe';

import { DbId, IReviewReplyAutomation } from '@malou-io/package-models';
import { AutomationFeature } from '@malou-io/package-utils';

import { INewReviewReplyAutomation } from './review-replies.mapper.dto';
import ReviewReplyAutomationsRepository from './review-replies.repository';

@singleton()
export default class AutomationsUseCases {
    constructor(private readonly _reviewReplyAutomationsRepository: ReviewReplyAutomationsRepository) {}

    async getRestaurantReviewReplyAutomations(restaurantId: DbId): Promise<IReviewReplyAutomation[]> {
        return this._reviewReplyAutomationsRepository.find({
            filter: { restaurantId },
            options: { lean: true, populate: [{ path: 'templates' }] },
        });
    }

    async upsertReviewReplyAutomations(automations: INewReviewReplyAutomation[]) {
        return Promise.all(
            automations.map((automation) => {
                const filter: Partial<IReviewReplyAutomation> = {
                    restaurantId: automation.restaurantId,
                    feature: AutomationFeature.REPLY_TO_REVIEW,
                    ratingCategory: automation.ratingCategory,
                    platformKey: automation.platformKey,
                    withComment: automation.withComment,
                };

                return this._reviewReplyAutomationsRepository.upsert({
                    filter,
                    update: automation,
                    options: {
                        lean: true,
                        populate: [{ path: 'templates' }],
                    },
                });
            })
        );
    }
}
