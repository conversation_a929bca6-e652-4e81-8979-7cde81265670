import { singleton } from 'tsyringe';

import { DbId, EntityRepository, ID, IReview, IReviewReplyAutomation, ReviewReplyAutomationModel, toDbId } from '@malou-io/package-models';
import {
    AutomationFeature,
    getPlatformKeysAutomatableWithValidation,
    ReviewReplyAutomationComment,
    ReviewReplyAutomationMethod,
} from '@malou-io/package-utils';

@singleton()
export default class ReviewReplyAutomationsRepository extends EntityRepository<IReviewReplyAutomation> {
    constructor() {
        super(ReviewReplyAutomationModel);
    }

    /**
     * Returns information about how we should try to reply to the review automatically,
     * or null if we shouldn’t try to generate a reply.
     */
    async getReviewReplyAutomation(review: IReview): Promise<IReviewReplyAutomation | null> {
        const withComment = review.text?.length ? ReviewReplyAutomationComment.WITH_COMMENT : ReviewReplyAutomationComment.WITHOUT_COMMENT;
        const automation = await this.findOne({
            filter: {
                restaurantId: review.restaurantId,
                feature: AutomationFeature.REPLY_TO_REVIEW,
                platformKey: review.key,
                ratingCategory: review.rating,
                withComment,
            },
            options: { lean: true },
        });
        if (automation) {
            return automation.active ? automation : null;
        }

        // If we have no automation here it could mean two things:
        //
        //  - automations were never activated on the restaurant, so the restaurant has no
        //  matching document in the automations collection.
        //
        //  - the platform was recently added, and there’s no matching document in the
        //  collection for this platform.

        const automations = await this.find({
            filter: {
                restaurantId: review.restaurantId,
                replyMethod: ReviewReplyAutomationMethod.AI,
                feature: AutomationFeature.REPLY_TO_REVIEW,
                ratingCategory: review.rating,
                withComment,
            },
            options: { lean: true },
        });

        if (automations.length === 0) {
            // automations were never enabled on this restaurant, or the restaurant is still
            // using template automations
            return null;
        }

        // Check if platform allows automation for this type of review
        const applicablePlatformKeys = getPlatformKeysAutomatableWithValidation(withComment);
        if (!applicablePlatformKeys.includes(review.key)) {
            return null;
        }

        // The platform was recently added so there’s no matching automation in the
        // `automations` collection for this platform, but there are documents for other
        // platforms:

        if (automations.every((a) => a.active && a.shouldValidateAiBeforeSend === automations[0].shouldValidateAiBeforeSend)) {
            // Other platforms have the same configuration so we can guess the configuration
            // for the new platform.
            return {
                ...automations[0],
                platformKey: review.key,
            };
        }
        // Some other platforms are configured differently.
        return null;
    }

    getByTemplateId(templateId: DbId): Promise<IReviewReplyAutomation[]> {
        return this.find({
            filter: {
                templateIds: templateId,
            },
            options: { lean: true },
        });
    }

    async removeTemplate(automationId: DbId, templateId: DbId): Promise<void> {
        await this.findOneAndUpdate({
            filter: {
                _id: automationId,
            },
            update: {
                $pull: { templateIds: templateId },
            },
            options: { lean: true, new: true },
        });
    }

    async removeTemplateFromAllAutomations(templateId: ID): Promise<void> {
        await this.updateMany({
            filter: {
                templateIds: toDbId(templateId),
            },
            update: {
                $pull: { templateIds: toDbId(templateId) },
            },
        });
    }

    async reviewHasMatchingActiveAutomation(review: IReview): Promise<boolean> {
        const withComment = review.text?.length ? ReviewReplyAutomationComment.WITH_COMMENT : ReviewReplyAutomationComment.WITHOUT_COMMENT;
        const automation = await this.findOne({
            filter: {
                restaurantId: review.restaurantId,
                feature: AutomationFeature.REPLY_TO_REVIEW,
                platformKey: review.key,
                ratingCategory: review.rating,
                withComment,
                active: true,
                shouldValidateAiBeforeSend: false,
            },
            options: { lean: true },
        });
        return !!automation;
    }
}
