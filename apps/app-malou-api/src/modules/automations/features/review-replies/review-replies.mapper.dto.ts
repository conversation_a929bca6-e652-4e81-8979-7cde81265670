import * as lodash from 'lodash';
import assert from 'node:assert/strict';
import { singleton } from 'tsyringe';

import { ReviewReplyAutomationDto } from '@malou-io/package-dto';
import { IReviewReplyAutomation, toDbId } from '@malou-io/package-models';
import {
    AutomationFeature,
    RatingCategory as DbRatingCategory,
    ReviewReplyAutomationMethod as DtoMethod,
    ReviewReplyAutomationRatingCategory as DtoRatingCategory,
    getPlatformKeysAutomatableWithoutValidation,
    getPlatformKeysAutomatableWithValidation,
    MalouErrorCode,
    PlatformKey,
    ReviewReplyAutomationComment,
    ReviewReplyAutomationMethod,
} from '@malou-io/package-utils';

import { MalouError } from ':helpers/classes/malou-error';
import { logger } from ':helpers/logger';

export type INewReviewReplyAutomation = Omit<IReviewReplyAutomation, '_id' | 'createdAt' | 'updatedAt'>;

@singleton()
export class ReviewReplyAutomationsDtoMapper {
    /**
     * The parameter dbAutomations must contain all the automations of the restaurant.
     * This function won’t work properly if this list is incomplete.
     */
    toDtoList(
        dbAutomations: IReviewReplyAutomation[],
        restaurantId: string,
        restaurantPlatformKeys: PlatformKey[]
    ): ReviewReplyAutomationDto[] {
        assert(
            dbAutomations.every((automation) => automation.restaurantId.toString() === restaurantId),
            'documents must have same restaurant id'
        );

        const dtoAutomations: ReviewReplyAutomationDto[] = [];

        for (const ratingCategory of Object.values(DtoRatingCategory)) {
            for (const withComment of Object.values(ReviewReplyAutomationComment)) {
                const dbAutomationGroup = dbAutomations.filter(
                    (dba) =>
                        dba.withComment === withComment &&
                        this._fromDbRatingCategoryToDtoRatingCategory(dba.ratingCategory) === ratingCategory
                );

                const dto = this._groupedDbAutomationsToDto(
                    withComment,
                    ratingCategory,
                    restaurantId,
                    dbAutomationGroup,
                    restaurantPlatformKeys
                );
                dtoAutomations.push(dto);
            }
        }
        return dtoAutomations;
    }

    toList(dtos: ReviewReplyAutomationDto[], restaurantId: string, restaurantPlatformKeys: PlatformKey[]): INewReviewReplyAutomation[] {
        if (!dtos?.length) {
            return [];
        }

        if (dtos.some((dto) => dto.restaurantId !== restaurantId)) {
            throw new MalouError(MalouErrorCode.INVALID_DATA, {
                message: 'All DTOs must have same restaurant id',
                metadata: { restaurantId, dtos },
            });
        }

        let automations: INewReviewReplyAutomation[] = this._initNewAutomations(restaurantId);

        dtos.forEach((dto) => {
            const ratingCategories = this._fromDtoRatingCategoryToRatings(dto.ratingCategory);

            ratingCategories.forEach((ratingCategory) => {
                automations = this._setActiveAndReplyMethod(dto, automations, ratingCategory);
                automations = this._addDtoAiConfig(dto, automations, ratingCategory, restaurantPlatformKeys);
                automations = this._addDtoTemplateConfigs(dto, automations, ratingCategory);
            });
        });

        return automations;
    }

    /**
     * dbAutomationGroup must be the list of automations grouped by withComment and
     * ratingCategory.
     */
    private _groupedDbAutomationsToDto(
        withComment: ReviewReplyAutomationComment,
        ratingCategory: DtoRatingCategory,
        restaurantId: string,
        dbAutomationGroup: IReviewReplyAutomation[],
        restaurantPlatformKeys: PlatformKey[]
    ): ReviewReplyAutomationDto {
        const defaultDto: ReviewReplyAutomationDto = {
            restaurantId,
            active: false,
            feature: AutomationFeature.REPLY_TO_REVIEW,
            ratingCategory,
            withComment,
            replyMethod: DtoMethod.AI,
            aiConfig: {
                sendAutomaticallyToThesePlatformKeys: [],
            },
            templateConfigs: [],
        };

        if (dbAutomationGroup.length === 0) {
            return defaultDto;
        }

        const first = dbAutomationGroup[0];
        if (!dbAutomationGroup.every((a) => a.replyMethod === first.replyMethod)) {
            logger.info('[ReviewReplyAutomationsDtoMapper] replyMethod is inconsistent', first);
            return defaultDto;
        }

        const groupDisabled = dbAutomationGroup.every((a) => !a.active);

        // We return aiConfig and templateConfigs regardless of replyMethod because we don’t
        // want to loose method-specific settings when the method is changed

        const templateConfigs = lodash
            .chain(dbAutomationGroup)
            .flatMap((a) => a.templateIds.map((id) => ({ id, platformKey: a.platformKey })))
            .groupBy((template) => template.id)
            .map((templates, templateId) => ({
                template: templateId,
                platformKeys: lodash
                    .chain(templates)
                    .map((t) => t.platformKey)
                    .filter((p) => restaurantPlatformKeys.includes(p))
                    .uniq()
                    .value(),
            }))
            .value();

        const sendAutomaticallyForEveryPlatform = dbAutomationGroup.every(
            (a) => (groupDisabled || a.active) && a.replyMethod === DtoMethod.AI && !a.shouldValidateAiBeforeSend
        );

        const aiConfig = {
            // If new platforms have been recently added but there is no document in the
            // automations collection for them yet, we “guess” automation settings
            // for these new platforms: If every other platform is selected in the
            // “send automatically” selector, we consider new platforms to be selected
            // as well.
            sendAutomaticallyToThesePlatformKeys: sendAutomaticallyForEveryPlatform
                ? restaurantPlatformKeys
                : lodash
                      .chain(dbAutomationGroup)
                      .filter((a) => a.active && !a.shouldValidateAiBeforeSend && restaurantPlatformKeys.includes(a.platformKey))
                      .map((a) => a.platformKey)
                      .uniq()
                      .value(),
        };

        return {
            ...defaultDto,
            active: !groupDisabled,
            replyMethod: first.replyMethod,
            aiConfig,
            templateConfigs,
        };
    }

    private _initNewAutomations(restaurantId: string): INewReviewReplyAutomation[] {
        const automations: INewReviewReplyAutomation[] = [];

        const ratingCategories = Object.values(DtoRatingCategory);
        const withCommentOptions = [ReviewReplyAutomationComment.WITH_COMMENT, ReviewReplyAutomationComment.WITHOUT_COMMENT];

        ratingCategories.forEach((ratingCategoryDto) => {
            const reviewReplyAutomationRatingCategories = this._fromDtoRatingCategoryToRatings(ratingCategoryDto);

            reviewReplyAutomationRatingCategories.forEach((ratingCategory) => {
                withCommentOptions.forEach((withComment) => {
                    const applicablePlatformKeys = getPlatformKeysAutomatableWithValidation(withComment);
                    applicablePlatformKeys.forEach((platformKey) => {
                        automations.push({
                            restaurantId: toDbId(restaurantId),
                            active: false,
                            feature: AutomationFeature.REPLY_TO_REVIEW,
                            ratingCategory,
                            platformKey,
                            withComment,
                            replyMethod: ReviewReplyAutomationMethod.TEMPLATES,
                            shouldValidateAiBeforeSend: true,
                            templateIds: [],
                        });
                    });
                });
            });
        });

        return automations;
    }

    private _setActiveAndReplyMethod(
        dto: ReviewReplyAutomationDto,
        automations: INewReviewReplyAutomation[],
        ratingCategory: DbRatingCategory
    ): INewReviewReplyAutomation[] {
        const updatedAutomations = lodash.cloneDeep(automations);

        const platformKeysWithValidation = getPlatformKeysAutomatableWithValidation(dto.withComment);
        const platformKeysWithoutValidation = getPlatformKeysAutomatableWithoutValidation(dto.withComment);

        updatedAutomations.forEach((automation) => {
            const isPlatformSupported =
                platformKeysWithValidation.includes(automation.platformKey) ||
                platformKeysWithoutValidation.includes(automation.platformKey);
            if (automation.ratingCategory === ratingCategory && automation.withComment === dto.withComment && isPlatformSupported) {
                // Automation should be active if the DTO is active, and
                // ((reply method is AI, and aiConfig's shouldValidateAiBeforeSend is true or the platform key is in aiConfig)
                // or (reply method is TEMPLATES and the platform key is in the template configs))
                automation.active =
                    dto.active &&
                    (dto.replyMethod === DtoMethod.AI ||
                        (dto.replyMethod === DtoMethod.TEMPLATES &&
                            dto.templateConfigs.some((t) => t.platformKeys.includes(automation.platformKey))));

                automation.replyMethod = dto.replyMethod;
            }
        });

        return updatedAutomations;
    }

    private _addDtoAiConfig(
        dto: ReviewReplyAutomationDto,
        automations: INewReviewReplyAutomation[],
        ratingCategory: DbRatingCategory,
        restaurantPlatformKeys: PlatformKey[]
    ): INewReviewReplyAutomation[] {
        const updatedAutomations = lodash.cloneDeep(automations);
        const platformKeysWithValidation = getPlatformKeysAutomatableWithValidation(dto.withComment);
        const platformKeysWithoutValidation = getPlatformKeysAutomatableWithoutValidation(dto.withComment);

        restaurantPlatformKeys
            .filter(
                (platformKey) => platformKeysWithValidation.includes(platformKey) || platformKeysWithoutValidation.includes(platformKey)
            )
            .forEach((platformKey) => {
                const automationToUpdate = updatedAutomations.find(
                    (automation) =>
                        automation.ratingCategory === ratingCategory &&
                        automation.withComment === dto.withComment &&
                        automation.platformKey === platformKey
                );

                if (automationToUpdate) {
                    const shouldValidateAiBeforeSend = !dto.aiConfig.sendAutomaticallyToThesePlatformKeys.includes(platformKey);
                    const isPlatformWithoutValidation = platformKeysWithoutValidation.includes(platformKey);
                    if (!shouldValidateAiBeforeSend && isPlatformWithoutValidation) {
                        automationToUpdate.shouldValidateAiBeforeSend = false;
                    } else {
                        automationToUpdate.shouldValidateAiBeforeSend = true;
                    }
                }
            });

        return updatedAutomations;
    }

    private _addDtoTemplateConfigs(
        dto: ReviewReplyAutomationDto,
        automations: INewReviewReplyAutomation[],
        ratingCategory: DbRatingCategory
    ): INewReviewReplyAutomation[] {
        const updatedAutomations = lodash.cloneDeep(automations);

        dto.templateConfigs.forEach((templateConfig) => {
            templateConfig.platformKeys.forEach((platformKey) => {
                const automationToUpdate = updatedAutomations.find(
                    (automation) =>
                        automation.ratingCategory === ratingCategory &&
                        automation.withComment === dto.withComment &&
                        automation.platformKey === platformKey
                );

                if (automationToUpdate) {
                    if (automationToUpdate.templateIds) {
                        automationToUpdate.templateIds.push(toDbId(templateConfig.template));
                    } else {
                        automationToUpdate.templateIds = [toDbId(templateConfig.template)];
                    }
                }
            });
        });

        return updatedAutomations;
    }

    private _fromDbRatingCategoryToDtoRatingCategory(ratingCategory: number): DtoRatingCategory {
        switch (ratingCategory) {
            case 1:
            case 2:
                return DtoRatingCategory.REVIEW_1_2;
            case 3:
                return DtoRatingCategory.REVIEW_3;
            case 4:
                return DtoRatingCategory.REVIEW_4;
            case 5:
                return DtoRatingCategory.REVIEW_5;
            default:
                assert.fail();
        }
    }

    private _fromDtoRatingCategoryToRatings(ratingCategory: DtoRatingCategory): DbRatingCategory[] {
        switch (ratingCategory) {
            case DtoRatingCategory.REVIEW_1_2:
                return [1, 2];
            case DtoRatingCategory.REVIEW_3:
                return [3];
            case DtoRatingCategory.REVIEW_4:
                return [4];
            case DtoRatingCategory.REVIEW_5:
                return [5];
            default:
                return [];
        }
    }
}
