import { NextFunction, Request, Response } from 'express';
import { singleton } from 'tsyringe';

import {
    GetRestaurantReviewReplyAutomationsParamsDto,
    getRestaurantReviewReplyAutomationsParamsValidator,
    ReviewReplyAutomationDto,
    updateRestaurantReviewReplyAutomationsBodyValidator,
    UpdateRestaurantReviewReplyAutomationsParamsDto,
    updateRestaurantReviewReplyAutomationsParamsValidator,
} from '@malou-io/package-dto';
import { toDbId } from '@malou-io/package-models';
import { ApiResultError, ApiResultV2, MalouErrorCode } from '@malou-io/package-utils';

import { MalouError } from ':helpers/classes/malou-error';
import { Body, Params } from ':helpers/decorators/validators';
import PlatformsRepository from ':modules/platforms/platforms.repository';

import { ReviewReplyAutomationsDtoMapper } from './review-replies.mapper.dto';
import ReviewReplyAutomationsUseCases from './review-replies.use-cases';

@singleton()
export default class ReviewReplyAutomationsController {
    constructor(
        private readonly _reviewReplyAutomationsUseCases: ReviewReplyAutomationsUseCases,
        private readonly _reviewReplyAutomationsDtoMapper: ReviewReplyAutomationsDtoMapper,
        private readonly _platformsRepository: PlatformsRepository
    ) {}

    @Params(getRestaurantReviewReplyAutomationsParamsValidator)
    async handleGetRestaurantReviewReplyAutomations(
        req: Request<GetRestaurantReviewReplyAutomationsParamsDto>,
        res: Response<ApiResultV2<ReviewReplyAutomationDto[]>>,
        next: NextFunction
    ) {
        try {
            const { restaurantId } = req.params;

            const restaurantPlatforms = await this._platformsRepository.find({
                filter: { restaurantId },
                projection: { restaurantId: true, key: true },
                options: { lean: true },
            });

            const restaurantPlatformKeys = restaurantPlatforms.map((platform) => platform.key);

            const reviewReplyAutomations = await this._reviewReplyAutomationsUseCases.getRestaurantReviewReplyAutomations(
                toDbId(restaurantId)
            );
            const reviewReplyAutomationDtos = this._reviewReplyAutomationsDtoMapper.toDtoList(
                reviewReplyAutomations,
                restaurantId,
                restaurantPlatformKeys
            );

            return res.json({ data: reviewReplyAutomationDtos });
        } catch (err) {
            next(err);
        }
    }

    @Params(updateRestaurantReviewReplyAutomationsParamsValidator)
    @Body(updateRestaurantReviewReplyAutomationsBodyValidator)
    async handleUpdateRestaurantReviewReplyAutomations(
        req: Request<UpdateRestaurantReviewReplyAutomationsParamsDto, any, { automations: ReviewReplyAutomationDto[] }>,
        res: Response<ApiResultV2<ReviewReplyAutomationDto[], ApiResultError>>,
        next: NextFunction
    ) {
        try {
            const { restaurantId } = req.params;
            const { automations } = req.body;

            if (automations.some((automation) => automation.restaurantId !== restaurantId)) {
                throw new MalouError(MalouErrorCode.INCONSISTENT_RESTAURANT_ID, {
                    message: "The restaurant ID given in route params is different from at least one of the automations' restaurantId",
                    metadata: { restaurantId },
                });
            }

            const restaurantPlatforms = await this._platformsRepository.find({
                filter: { restaurantId },
                projection: { restaurantId: true, key: true },
                options: { lean: true },
            });

            const restaurantPlatformKeys = restaurantPlatforms.map((platform) => platform.key);

            const reviewReplyAutomationsToUpdate = this._reviewReplyAutomationsDtoMapper.toList(
                automations,
                restaurantId,
                restaurantPlatformKeys
            );
            const reviewReplyAutomationsUpdated =
                await this._reviewReplyAutomationsUseCases.upsertReviewReplyAutomations(reviewReplyAutomationsToUpdate);
            const reviewReplyAutomationDtos = this._reviewReplyAutomationsDtoMapper.toDtoList(
                reviewReplyAutomationsUpdated,
                restaurantId,
                restaurantPlatformKeys
            );

            res.json({ data: reviewReplyAutomationDtos });
        } catch (err) {
            next(err);
        }
    }
}
