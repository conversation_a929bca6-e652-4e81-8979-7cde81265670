import { singleton } from 'tsyringe';

import { MalouErrorCode, platformsKeys } from '@malou-io/package-utils';

import { Config } from ':config';
import { MalouError } from ':helpers/classes/malou-error';
import { logger } from ':helpers/logger';
import { randomString } from ':helpers/utils';
import { fetchUntilWorks } from ':microservices/node-crawler';
import { TripadvisorMapper } from ':modules/platforms/platforms/tripadvisor/tripadvisor-mapper';
import { MalouRestaurantSearchResult } from ':modules/platforms/use-cases/search-social-ids/search-social-ids.interface';
import RestaurantsRepository from ':modules/restaurants/restaurants.repository';

@singleton()
export class SearchTripadvisorSocialIdsUseCase {
    constructor(
        private readonly _restaurantsRepository: RestaurantsRepository,
        private readonly _tripadvisorApiMapper: TripadvisorMapper
    ) {}

    async execute({ restaurantId }: { restaurantId?: string | null }): Promise<{ list: MalouRestaurantSearchResult[] }> {
        if (!restaurantId) {
            return { list: [] };
        }
        const restaurant = await this._restaurantsRepository.getRestaurantById(restaurantId);
        if (!restaurant) {
            return { list: [] };
        }
        const { name } = restaurant;
        const apiQuery =
            platformsKeys.TRIPADVISOR.baseUrl + Config.platforms.tripadvisor.api.params.search + encodeURIComponent(name.toLowerCase());
        const res = await fetchUntilWorks({
            params: {
                url: apiQuery,
                headers: { ...Config.platforms.tripadvisor.api.headers, uid: randomString(10) },
            },
            isResponseValid: (result) => result?.results,
            retries: 0,
            crawlerCount: 1,
        });
        const results = res.results;
        if (!results) {
            throw new MalouError(MalouErrorCode.PLATFORM_NO_DATA_IN_RESPONSE, {
                message: 'TripAdvisor',
                metadata: { restaurantId },
            });
        }
        return { list: this._mapSearchResultsToMalou(results) };
    }

    private _mapSearchResultsToMalou(businesses) {
        try {
            return this._tripadvisorApiMapper.toMalouMapperSearch(businesses);
        } catch (e) {
            logger.warn('[TRIPADVISOR_MAPPER_ERROR] Error trying to map search results', e);
            return [];
        }
    }
}
