import { omit } from 'lodash';
import { DateTime } from 'luxon';
import { container } from 'tsyringe';

import { GiftDrawDto, GiftDrawInsightPerRestaurantDto, GiftDrawPopulatedDto } from '@malou-io/package-dto';
import { DbId, IWheelOfFortune, newDbId, toDbId } from '@malou-io/package-models';
import {
    DEFAULT_WHEEL_OF_FORTUNE_REDIRECTION_SETTINGS,
    GiftClaimStartDateOption,
    MalouComparisonPeriod,
    MalouErrorCode,
} from '@malou-io/package-utils';

import { MalouError } from ':helpers/classes/malou-error';
import { DateProviderPort } from ':helpers/providers/date/date.provider';
import { IndexProviderPort } from ':helpers/providers/index/index.provider';
import { registerRepositories, TestCaseBuilderV2 } from ':helpers/tests/testing-utils';
import { getDefaultClient } from ':modules/clients/tests/clients.builder';
import { GiftDrawsUseCases } from ':modules/gift-draws/gift-draws.use-cases';
import { GiftStocksRepository } from ':modules/gifts/stocks/gift-stocks.repository';
import { getDefaultRestaurant } from ':modules/restaurants/tests/restaurant.builder';
import {
    getDefaultGift,
    getDefaultGiftDraw,
    getDefaultGiftStock,
    getDefaultRestaurantWheelsOfFortune,
} from ':modules/wheels-of-fortune/tests/wheels-of-fortune.builder';

describe('giftDrawsUseCases', () => {
    let indexProvider: IndexProviderPort;
    let dateProvider: DateProviderPort;
    beforeAll(() => {
        registerRepositories([
            'ClientsRepository',
            'GiftDrawsRepository',
            'RestaurantsRepository',
            'RestaurantWheelsOfFortuneRepository',
            'GiftsRepository',
            'GiftStocksRepository',
            'WheelsOfFortuneRepository',
        ]);
        indexProvider = container.resolve(IndexProviderPort);
        dateProvider = container.resolve(DateProviderPort);
    });

    describe('getGiftDrawById', () => {
        it('should return the gift draw with the correct id', async () => {
            const giftDrawsUseCases = container.resolve(GiftDrawsUseCases);

            const testCase = new TestCaseBuilderV2<'giftDraws' | 'gifts' | 'restaurants' | 'restaurantWheelsOfFortune'>({
                seeds: {
                    restaurantWheelsOfFortune: {
                        data() {
                            return [getDefaultRestaurantWheelsOfFortune().build()];
                        },
                    },
                    restaurants: {
                        data() {
                            return [getDefaultRestaurant().build()];
                        },
                    },
                    gifts: {
                        data() {
                            return [getDefaultGift().build()];
                        },
                    },
                    giftDraws: {
                        data(dependencies) {
                            return [
                                getDefaultGiftDraw()
                                    .giftId(dependencies.gifts()[0]._id)
                                    .restaurantId(dependencies.restaurants()[0]._id)
                                    .wheelOfFortuneId(dependencies.restaurantWheelsOfFortune()[0]._id)
                                    .build(),
                            ];
                        },
                    },
                },
                expectedResult(dependencies): GiftDrawPopulatedDto {
                    const giftDraw = dependencies.giftDraws[0];
                    const gift = dependencies.gifts[0];
                    const restaurant = dependencies.restaurants[0];
                    const wheelOfFortune = dependencies.restaurantWheelsOfFortune[0];

                    return {
                        id: giftDraw._id.toString(),
                        giftId: giftDraw.giftId.toString(),
                        gift: {
                            id: gift._id.toString(),
                            name: gift.name,
                            weight: gift.weight,
                            conditions: gift.conditions,
                        },
                        restaurantId: giftDraw.restaurantId.toString(),
                        restaurant: {
                            id: restaurant._id.toString(),
                            name: restaurant.name,
                            logo: null,
                            address: restaurant.address ?? undefined,
                            internalName: restaurant.internalName,
                        },
                        clientId: null,
                        client: undefined,
                        wheelOfFortuneId: giftDraw.wheelOfFortuneId.toString(),
                        wheelOfFortune: {
                            id: wheelOfFortune._id.toString(),
                            startDate: null,
                            endDate: null,
                            parameters: {
                                primaryColor: wheelOfFortune.parameters.primaryColor,
                                secondaryColor: wheelOfFortune.parameters.secondaryColor,
                                giftClaimDurationInDays: wheelOfFortune.parameters.giftClaimDurationInDays,
                                giftClaimStartDateOption: wheelOfFortune.parameters.giftClaimStartDateOption,
                                mediaId: wheelOfFortune.parameters.mediaId ? wheelOfFortune.parameters.mediaId.toString() : null,
                                redirectionSettings: {
                                    ...wheelOfFortune.parameters.redirectionSettings,
                                    platforms: wheelOfFortune.parameters.redirectionSettings.platforms.map((platform) => ({
                                        order: platform.order,
                                        platformKey: platform.platformKey,
                                        privateReviewRatings: platform.privateReviewRatings ?? [],
                                    })),
                                },
                            },
                        },
                        retrievalStartDate: giftDraw.retrievalStartDate.toISOString(),
                        retrievalEndDate: giftDraw.retrievalEndDate.toISOString(),
                        retrievedAt: giftDraw.retrievedAt?.toISOString() || null,
                        lang: giftDraw.lang,
                    };
                },
            });
            await testCase.build();

            const seededObjects = testCase.getSeededObjects();
            const result = await giftDrawsUseCases.getGiftDrawById((seededObjects.giftDraws[0]._id as DbId).toString());

            const expectedResult = testCase.getExpectedResult();
            expect(result).toStrictEqual(expectedResult);
        });

        it('should throw an error if no gift draw is found', async () => {
            const giftDrawsUseCases = container.resolve(GiftDrawsUseCases);

            const testCase = new TestCaseBuilderV2<'giftDraws'>({
                seeds: {
                    giftDraws: {
                        data() {
                            return [];
                        },
                    },
                },
                expectedErrorCode: MalouErrorCode.GIFT_DRAW_NOT_FOUND,
            });
            await testCase.build();

            const expectedErrorCode = testCase.getExpectedErrorCode();
            let errorCode: MalouErrorCode | undefined;

            await giftDrawsUseCases.getGiftDrawById(newDbId().toString()).catch((error: MalouError) => {
                errorCode = error.malouErrorCode;
            });
            expect(errorCode).toEqual(expectedErrorCode);
        });
    });

    describe('setGiftDrawRetrieved', () => {
        it('should return the gift draw with the correct retrievedAt', async () => {
            const giftDrawsUseCases = container.resolve(GiftDrawsUseCases);
            const now = new Date();
            const mockDateProvider = jest.fn().mockReturnValue(now);
            dateProvider.provideTodayDate = mockDateProvider;

            const testCase = new TestCaseBuilderV2<'giftDraws' | 'gifts' | 'restaurants' | 'restaurantWheelsOfFortune'>({
                seeds: {
                    restaurantWheelsOfFortune: {
                        data() {
                            return [getDefaultRestaurantWheelsOfFortune().build()];
                        },
                    },
                    restaurants: {
                        data() {
                            return [getDefaultRestaurant().build()];
                        },
                    },
                    gifts: {
                        data() {
                            return [getDefaultGift().build()];
                        },
                    },
                    giftDraws: {
                        data(dependencies) {
                            return [
                                getDefaultGiftDraw()
                                    .giftId(dependencies.gifts()[0]._id)
                                    .restaurantId(dependencies.restaurants()[0]._id)
                                    .wheelOfFortuneId(dependencies.restaurantWheelsOfFortune()[0]._id)
                                    .build(),
                            ];
                        },
                    },
                },
                expectedResult(dependencies): GiftDrawPopulatedDto {
                    const giftDraw = dependencies.giftDraws[0];
                    const gift = dependencies.gifts[0];
                    const restaurant = dependencies.restaurants[0];
                    const wheelOfFortune = dependencies.restaurantWheelsOfFortune[0];

                    return {
                        id: giftDraw._id.toString(),
                        giftId: giftDraw.giftId.toString(),
                        gift: {
                            id: gift._id.toString(),
                            name: gift.name,
                            weight: gift.weight,
                            conditions: undefined,
                        },
                        restaurantId: giftDraw.restaurantId.toString(),
                        restaurant: {
                            id: restaurant._id.toString(),
                            name: restaurant.name,
                            logo: null,
                            address: restaurant.address ?? undefined,
                            internalName: restaurant.internalName,
                        },
                        clientId: null,
                        client: undefined,
                        wheelOfFortuneId: giftDraw.wheelOfFortuneId.toString(),
                        wheelOfFortune: {
                            id: wheelOfFortune._id.toString(),
                            startDate: null,
                            endDate: null,
                            parameters: {
                                primaryColor: wheelOfFortune.parameters.primaryColor,
                                secondaryColor: wheelOfFortune.parameters.secondaryColor,
                                giftClaimDurationInDays: wheelOfFortune.parameters.giftClaimDurationInDays,
                                giftClaimStartDateOption: wheelOfFortune.parameters.giftClaimStartDateOption,
                                mediaId: wheelOfFortune.parameters.mediaId ? wheelOfFortune.parameters.mediaId.toString() : null,
                                redirectionSettings: {
                                    ...wheelOfFortune.parameters.redirectionSettings,
                                    platforms: wheelOfFortune.parameters.redirectionSettings.platforms.map((platform) => ({
                                        order: platform.order,
                                        platformKey: platform.platformKey,
                                        privateReviewRatings: platform.privateReviewRatings ?? [],
                                    })),
                                },
                            },
                        },
                        retrievalStartDate: giftDraw.retrievalStartDate.toISOString(),
                        retrievalEndDate: giftDraw.retrievalEndDate.toISOString(),
                        retrievedAt: now.toISOString(),
                        lang: giftDraw.lang,
                    };
                },
            });
            await testCase.build();

            const seededObjects = testCase.getSeededObjects();
            const result = await giftDrawsUseCases.setGiftDrawRetrieved((seededObjects.giftDraws[0]._id as DbId).toString());

            const expectedResult = testCase.getExpectedResult();
            expect(result).toStrictEqual(expectedResult);
        });

        it('should throw an error if no gift draw is found', async () => {
            const giftDrawsUseCases = container.resolve(GiftDrawsUseCases);

            const testCase = new TestCaseBuilderV2<'giftDraws'>({
                seeds: {
                    giftDraws: {
                        data() {
                            return [];
                        },
                    },
                },
                expectedErrorCode: MalouErrorCode.GIFT_DRAW_NOT_FOUND,
            });
            await testCase.build();

            const expectedErrorCode = testCase.getExpectedErrorCode();
            let errorCode: MalouErrorCode | undefined;

            await giftDrawsUseCases.setGiftDrawRetrieved(newDbId().toString()).catch((error: MalouError) => {
                errorCode = error.malouErrorCode;
            });
            expect(errorCode).toEqual(expectedErrorCode);
        });
    });

    describe('createGiftDraw', () => {
        it('should throw an error if client not found', async () => {
            const giftDrawsUseCases = container.resolve(GiftDrawsUseCases);
            const startDate = DateTime.now().minus({ days: 1 }).toJSDate();
            const endDate = DateTime.now().plus({ days: 1 }).toJSDate();
            const lang = 'fr';
            const testCase = new TestCaseBuilderV2<'restaurants' | 'restaurantWheelsOfFortune' | 'gifts'>({
                seeds: {
                    restaurants: {
                        data() {
                            return [getDefaultRestaurant().build()];
                        },
                    },
                    restaurantWheelsOfFortune: {
                        data(dependencies) {
                            return [
                                getDefaultRestaurantWheelsOfFortune()
                                    .restaurantId(toDbId(dependencies.restaurants()[0]._id))
                                    .startDate(startDate)
                                    .endDate(endDate)
                                    .build(),
                            ];
                        },
                    },
                    gifts: {
                        data() {
                            return [getDefaultGift().name('Gift 1').weight(1).build()];
                        },
                    },
                },
                expectedErrorCode: MalouErrorCode.CLIENT_NOT_FOUND,
            });
            await testCase.build();
            const seededObjects = testCase.getSeededObjects();
            const restaurantId = (seededObjects.restaurants[0]._id as DbId).toString();
            const wheelOfFortuneId = (seededObjects.restaurantWheelsOfFortune[0]._id as DbId).toString();
            const clientId = newDbId();

            const expectedErrorCode = testCase.getExpectedErrorCode();
            let errorCode: MalouErrorCode | undefined;
            await giftDrawsUseCases.createGiftDraw(restaurantId, wheelOfFortuneId, lang, clientId.toString()).catch((error: MalouError) => {
                errorCode = error.malouErrorCode;
            });
            expect(errorCode).toBe(expectedErrorCode);
        });

        it('should throw an error if no gift can be won', async () => {
            const giftDrawsUseCases = container.resolve(GiftDrawsUseCases);
            const startDate = DateTime.now().minus({ days: 1 }).toJSDate();
            const endDate = DateTime.now().plus({ days: 1 }).toJSDate();
            const lang = 'fr';
            const testCase = new TestCaseBuilderV2<'restaurants' | 'restaurantWheelsOfFortune' | 'gifts' | 'clients'>({
                seeds: {
                    restaurants: {
                        data() {
                            return [getDefaultRestaurant().build()];
                        },
                    },
                    restaurantWheelsOfFortune: {
                        data(dependencies) {
                            return [
                                getDefaultRestaurantWheelsOfFortune()
                                    .restaurantId(toDbId(dependencies.restaurants()[0]._id))
                                    .startDate(startDate)
                                    .endDate(endDate)
                                    .build(),
                            ];
                        },
                    },
                    gifts: {
                        data() {
                            return [getDefaultGift().name('Gift 1').weight(1).build()];
                        },
                    },
                    clients: {
                        data() {
                            return [getDefaultClient().build()];
                        },
                    },
                },
                expectedErrorCode: MalouErrorCode.NO_AVAILABLE_GIFTS,
            });
            await testCase.build();
            const seededObjects = testCase.getSeededObjects();
            const restaurantId = (seededObjects.restaurants[0]._id as DbId).toString();
            const wheelOfFortuneId = (seededObjects.restaurantWheelsOfFortune[0]._id as DbId).toString();
            const clientId = (seededObjects.clients[0]._id as DbId).toString();

            const expectedErrorCode = testCase.getExpectedErrorCode();
            let errorCode: MalouErrorCode | undefined;
            await giftDrawsUseCases.createGiftDraw(restaurantId, wheelOfFortuneId, lang, clientId).catch((error: MalouError) => {
                errorCode = error.malouErrorCode;
            });
            expect(errorCode).toBe(expectedErrorCode);
        });

        it('should throw an error if a draw is made when the wheel is not active', async () => {
            const giftDrawsUseCases = container.resolve(GiftDrawsUseCases);
            const startDate = DateTime.now().plus({ days: 1 }).toJSDate();
            const endDate = DateTime.now().plus({ days: 3 }).toJSDate();
            const lang = 'fr';
            const testCase = new TestCaseBuilderV2<'restaurants' | 'restaurantWheelsOfFortune' | 'gifts' | 'clients'>({
                seeds: {
                    restaurants: {
                        data() {
                            return [getDefaultRestaurant().build()];
                        },
                    },
                    restaurantWheelsOfFortune: {
                        data(dependencies) {
                            return [
                                getDefaultRestaurantWheelsOfFortune()
                                    .restaurantId(toDbId(dependencies.restaurants()[0]._id))
                                    .startDate(startDate)
                                    .endDate(endDate)
                                    .build(),
                            ];
                        },
                    },
                    gifts: {
                        data() {
                            return [getDefaultGift().name('Gift 1').weight(1).build()];
                        },
                    },
                    clients: {
                        data() {
                            return [getDefaultClient().build()];
                        },
                    },
                },
                expectedErrorCode: MalouErrorCode.WHEEL_OF_FORTUNE_NOT_STARTED,
            });
            await testCase.build();
            const seededObjects = testCase.getSeededObjects();
            const restaurantId = (seededObjects.restaurants[0]._id as DbId).toString();
            const wheelOfFortuneId = (seededObjects.restaurantWheelsOfFortune[0]._id as DbId).toString();
            const clientId = (seededObjects.clients[0]._id as DbId).toString();

            const expectedErrorCode = testCase.getExpectedErrorCode();
            let errorCode: MalouErrorCode | undefined;
            await giftDrawsUseCases.createGiftDraw(restaurantId, wheelOfFortuneId, lang, clientId).catch((error: MalouError) => {
                errorCode = error.malouErrorCode;
            });
            expect(errorCode).toBe(expectedErrorCode);
        });

        it('should create the a giftDraw', async () => {
            const giftDrawsUseCases = container.resolve(GiftDrawsUseCases);
            const startDate = DateTime.now().minus({ days: 1 }).toJSDate();
            const endDate = DateTime.now().plus({ days: 1 }).toJSDate();
            const mockIndexProvider = jest.fn().mockReturnValue(0);
            indexProvider.provideFloat = mockIndexProvider;
            const lang = 'fr';
            const testCase = new TestCaseBuilderV2<'restaurants' | 'gifts' | 'giftStocks' | 'restaurantWheelsOfFortune' | 'clients'>({
                seeds: {
                    restaurants: {
                        data() {
                            return [getDefaultRestaurant().build()];
                        },
                    },
                    gifts: {
                        data() {
                            return [getDefaultGift().name('Gift 1').weight(1).build(), getDefaultGift().name('Gift 2').weight(2).build()];
                        },
                    },
                    giftStocks: {
                        data(dependencies) {
                            return [
                                getDefaultGiftStock()
                                    .giftId(toDbId(dependencies.gifts()[0]._id))
                                    .restaurantId(toDbId(dependencies.restaurants()[0]._id))
                                    .quantity(1)
                                    .build(),
                                getDefaultGiftStock()
                                    .giftId(toDbId(dependencies.gifts()[1]._id))
                                    .restaurantId(toDbId(dependencies.restaurants()[0]._id))
                                    .quantity(2)
                                    .build(),
                            ];
                        },
                    },
                    restaurantWheelsOfFortune: {
                        data(dependencies) {
                            return [
                                getDefaultRestaurantWheelsOfFortune()
                                    .restaurantId(toDbId(dependencies.restaurants()[0]._id))
                                    .giftIds([toDbId(dependencies.gifts()[0]._id), toDbId(dependencies.gifts()[1]._id)])
                                    .startDate(startDate)
                                    .endDate(endDate)
                                    .build(),
                            ];
                        },
                    },
                    clients: {
                        data() {
                            return [getDefaultClient().build()];
                        },
                    },
                },
                expectedResult(dependencies) {
                    return [
                        {
                            giftId: dependencies.gifts[0]._id.toString(),
                            restaurantId: dependencies.restaurants[0]._id.toString(),
                            wheelOfFortuneId: dependencies.restaurantWheelsOfFortune[0]._id.toString(),
                            clientId: dependencies.clients[0]._id.toString(),
                            retrievedAt: null,
                        },
                    ];
                },
            });
            await testCase.build();
            const seededObjects = testCase.getSeededObjects();
            const restaurantId = (seededObjects.restaurants[0]._id as DbId).toString();
            const wheelOfFortuneId = (seededObjects.restaurantWheelsOfFortune[0]._id as DbId).toString();
            const clientId = (seededObjects.clients[0]._id as DbId).toString();

            const giftDraw = await giftDrawsUseCases.createGiftDraw(restaurantId, wheelOfFortuneId, lang, clientId);

            const expectedResult = testCase.getExpectedResult();
            expect(omit(giftDraw, 'id')).toMatchObject(expectedResult[0]);
        });

        it('should decrease stocks if a draw is made', async () => {
            const giftStocksRepository = container.resolve(GiftStocksRepository);
            const giftDrawsUseCases = container.resolve(GiftDrawsUseCases);
            const startDate = DateTime.now().minus({ days: 1 }).toJSDate();
            const endDate = DateTime.now().plus({ days: 1 }).toJSDate();
            const lang = 'fr';

            const mockIndexProvider = jest.fn().mockReturnValue(0);
            indexProvider.provideFloat = mockIndexProvider;
            const testCase = new TestCaseBuilderV2<'restaurants' | 'gifts' | 'giftStocks' | 'restaurantWheelsOfFortune' | 'clients'>({
                seeds: {
                    restaurants: {
                        data() {
                            return [getDefaultRestaurant().build()];
                        },
                    },
                    gifts: {
                        data() {
                            return [getDefaultGift().name('Gift 1').weight(1).build(), getDefaultGift().name('Gift 2').weight(2).build()];
                        },
                    },
                    giftStocks: {
                        data(dependencies) {
                            return [
                                getDefaultGiftStock()
                                    .giftId(toDbId(dependencies.gifts()[0]._id))
                                    .restaurantId(toDbId(dependencies.restaurants()[0]._id))
                                    .quantity(1)
                                    .build(),
                                getDefaultGiftStock()
                                    .giftId(toDbId(dependencies.gifts()[1]._id))
                                    .restaurantId(toDbId(dependencies.restaurants()[0]._id))
                                    .quantity(2)
                                    .build(),
                            ];
                        },
                    },
                    restaurantWheelsOfFortune: {
                        data(dependencies) {
                            return [
                                getDefaultRestaurantWheelsOfFortune()
                                    .restaurantId(toDbId(dependencies.restaurants()[0]._id))
                                    .giftIds([toDbId(dependencies.gifts()[0]._id), toDbId(dependencies.gifts()[1]._id)])
                                    .startDate(startDate)
                                    .endDate(endDate)
                                    .build(),
                            ];
                        },
                    },
                    clients: {
                        data() {
                            return [getDefaultClient().build()];
                        },
                    },
                },
                expectedResult(dependencies) {
                    return dependencies.giftStocks[0].quantity! - 1;
                },
            });
            await testCase.build();
            const seededObjects = testCase.getSeededObjects();
            const restaurantId = (seededObjects.restaurants[0]._id as DbId).toString();
            const wheelOfFortuneId = (seededObjects.restaurantWheelsOfFortune[0]._id as DbId).toString();
            const clientId = (seededObjects.clients[0]._id as DbId).toString();

            await giftDrawsUseCases.createGiftDraw(restaurantId, wheelOfFortuneId, lang, clientId);

            const result = await giftStocksRepository.findOne({
                filter: { giftId: seededObjects.gifts[0]._id },
            });

            const expectedResult = testCase.getExpectedResult();
            expect(result?.quantity).toBe(expectedResult);
        });

        it('should not modify unlimited stock even if a draw is made', async () => {
            const giftStocksRepository = container.resolve(GiftStocksRepository);
            const giftDrawsUseCases = container.resolve(GiftDrawsUseCases);
            const startDate = DateTime.now().minus({ days: 1 }).toJSDate();
            const endDate = DateTime.now().plus({ days: 1 }).toJSDate();
            const lang = 'fr';
            const mockIndexProvider = jest.fn().mockReturnValue(0);
            indexProvider.provideFloat = mockIndexProvider;
            const testCase = new TestCaseBuilderV2<'restaurants' | 'gifts' | 'giftStocks' | 'restaurantWheelsOfFortune' | 'clients'>({
                seeds: {
                    restaurants: {
                        data() {
                            return [getDefaultRestaurant().build()];
                        },
                    },
                    gifts: {
                        data() {
                            return [getDefaultGift().name('Gift 1').weight(1).build(), getDefaultGift().name('Gift 2').weight(2).build()];
                        },
                    },
                    giftStocks: {
                        data(dependencies) {
                            return [
                                getDefaultGiftStock()
                                    .giftId(toDbId(dependencies.gifts()[0]._id))
                                    .restaurantId(toDbId(dependencies.restaurants()[0]._id))
                                    .quantity(null)
                                    .build(),
                                getDefaultGiftStock()
                                    .giftId(toDbId(dependencies.gifts()[1]._id))
                                    .restaurantId(toDbId(dependencies.restaurants()[0]._id))
                                    .quantity(2)
                                    .build(),
                            ];
                        },
                    },
                    restaurantWheelsOfFortune: {
                        data(dependencies) {
                            return [
                                getDefaultRestaurantWheelsOfFortune()
                                    .restaurantId(toDbId(dependencies.restaurants()[0]._id))
                                    .giftIds([toDbId(dependencies.gifts()[0]._id), toDbId(dependencies.gifts()[1]._id)])
                                    .startDate(startDate)
                                    .endDate(endDate)
                                    .build(),
                            ];
                        },
                    },
                    clients: {
                        data() {
                            return [getDefaultClient().build()];
                        },
                    },
                },
                expectedResult() {
                    return null;
                },
            });
            await testCase.build();
            const seededObjects = testCase.getSeededObjects();
            const restaurantId = (seededObjects.restaurants[0]._id as DbId).toString();
            const wheelOfFortuneId = (seededObjects.restaurantWheelsOfFortune[0]._id as DbId).toString();
            const clientId = (seededObjects.clients[0]._id as DbId).toString();

            await giftDrawsUseCases.createGiftDraw(restaurantId, wheelOfFortuneId, lang, clientId);

            const result = await giftStocksRepository.findOne({
                filter: { giftId: seededObjects.gifts[0]._id },
            });

            const expectedResult = testCase.getExpectedResult();
            expect(result?.quantity).toBe(expectedResult);
        });
    });

    describe('getSoonExpiredDraws', () => {
        it('should return the draws that expires in 5 days when giftClaimDurationInDays > 5', async () => {
            const giftDrawsUseCases = container.resolve(GiftDrawsUseCases);
            const startDate = DateTime.now().minus({ days: 1 }).toJSDate();
            const endDate = DateTime.now().plus({ days: 1 }).toJSDate();
            const parameters: IWheelOfFortune['parameters'] = {
                primaryColor: '#111111',
                secondaryColor: '#222222',
                mediaId: null,
                giftClaimDurationInDays: 30,
                giftClaimStartDateOption: GiftClaimStartDateOption.NOW,
                redirectionSettings: DEFAULT_WHEEL_OF_FORTUNE_REDIRECTION_SETTINGS,
            };
            const firstRetrievalEndDate = DateTime.now()
                .plus({ days: 5 })
                .set({ hour: 0, minute: 0, second: 0, millisecond: 0 })
                .toJSDate();
            const secondRetrievalEndDate = DateTime.now()
                .plus({ days: 1 })
                .set({ hour: 0, minute: 0, second: 0, millisecond: 0 })
                .toJSDate();

            const testCase = new TestCaseBuilderV2<
                'restaurants' | 'gifts' | 'giftStocks' | 'restaurantWheelsOfFortune' | 'giftDraws' | 'clients'
            >({
                seeds: {
                    restaurants: {
                        data() {
                            return [getDefaultRestaurant().build()];
                        },
                    },
                    gifts: {
                        data() {
                            return [getDefaultGift().name('Gift 1').weight(1).build(), getDefaultGift().name('Gift 2').weight(2).build()];
                        },
                    },
                    giftStocks: {
                        data(dependencies) {
                            return [
                                getDefaultGiftStock()
                                    .giftId(toDbId(dependencies.gifts()[0]._id))
                                    .restaurantId(toDbId(dependencies.restaurants()[0]._id))
                                    .quantity(1)
                                    .build(),
                                getDefaultGiftStock()
                                    .giftId(toDbId(dependencies.gifts()[1]._id))
                                    .restaurantId(toDbId(dependencies.restaurants()[0]._id))
                                    .quantity(2)
                                    .build(),
                            ];
                        },
                    },
                    restaurantWheelsOfFortune: {
                        data(dependencies) {
                            return [
                                getDefaultRestaurantWheelsOfFortune()
                                    .restaurantId(toDbId(dependencies.restaurants()[0]._id))
                                    .giftIds([toDbId(dependencies.gifts()[0]._id), toDbId(dependencies.gifts()[1]._id)])
                                    .startDate(startDate)
                                    .endDate(endDate)
                                    .parameters(parameters)
                                    .build(),
                            ];
                        },
                    },
                    clients: {
                        data() {
                            return [getDefaultClient().build(), getDefaultClient().build()];
                        },
                    },
                    giftDraws: {
                        data(dependencies) {
                            return [
                                getDefaultGiftDraw()
                                    .giftId(toDbId(dependencies.gifts()[0]._id))
                                    .restaurantId(toDbId(dependencies.restaurants()[0]._id))
                                    .clientId(toDbId(dependencies.clients()[0]._id))
                                    .wheelOfFortuneId(toDbId(dependencies.restaurantWheelsOfFortune()[0]._id))
                                    .retrievedAt(undefined)
                                    .retrievalEndDate(firstRetrievalEndDate)
                                    .build(),
                                getDefaultGiftDraw()
                                    .giftId(toDbId(dependencies.gifts()[0]._id))
                                    .restaurantId(toDbId(dependencies.restaurants()[0]._id))
                                    .clientId(toDbId(dependencies.clients()[1]._id))
                                    .wheelOfFortuneId(toDbId(dependencies.restaurantWheelsOfFortune()[0]._id))
                                    .retrievedAt(undefined)
                                    .retrievalEndDate(secondRetrievalEndDate)
                                    .build(),
                            ];
                        },
                    },
                },
                expectedResult(dependencies) {
                    return [
                        {
                            _id: dependencies.giftDraws[0]._id,
                            wheelOfFortuneId: dependencies.restaurantWheelsOfFortune[0]._id,
                            giftId: dependencies.gifts[0]._id,
                            restaurantId: dependencies.restaurants[0]._id,
                            clientId: dependencies.clients[0]._id,
                            gift: omit(dependencies.gifts[0], 'id'),
                            restaurant: omit(
                                {
                                    ...dependencies.restaurants[0],
                                    ai: omit(dependencies.restaurants[0].ai, 'id'),
                                    boosterPack: omit(dependencies.restaurants[0].boosterPack, 'id'),
                                },
                                'id',
                                'boosterPack'
                            ),
                            client: omit(dependencies.clients[0], 'id'),
                            retrievalStartDate: dependencies.giftDraws[0].retrievalStartDate,
                            retrievalEndDate: dependencies.giftDraws[0].retrievalEndDate,
                            retrievedAt: dependencies.giftDraws[0].retrievedAt,
                            createdAt: dependencies.giftDraws[0].createdAt,
                            updatedAt: dependencies.giftDraws[0].updatedAt,
                            lang: dependencies.giftDraws[0].lang,
                        },
                    ];
                },
            });
            await testCase.build();

            const result = await giftDrawsUseCases.getSoonExpiredDraws();

            const expectedResult = testCase.getExpectedResult();
            expect(result.map((res) => omit(res, 'restaurant.boosterPack'))).toIncludeAllMembers(expectedResult);
        });

        it('should return the draws that expires tomorrow when giftClaimDurationInDays <= 5', async () => {
            const giftDrawsUseCases = container.resolve(GiftDrawsUseCases);
            const startDate = DateTime.now().minus({ days: 1 }).toJSDate();
            const endDate = DateTime.now().plus({ days: 1 }).toJSDate();
            const parameters: IWheelOfFortune['parameters'] = {
                primaryColor: '#111111',
                secondaryColor: '#222222',
                mediaId: null,
                giftClaimDurationInDays: 2,
                giftClaimStartDateOption: GiftClaimStartDateOption.NOW,
                redirectionSettings: DEFAULT_WHEEL_OF_FORTUNE_REDIRECTION_SETTINGS,
            };
            const firstRetrievalEndDate = DateTime.now()
                .plus({ days: 5 })
                .set({ hour: 0, minute: 0, second: 0, millisecond: 0 })
                .toJSDate();
            const secondRetrievalEndDate = DateTime.now()
                .plus({ days: 1 })
                .set({ hour: 0, minute: 0, second: 0, millisecond: 0 })
                .toJSDate();

            const testCase = new TestCaseBuilderV2<
                'restaurants' | 'gifts' | 'giftStocks' | 'restaurantWheelsOfFortune' | 'giftDraws' | 'clients'
            >({
                seeds: {
                    restaurants: {
                        data() {
                            return [getDefaultRestaurant().build()];
                        },
                    },
                    gifts: {
                        data() {
                            return [getDefaultGift().name('Gift 1').weight(1).build(), getDefaultGift().name('Gift 2').weight(2).build()];
                        },
                    },
                    giftStocks: {
                        data(dependencies) {
                            return [
                                getDefaultGiftStock()
                                    .giftId(toDbId(dependencies.gifts()[0]._id))
                                    .restaurantId(toDbId(dependencies.restaurants()[0]._id))
                                    .quantity(1)
                                    .build(),
                                getDefaultGiftStock()
                                    .giftId(toDbId(dependencies.gifts()[1]._id))
                                    .restaurantId(toDbId(dependencies.restaurants()[0]._id))
                                    .quantity(2)
                                    .build(),
                            ];
                        },
                    },
                    restaurantWheelsOfFortune: {
                        data(dependencies) {
                            return [
                                getDefaultRestaurantWheelsOfFortune()
                                    .restaurantId(toDbId(dependencies.restaurants()[0]._id))
                                    .giftIds([toDbId(dependencies.gifts()[0]._id), toDbId(dependencies.gifts()[1]._id)])
                                    .startDate(startDate)
                                    .endDate(endDate)
                                    .parameters(parameters)
                                    .build(),
                            ];
                        },
                    },
                    clients: {
                        data() {
                            return [getDefaultClient().build(), getDefaultClient().build()];
                        },
                    },
                    giftDraws: {
                        data(dependencies) {
                            return [
                                getDefaultGiftDraw()
                                    .giftId(toDbId(dependencies.gifts()[0]._id))
                                    .restaurantId(toDbId(dependencies.restaurants()[0]._id))
                                    .clientId(toDbId(dependencies.clients()[0]._id))
                                    .wheelOfFortuneId(toDbId(dependencies.restaurantWheelsOfFortune()[0]._id))
                                    .retrievedAt(undefined)
                                    .retrievalEndDate(firstRetrievalEndDate)
                                    .build(),
                                getDefaultGiftDraw()
                                    .giftId(toDbId(dependencies.gifts()[0]._id))
                                    .restaurantId(toDbId(dependencies.restaurants()[0]._id))
                                    .clientId(toDbId(dependencies.clients()[1]._id))
                                    .wheelOfFortuneId(toDbId(dependencies.restaurantWheelsOfFortune()[0]._id))
                                    .retrievedAt(undefined)
                                    .retrievalEndDate(secondRetrievalEndDate)
                                    .build(),
                            ];
                        },
                    },
                },
                expectedResult(dependencies) {
                    return [
                        {
                            _id: dependencies.giftDraws[1]._id,
                            wheelOfFortuneId: dependencies.restaurantWheelsOfFortune[0]._id,
                            giftId: dependencies.gifts[0]._id,
                            restaurantId: dependencies.restaurants[0]._id,
                            clientId: dependencies.clients[1]._id,
                            gift: omit(dependencies.gifts[0], 'id'),
                            restaurant: omit(
                                {
                                    ...dependencies.restaurants[0],
                                    ai: omit(dependencies.restaurants[0].ai, 'id'),
                                    boosterPack: omit(dependencies.restaurants[0].boosterPack, 'id'),
                                },
                                'id',
                                'boosterPack'
                            ),
                            client: omit(dependencies.clients[1], 'id'),
                            retrievalStartDate: dependencies.giftDraws[1].retrievalStartDate,
                            retrievalEndDate: dependencies.giftDraws[1].retrievalEndDate,
                            retrievedAt: dependencies.giftDraws[1].retrievedAt,
                            createdAt: dependencies.giftDraws[1].createdAt,
                            updatedAt: dependencies.giftDraws[1].updatedAt,
                            lang: dependencies.giftDraws[0].lang,
                        },
                    ];
                },
            });
            await testCase.build();

            const result: any = await giftDrawsUseCases.getSoonExpiredDraws();
            const expectedResult = testCase.getExpectedResult();
            expect(result.map((res) => omit(res, 'restaurant.boosterPack'))).toIncludeAllMembers(expectedResult);
        });
    });

    describe('assignClientToGiftDraw', () => {
        it('should throw an error when gift draw is not found', async () => {
            const giftDrawsUseCases = container.resolve(GiftDrawsUseCases);

            const testCase = new TestCaseBuilderV2<'giftDraws' | 'clients'>({
                seeds: {
                    clients: {
                        data() {
                            return [getDefaultClient().build()];
                        },
                    },
                    giftDraws: {
                        data() {
                            return [getDefaultGiftDraw().build()];
                        },
                    },
                },
                expectedErrorCode: MalouErrorCode.GIFT_DRAW_NOT_FOUND,
            });
            await testCase.build();

            const seededObjects = testCase.getSeededObjects();

            const clientId = (seededObjects.clients[0]._id as DbId).toString();

            const expectedErrorCode = testCase.getExpectedErrorCode();
            let errorCode: MalouErrorCode | undefined;

            await giftDrawsUseCases.assignClientToGiftDraw(newDbId().toString(), clientId).catch((error: MalouError) => {
                errorCode = error.malouErrorCode;
            });
            expect(errorCode).toEqual(expectedErrorCode);
        });

        it('should throw an error when client already played', async () => {
            const giftDrawsUseCases = container.resolve(GiftDrawsUseCases);
            const tomorrow = DateTime.now().plus({ day: 1 }).toJSDate();

            const testCase = new TestCaseBuilderV2<'giftDraws' | 'clients' | 'restaurants' | 'restaurantWheelsOfFortune'>({
                seeds: {
                    restaurants: {
                        data() {
                            return [getDefaultRestaurant().build()];
                        },
                    },
                    restaurantWheelsOfFortune: {
                        data(dependencies) {
                            return [getDefaultRestaurantWheelsOfFortune().restaurantId(toDbId(dependencies.restaurants()[0]._id)).build()];
                        },
                    },
                    clients: {
                        data() {
                            return [getDefaultClient().build()];
                        },
                    },
                    giftDraws: {
                        data(dependencies) {
                            return [
                                getDefaultGiftDraw()
                                    .restaurantId(dependencies.restaurants()[0]._id)
                                    .wheelOfFortuneId(dependencies.restaurantWheelsOfFortune()[0]._id)
                                    .build(),
                                getDefaultGiftDraw()
                                    .restaurantId(dependencies.restaurants()[0]._id)
                                    .wheelOfFortuneId(dependencies.restaurantWheelsOfFortune()[0]._id)
                                    .clientId(dependencies.clients()[0]._id)
                                    .retrievalEndDate(tomorrow)
                                    .build(),
                            ];
                        },
                    },
                },
                expectedErrorCode: MalouErrorCode.CLIENT_ALREADY_PLAYED,
            });
            await testCase.build();

            const seededObjects = testCase.getSeededObjects();

            const clientId = (seededObjects.clients[0]._id as DbId).toString();
            const giftDrawId = (seededObjects.giftDraws[0]._id as DbId).toString();

            const expectedErrorCode = testCase.getExpectedErrorCode();
            let errorCode: MalouErrorCode | undefined;

            await giftDrawsUseCases.assignClientToGiftDraw(giftDrawId, clientId).catch((error: MalouError) => {
                errorCode = error.malouErrorCode;
            });
            expect(errorCode).toEqual(expectedErrorCode);
        });

        it('should not throw an error when client already played but previous gift expired', async () => {
            const giftDrawsUseCases = container.resolve(GiftDrawsUseCases);
            const yesterday = DateTime.now().minus({ day: 1 }).toJSDate();
            const testCase = new TestCaseBuilderV2<'giftDraws' | 'clients' | 'restaurants' | 'restaurantWheelsOfFortune'>({
                seeds: {
                    restaurants: {
                        data() {
                            return [getDefaultRestaurant().build()];
                        },
                    },
                    restaurantWheelsOfFortune: {
                        data(dependencies) {
                            return [getDefaultRestaurantWheelsOfFortune().restaurantId(toDbId(dependencies.restaurants()[0]._id)).build()];
                        },
                    },
                    clients: {
                        data() {
                            return [getDefaultClient().build()];
                        },
                    },
                    giftDraws: {
                        data(dependencies) {
                            return [
                                getDefaultGiftDraw()
                                    .restaurantId(dependencies.restaurants()[0]._id)
                                    .wheelOfFortuneId(dependencies.restaurantWheelsOfFortune()[0]._id)
                                    .build(),
                                getDefaultGiftDraw()
                                    .restaurantId(dependencies.restaurants()[0]._id)
                                    .wheelOfFortuneId(dependencies.restaurantWheelsOfFortune()[0]._id)
                                    .clientId(dependencies.clients()[0]._id)
                                    .retrievalEndDate(yesterday)
                                    .build(),
                            ];
                        },
                    },
                },
                expectedErrorCode: undefined,
            });
            await testCase.build();

            const seededObjects = testCase.getSeededObjects();

            const clientId = (seededObjects.clients[0]._id as DbId).toString();
            const giftDrawId = (seededObjects.giftDraws[0]._id as DbId).toString();

            const expectedErrorCode = testCase.getExpectedErrorCode();
            let errorCode: MalouErrorCode | undefined;

            await giftDrawsUseCases.assignClientToGiftDraw(giftDrawId, clientId).catch((error: MalouError) => {
                errorCode = error.malouErrorCode;
            });
            expect(errorCode).toEqual(expectedErrorCode);
        });

        it('should update giftDraw with client id', async () => {
            const giftDrawsUseCases = container.resolve(GiftDrawsUseCases);
            const testCase = new TestCaseBuilderV2<'giftDraws' | 'gifts' | 'clients' | 'restaurants' | 'restaurantWheelsOfFortune'>({
                seeds: {
                    restaurants: {
                        data() {
                            return [getDefaultRestaurant().build()];
                        },
                    },
                    restaurantWheelsOfFortune: {
                        data(dependencies) {
                            return [getDefaultRestaurantWheelsOfFortune().restaurantId(toDbId(dependencies.restaurants()[0]._id)).build()];
                        },
                    },
                    clients: {
                        data() {
                            return [getDefaultClient().build()];
                        },
                    },
                    gifts: {
                        data() {
                            return [getDefaultGift().name('Gift 1').weight(1).build()];
                        },
                    },
                    giftDraws: {
                        data(dependencies) {
                            return [
                                getDefaultGiftDraw()
                                    .restaurantId(dependencies.restaurants()[0]._id)
                                    .wheelOfFortuneId(dependencies.restaurantWheelsOfFortune()[0]._id)
                                    .build(),
                                getDefaultGiftDraw()
                                    .restaurantId(dependencies.restaurants()[0]._id)
                                    .wheelOfFortuneId(dependencies.restaurantWheelsOfFortune()[0]._id)
                                    .build(),
                            ];
                        },
                    },
                },
                expectedResult(dependencies): GiftDrawDto {
                    return {
                        id: dependencies.giftDraws[0]._id.toString(),
                        giftId: dependencies.giftDraws[0].giftId.toString(),
                        restaurantId: dependencies.giftDraws[0].restaurantId.toString(),
                        clientId: dependencies.clients[0]._id.toString() || null,
                        wheelOfFortuneId: dependencies.giftDraws[0].wheelOfFortuneId.toString(),
                        retrievalStartDate: dependencies.giftDraws[0].retrievalStartDate.toISOString(),
                        retrievalEndDate: dependencies.giftDraws[0].retrievalEndDate.toISOString(),
                        retrievedAt: dependencies.giftDraws[0].retrievedAt?.toISOString() || null,
                        lang: dependencies.giftDraws[0].lang,
                    };
                },
            });
            await testCase.build();

            const seededObjects = testCase.getSeededObjects();

            const clientId = (seededObjects.clients[0]._id as DbId).toString();
            const giftDrawId = (seededObjects.giftDraws[0]._id as DbId).toString();

            const expectedResult = testCase.getExpectedResult();
            const result = await giftDrawsUseCases.assignClientToGiftDraw(giftDrawId, clientId);
            expect(result).toEqual(expectedResult);
        });
    });

    describe('sendRetrievalEmail', () => {
        it('should throw an error if gift draw is not found', async () => {
            const giftDrawsUseCases = container.resolve(GiftDrawsUseCases);

            const testCase = new TestCaseBuilderV2({
                seeds: {},
                expectedErrorCode: MalouErrorCode.GIFT_DRAW_NOT_FOUND,
            });
            await testCase.build();

            const expectedErrorCode = testCase.getExpectedErrorCode();
            let errorCode: MalouErrorCode | undefined;

            await giftDrawsUseCases.sendRetrievalEmail(newDbId().toString()).catch((error: MalouError) => {
                errorCode = error.malouErrorCode;
            });
            expect(errorCode).toEqual(expectedErrorCode);
        });

        it('should throw an error if client has no email', async () => {
            const giftDrawsUseCases = container.resolve(GiftDrawsUseCases);

            const testCase = new TestCaseBuilderV2<'giftDraws' | 'clients'>({
                seeds: {
                    clients: {
                        data() {
                            return [getDefaultClient().build()];
                        },
                    },
                    giftDraws: {
                        data(dependencies) {
                            return [getDefaultGiftDraw().clientId(dependencies.clients()[0]._id).build()];
                        },
                    },
                },
                expectedErrorCode: MalouErrorCode.CLIENT_EMAIL_NOT_FOUND,
            });
            await testCase.build();

            const seededObjects = testCase.getSeededObjects();

            const expectedErrorCode = testCase.getExpectedErrorCode();
            let errorCode: MalouErrorCode | undefined;

            await giftDrawsUseCases.sendRetrievalEmail((seededObjects.giftDraws[0]._id as DbId).toString()).catch((error: MalouError) => {
                errorCode = error.malouErrorCode;
            });
            expect(errorCode).toEqual(expectedErrorCode);
        });
    });

    describe('cancelGiftDraw', () => {
        it('should throw an error if gift draw is not found', async () => {
            const giftDrawsUseCases = container.resolve(GiftDrawsUseCases);

            const testCase = new TestCaseBuilderV2({
                seeds: {},
                expectedErrorCode: MalouErrorCode.GIFT_DRAW_NOT_FOUND,
            });
            await testCase.build();

            const expectedErrorCode = testCase.getExpectedErrorCode();
            let errorCode: MalouErrorCode | undefined;
            await giftDrawsUseCases.cancelGiftDraw(newDbId().toString()).catch((error: MalouError) => {
                errorCode = error.malouErrorCode;
            });
            expect(errorCode).toBe(expectedErrorCode);
        });

        it('should increment the stock of the gift that has been drawn ', async () => {
            const giftDrawsUseCases = container.resolve(GiftDrawsUseCases);

            const testCase = new TestCaseBuilderV2<'restaurants' | 'gifts' | 'giftStocks' | 'giftDraws'>({
                seeds: {
                    restaurants: {
                        data() {
                            return [getDefaultRestaurant().build()];
                        },
                    },
                    gifts: {
                        data() {
                            return [getDefaultGift().name('Gift 1').weight(1).build()];
                        },
                    },
                    giftStocks: {
                        data(dependencies) {
                            return [
                                getDefaultGiftStock()
                                    .restaurantId(dependencies.restaurants()[0]._id)
                                    .giftId(toDbId(dependencies.gifts()[0]._id))
                                    .quantity(1)
                                    .build(),
                            ];
                        },
                    },
                    giftDraws: {
                        data(dependencies) {
                            return [
                                getDefaultGiftDraw()
                                    .restaurantId(dependencies.restaurants()[0]._id)
                                    .giftId(dependencies.gifts()[0]._id)
                                    .build(),
                            ];
                        },
                    },
                },
                expectedResult(dependencies) {
                    return dependencies.giftStocks[0].quantity! + 1;
                },
            });
            await testCase.build();
            const seededObjects = testCase.getSeededObjects();
            const drawId = (seededObjects.giftDraws[0]._id as DbId).toString();

            await giftDrawsUseCases.cancelGiftDraw(drawId);

            const result = await container.resolve(GiftStocksRepository).findOne({
                filter: { giftId: seededObjects.gifts[0]._id },
            });
            const expectedResult = testCase.getExpectedResult();
            expect(result?.quantity).toBe(expectedResult);
        });
    });

    describe('getGiftDrawsInsightsPerRestaurant', () => {
        it('should return the correct restaurant gift draw insights per restaurant according to the given period', async () => {
            const giftDrawsUseCases = container.resolve(GiftDrawsUseCases);
            const tenDaysAgo = DateTime.now().minus({ days: 10 }).toJSDate();
            const fiveDaysAgo = DateTime.now().minus({ days: 5 }).toJSDate();
            const startDate = DateTime.now().minus({ days: 7 }).toJSDate();
            const endDate = DateTime.now().toJSDate();

            const testCase = new TestCaseBuilderV2<'restaurants' | 'gifts' | 'giftDraws' | 'clients'>({
                seeds: {
                    restaurants: {
                        data() {
                            return [
                                getDefaultRestaurant().uniqueKey('facebook_132987498279187').build(),
                                getDefaultRestaurant().uniqueKey('facebook_404282094862822').build(),
                            ];
                        },
                    },
                    gifts: {
                        data() {
                            return [
                                getDefaultGift().name('Gift 1').build(),
                                getDefaultGift().name('Gift 2').build(),
                                getDefaultGift().name('Gift 3').build(),
                            ];
                        },
                    },
                    clients: {
                        data() {
                            return [
                                getDefaultClient().email('<EMAIL>').build(),
                                getDefaultClient().email('<EMAIL>').build(),
                                getDefaultClient().email('<EMAIL>').build(),
                                getDefaultClient().email('<EMAIL>').build(),
                                getDefaultClient().email('<EMAIL>').build(),
                            ];
                        },
                    },
                    giftDraws: {
                        data(dependencies) {
                            return [
                                getDefaultGiftDraw()
                                    .restaurantId(dependencies.restaurants()[0]._id)
                                    .giftId(dependencies.gifts()[0]._id)
                                    .clientId(dependencies.clients()[0]._id)
                                    .retrievedAt(undefined)
                                    .createdAt(tenDaysAgo)
                                    .build(),
                                getDefaultGiftDraw()
                                    .restaurantId(dependencies.restaurants()[1]._id)
                                    .giftId(dependencies.gifts()[2]._id)
                                    .clientId(dependencies.clients()[0]._id)
                                    .retrievedAt(tenDaysAgo)
                                    .createdAt(tenDaysAgo)
                                    .build(),
                                getDefaultGiftDraw()
                                    .restaurantId(dependencies.restaurants()[0]._id)
                                    .giftId(dependencies.gifts()[1]._id)
                                    .clientId(dependencies.clients()[1]._id)
                                    .retrievedAt(tenDaysAgo)
                                    .createdAt(tenDaysAgo)
                                    .build(),
                                getDefaultGiftDraw()
                                    .restaurantId(dependencies.restaurants()[1]._id)
                                    .giftId(dependencies.gifts()[1]._id)
                                    .clientId(dependencies.clients()[1]._id)
                                    .retrievedAt(tenDaysAgo)
                                    .createdAt(tenDaysAgo)
                                    .build(),
                                getDefaultGiftDraw()
                                    .restaurantId(dependencies.restaurants()[0]._id)
                                    .giftId(dependencies.gifts()[0]._id)
                                    .clientId(dependencies.clients()[2]._id)
                                    .retrievedAt(undefined)
                                    .createdAt(tenDaysAgo)
                                    .build(),
                                getDefaultGiftDraw()
                                    .restaurantId(dependencies.restaurants()[1]._id)
                                    .giftId(dependencies.gifts()[0]._id)
                                    .clientId(dependencies.clients()[2]._id)
                                    .retrievedAt(tenDaysAgo)
                                    .createdAt(tenDaysAgo)
                                    .build(),
                                getDefaultGiftDraw()
                                    .restaurantId(dependencies.restaurants()[0]._id)
                                    .giftId(dependencies.gifts()[2]._id)
                                    .clientId(dependencies.clients()[3]._id)
                                    .retrievedAt(undefined)
                                    .createdAt(tenDaysAgo)
                                    .build(),
                                getDefaultGiftDraw()
                                    .restaurantId(dependencies.restaurants()[1]._id)
                                    .giftId(dependencies.gifts()[2]._id)
                                    .clientId(dependencies.clients()[4]._id)
                                    .retrievedAt(undefined)
                                    .createdAt(tenDaysAgo)
                                    .build(),
                                getDefaultGiftDraw()
                                    .restaurantId(dependencies.restaurants()[0]._id)
                                    .giftId(dependencies.gifts()[0]._id)
                                    .clientId(dependencies.clients()[0]._id)
                                    .retrievedAt(fiveDaysAgo)
                                    .createdAt(fiveDaysAgo)
                                    .build(),
                                getDefaultGiftDraw()
                                    .restaurantId(dependencies.restaurants()[0]._id)
                                    .giftId(dependencies.gifts()[1]._id)
                                    .clientId(dependencies.clients()[0]._id)
                                    .retrievedAt(fiveDaysAgo)
                                    .createdAt(fiveDaysAgo)
                                    .build(),
                                getDefaultGiftDraw()
                                    .restaurantId(dependencies.restaurants()[0]._id)
                                    .giftId(dependencies.gifts()[2]._id)
                                    .clientId(dependencies.clients()[0]._id)
                                    .retrievedAt(undefined)
                                    .createdAt(fiveDaysAgo)
                                    .build(),
                                getDefaultGiftDraw()
                                    .restaurantId(dependencies.restaurants()[0]._id)
                                    .giftId(dependencies.gifts()[2]._id)
                                    .clientId(dependencies.clients()[0]._id)
                                    .retrievedAt(fiveDaysAgo)
                                    .createdAt(fiveDaysAgo)
                                    .build(),
                                getDefaultGiftDraw()
                                    .restaurantId(dependencies.restaurants()[1]._id)
                                    .giftId(dependencies.gifts()[0]._id)
                                    .clientId(dependencies.clients()[0]._id)
                                    .retrievedAt(undefined)
                                    .createdAt(fiveDaysAgo)
                                    .build(),
                                getDefaultGiftDraw()
                                    .restaurantId(dependencies.restaurants()[1]._id)
                                    .giftId(dependencies.gifts()[0]._id)
                                    .clientId(dependencies.clients()[0]._id)
                                    .retrievedAt(fiveDaysAgo)
                                    .createdAt(fiveDaysAgo)
                                    .build(),
                            ];
                        },
                    },
                },
                expectedResult(dependencies): GiftDrawInsightPerRestaurantDto[] {
                    return [
                        {
                            restaurant: dependencies.restaurants[0].name,
                            giftDrawCount: 4,
                            previousGiftDrawCount: 4,
                            retrievedGiftDrawCount: 3,
                            previousRetrievedGiftDrawCount: 1,
                        },
                        {
                            restaurant: dependencies.restaurants[1].name,
                            giftDrawCount: 2,
                            previousGiftDrawCount: 4,
                            retrievedGiftDrawCount: 1,
                            previousRetrievedGiftDrawCount: 3,
                        },
                    ];
                },
            });
            await testCase.build();

            const seededObjects = testCase.getSeededObjects();
            const expectedResult = testCase.getExpectedResult();

            const restaurantIds = [
                (seededObjects.restaurants[0]._id as DbId).toString(),
                (seededObjects.restaurants[1]._id as DbId).toString(),
            ];
            const result = await giftDrawsUseCases.getGiftDrawsInsightsPerRestaurant({
                restaurantIds,
                startDate,
                endDate,
                comparisonPeriod: MalouComparisonPeriod.PREVIOUS_PERIOD,
            });

            expect(result).toIncludeAllMembers(expectedResult);
        });
    });
});
