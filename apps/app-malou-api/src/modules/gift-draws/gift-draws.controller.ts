import { NextFunction, Request, Response } from 'express';
import { singleton } from 'tsyringe';

import {
    assignClientToGiftDrawParamsValidator,
    CancelGiftDrawParamsDto,
    cancelGiftDrawParamsValidator,
    createGiftDrawBodyValidator,
    CreateGiftDrawParamsDto,
    createGiftDrawParamsValidator,
    GetGiftDrawByIdParamsDto,
    getGiftDrawByIdParamsValidator,
    GetGiftDrawInsightPerRestaurantBodyDto,
    getGiftDrawInsightPerRestaurantBodyValidator,
    GetGiftDrawInsightsPerGiftParamsDto,
    getGiftDrawInsightsPerGiftParamsValidator,
    GetGiftDrawInsightsPerGiftQueryDto,
    getGiftDrawInsightsPerGiftQueryValidator,
    GiftDrawDto,
    GiftDrawInsightPerGiftDto,
    GiftDrawInsightPerRestaurantDto,
    GiftDrawPopulatedDto,
    SendRetrievalEmailParamsDto,
    sendRetrievalEmailParamsValidator,
    SetGiftDrawRetrievedParamsDto,
    setGiftDrawRetrievedParamsValidator,
    UpdateGiftDrawParamsDto,
} from '@malou-io/package-dto';
import { ApiResultV2, errorReplacer } from '@malou-io/package-utils';

import { Body, Params, Query } from ':helpers/decorators/validators';
import { logger } from ':helpers/logger';
import { GetGiftDrawsInsightsPerGiftUseCase } from ':modules/gift-draws/use-cases/get-gift-draws-insights-per-gift/get-gift-draws-insights-per-gift.use-case';
import { SendGiftWonComoEventUseCase } from ':modules/gift-draws/use-cases/send-gift-won-como-event/send-gift-won-como-event.use-case';

import { GiftDrawsUseCases } from './gift-draws.use-cases';

@singleton()
export class GiftDrawsController {
    constructor(
        private readonly _giftDrawsUseCases: GiftDrawsUseCases,
        private readonly _sendGiftWonComoEvent: SendGiftWonComoEventUseCase,
        private readonly _getGiftDrawsInsightsPerGiftUseCase: GetGiftDrawsInsightsPerGiftUseCase
    ) {}

    @Params(createGiftDrawParamsValidator)
    @Body(createGiftDrawBodyValidator)
    async handleCreateGiftDraw(req: Request<CreateGiftDrawParamsDto>, res: Response<ApiResultV2<GiftDrawDto>>, next: NextFunction) {
        try {
            const { restaurantId, wheelOfFortuneId } = req.params;
            const { lang, clientId } = req.body;
            const giftDraw = await this._giftDrawsUseCases.createGiftDraw(restaurantId, wheelOfFortuneId, lang, clientId);

            res.json({ data: giftDraw });
        } catch (error) {
            next(error);
        }
    }

    @Params(assignClientToGiftDrawParamsValidator)
    async handleAssignClientToGiftDraw(req: Request<UpdateGiftDrawParamsDto>, res: Response<ApiResultV2<GiftDrawDto>>, next: NextFunction) {
        try {
            const { giftDrawId, clientId } = req.params;

            const giftDraw = await this._giftDrawsUseCases.assignClientToGiftDraw(giftDrawId, clientId);

            res.json({ data: giftDraw });
        } catch (error) {
            next(error);
        }
    }

    @Params(sendRetrievalEmailParamsValidator)
    async handleSendRetrievalEmail(req: Request<SendRetrievalEmailParamsDto>, res: Response<ApiResultV2>, next: NextFunction) {
        try {
            const { giftDrawId } = req.params;

            await this._giftDrawsUseCases.sendRetrievalEmail(giftDrawId);

            res.json({});
        } catch (error) {
            next(error);
        }
    }

    @Params(getGiftDrawByIdParamsValidator)
    async handleGetGiftDrawById(
        req: Request<GetGiftDrawByIdParamsDto>,
        res: Response<ApiResultV2<GiftDrawPopulatedDto>>,
        next: NextFunction
    ) {
        try {
            const { giftDrawId } = req.params;

            const giftDraw = await this._giftDrawsUseCases.getGiftDrawById(giftDrawId);

            res.json({ data: giftDraw });
        } catch (error) {
            next(error);
        }
    }

    @Params(setGiftDrawRetrievedParamsValidator)
    async handleSetGiftDrawRetrieved(
        req: Request<SetGiftDrawRetrievedParamsDto>,
        res: Response<ApiResultV2<GiftDrawPopulatedDto>>,
        next: NextFunction
    ) {
        try {
            const { giftDrawId } = req.params;

            const giftDraw = await this._giftDrawsUseCases.setGiftDrawRetrieved(giftDrawId);

            res.json({ data: giftDraw });
        } catch (error) {
            next(error);
        }
    }

    @Params(cancelGiftDrawParamsValidator)
    async handleCancelDraw(req: Request<CancelGiftDrawParamsDto>, res: Response<ApiResultV2>, next: NextFunction) {
        try {
            const { giftDrawId } = req.params;

            await this._giftDrawsUseCases.cancelGiftDraw(giftDrawId);

            res.json({});
        } catch (error) {
            next(error);
        }
    }

    @Params(getGiftDrawInsightsPerGiftParamsValidator)
    @Query(getGiftDrawInsightsPerGiftQueryValidator)
    async handleGetGiftDrawInsightsPerGift(
        req: Request<GetGiftDrawInsightsPerGiftParamsDto, any, any, GetGiftDrawInsightsPerGiftQueryDto>,
        res: Response<ApiResultV2<GiftDrawInsightPerGiftDto[]>>,
        next: NextFunction
    ) {
        try {
            const { restaurantId } = req.params;
            const { startDate, endDate, comparisonPeriod } = req.query;
            const insights = await this._getGiftDrawsInsightsPerGiftUseCase.execute({ restaurantId, startDate, endDate, comparisonPeriod });

            res.json({ data: insights });
        } catch (error) {
            next(error);
        }
    }

    @Body(getGiftDrawInsightPerRestaurantBodyValidator)
    async handleGetGiftDrawInsightsPerRestaurant(
        req: Request<any, any, GetGiftDrawInsightPerRestaurantBodyDto>,
        res: Response<ApiResultV2<GiftDrawInsightPerRestaurantDto[]>>,
        next: NextFunction
    ) {
        try {
            const { restaurantIds, startDate, endDate, comparisonPeriod } = req.body;
            const insights = await this._giftDrawsUseCases.getGiftDrawsInsightsPerRestaurant({
                restaurantIds,
                startDate,
                endDate,
                comparisonPeriod,
            });
            res.json({ data: insights });
        } catch (error) {
            logger.error('[ERROR_AGGREGATED_STATS] [GIFTS]', {
                error: JSON.stringify(error, errorReplacer),
                query: JSON.stringify(req.query),
            });
            next(error);
        }
    }

    @Params(getGiftDrawByIdParamsValidator)
    async handleSendGiftWonComoEvent(req: Request<GetGiftDrawByIdParamsDto>, res: Response<void>, next: NextFunction) {
        try {
            const { giftDrawId } = req.params;

            await this._sendGiftWonComoEvent.execute(giftDrawId);

            res.status(204).end();
        } catch (error) {
            next(error);
        }
    }
}
