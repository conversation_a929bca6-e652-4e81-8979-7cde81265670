import { DateTime } from 'luxon';
import { singleton } from 'tsyringe';

import { EntityRepository, GiftDrawModel, IGiftDraw, PopulateBuilderHelper, toDbId } from '@malou-io/package-models';

import { GiftDrawInsight } from './gift-draws.interface';

@singleton()
export class GiftDrawsRepository extends EntityRepository<IGiftDraw> {
    constructor() {
        super(GiftDrawModel);
    }

    async createAndPopulate(giftDraw: Partial<IGiftDraw>) {
        const createdGiftDraw = await this.create({
            data: giftDraw,
            options: { lean: true },
        });
        return this.findOne({
            filter: { _id: createdGiftDraw._id },
            options: {
                populate: [
                    {
                        path: 'gift',
                    },
                    {
                        path: 'client',
                    },
                    {
                        path: 'restaurant',
                        populate: [
                            {
                                path: 'logoPopulated',
                            },
                        ],
                    },
                ],
                lean: true,
            },
        }) as any as PopulateBuilderHelper<
            IGiftDraw,
            [
                {
                    path: 'gift';
                },
                {
                    path: 'client';
                },
                {
                    path: 'restaurant';
                    populate: [
                        {
                            path: 'logoPopulated';
                        },
                    ];
                },
            ]
        >;
    }

    findGiftDrawsForExistingClientForRestaurantAndWheelOfFortune(
        restaurantId: string,
        wheelOfFortuneId: string,
        clientId: string
    ): Promise<IGiftDraw[]> {
        return this.find({
            filter: {
                restaurantId: toDbId(restaurantId),
                wheelOfFortuneId: toDbId(wheelOfFortuneId),
                clientId: toDbId(clientId),
            },
            options: {
                lean: true,
            },
        });
    }

    findGiftDrawsInsightsPerGift({
        restaurantId,
        startPeriod,
        endPeriod,
        startPreviousPeriod,
        endPreviousPeriod,
    }: {
        restaurantId: string;
        startPeriod: Date;
        endPeriod: Date;
        startPreviousPeriod: Date;
        endPreviousPeriod: Date;
    }): Promise<GiftDrawInsight[]> {
        const pipeline = this._buildGetGiftDrawsInsightsPerGiftPipeline({
            restaurantId,
            startPeriod,
            endPeriod,
            startPreviousPeriod,
            endPreviousPeriod,
        });

        return this.aggregate(pipeline, { lean: true }) as any;
    }

    findGiftDrawsInsightsPerRestaurants({
        currentPeriod,
        previousPeriod,
        restaurantIds,
    }: {
        restaurantIds: string[];
        currentPeriod: { startDate: Date; endDate: Date };
        previousPeriod: { startDate: Date; endDate: Date };
    }): Promise<GiftDrawInsight[]> {
        const pipeline = this._buildGetGiftDrawsInsightsPerRestaurantPipeline({
            currentPeriod,
            previousPeriod,
            restaurantIds,
        });
        return this.aggregate(pipeline, { lean: true }) as any;
    }

    findSoonExpiredGiftDraws(): Promise<
        PopulateBuilderHelper<
            IGiftDraw,
            [
                {
                    path: 'gift';
                },
                {
                    path: 'client';
                },
                {
                    path: 'restaurant';
                    populate: [
                        {
                            path: 'logoPopulated';
                        },
                    ];
                },
            ]
        >[]
    > {
        const pipeline = [
            ...this._buildMatchGiftDrawsWithClientNotRetrievedYetStage(),
            ...this._buildLookupWheelOfFortuneStage(),
            ...this._buildMatchGiftDrawsByRetrievalEndDate(),
            ...this._buildLookupGiftStage(),
            ...this._buildLookupRestaurantStage(),
            ...this._buildLookupClientStage(),
            ...this._buildProjectStage(),
        ];

        return this.aggregate(pipeline, { lean: true }) as any;
    }

    private _buildMatchGiftDrawsWithClientNotRetrievedYetStage(): any[] {
        return [
            {
                $match: {
                    clientId: { $ne: null },
                    retrievedAt: null,
                },
            },
        ];
    }

    private _buildLookupWheelOfFortuneStage() {
        return [
            {
                $lookup: {
                    from: 'wheelsoffortune',
                    localField: 'wheelOfFortuneId',
                    foreignField: '_id',
                    as: 'wheelOfFortune',
                },
            },
            {
                $unwind: {
                    path: '$wheelOfFortune',
                    preserveNullAndEmptyArrays: false,
                },
            },
        ];
    }

    private _buildLookupGiftStage(localField = 'giftId') {
        return [
            {
                $lookup: {
                    from: 'gifts',
                    localField,
                    foreignField: '_id',
                    as: 'gift',
                },
            },
            {
                $unwind: {
                    path: '$gift',
                    preserveNullAndEmptyArrays: false,
                },
            },
        ];
    }

    private _buildLookupRestaurantStage() {
        return [
            {
                $lookup: {
                    from: 'restaurants',
                    localField: 'restaurantId',
                    foreignField: '_id',
                    as: 'restaurant',
                },
            },
            {
                $unwind: {
                    path: '$restaurant',
                    preserveNullAndEmptyArrays: false,
                },
            },
        ];
    }

    private _buildLookupClientStage() {
        return [
            {
                $lookup: {
                    from: 'clients',
                    localField: 'clientId',
                    foreignField: '_id',
                    as: 'client',
                },
            },
            {
                $unwind: {
                    path: '$client',
                    preserveNullAndEmptyArrays: false,
                },
            },
        ];
    }

    private _buildMatchGiftDrawsByRetrievalEndDate() {
        const today = DateTime.now().set({ hour: 0, minute: 0, second: 0, millisecond: 0 });
        const tomorrow = today.plus({ days: 1 });
        const in5days = today.plus({ days: 5 });

        return [
            {
                $match: {
                    $or: [
                        {
                            'wheelOfFortune.parameters.giftClaimDurationInDays': { $lte: 5 },
                            retrievalEndDate: tomorrow,
                        },
                        {
                            'wheelOfFortune.parameters.giftClaimDurationInDays': { $gt: 5 },
                            retrievalEndDate: in5days,
                        },
                    ],
                },
            },
        ];
    }

    private _buildProjectStage() {
        return [
            {
                $project: {
                    _id: 1,
                    giftId: 1,
                    restaurantId: 1,
                    clientId: 1,
                    wheelOfFortuneId: 1,
                    retrievedAt: 1,
                    retrievalStartDate: 1,
                    retrievalEndDate: 1,
                    gift: 1,
                    client: 1,
                    restaurant: 1,
                    lang: 1,
                    createdAt: 1,
                    updatedAt: 1,
                },
            },
        ];
    }

    private _buildGetGiftDrawsInsightsPerRestaurantPipeline({
        currentPeriod,
        previousPeriod,
        restaurantIds,
    }: {
        restaurantIds: string[];
        currentPeriod: { startDate: Date; endDate: Date };
        previousPeriod: { startDate: Date; endDate: Date };
    }): any[] {
        const { startDate: startPeriod, endDate: endPeriod } = currentPeriod;
        const { startDate: startPreviousPeriod, endDate: endPreviousPeriod } = previousPeriod;
        return [
            this._buildMatchByRestaurantIdsStage(restaurantIds),
            this._buildFacetForInsightsByRestaurantStage(startPeriod, endPeriod, startPreviousPeriod, endPreviousPeriod),
            ...this._buildProjectAndUnwindAfterFacetStages(),
            ...this._buildGroupByGiftIdWithMergeAndProjectStages(),
        ];
    }

    private _buildGetGiftDrawsInsightsPerGiftPipeline({
        restaurantId,
        startPeriod,
        endPeriod,
        startPreviousPeriod,
        endPreviousPeriod,
    }: {
        restaurantId: string;
        startPeriod: Date;
        endPeriod: Date;
        startPreviousPeriod: Date;
        endPreviousPeriod: Date;
    }): any[] {
        return [
            this._buildMatchByRestaurantIdStage(restaurantId),
            this._buildFacetForInsightsByGiftStage({ startPeriod, endPeriod, startPreviousPeriod, endPreviousPeriod }),
            ...this._buildProjectAndUnwindAfterFacetStages(),
            ...this._buildGroupByGiftIdWithMergeAndProjectStages(),
        ];
    }

    private _buildFacetForInsightsByGiftStage({
        startPeriod,
        endPeriod,
        startPreviousPeriod,
        endPreviousPeriod,
    }: {
        startPeriod: Date;
        endPeriod: Date;
        startPreviousPeriod: Date;
        endPreviousPeriod: Date;
    }): any {
        return {
            $facet: {
                period: this._buildGetGiftDrawsInsightsPerGiftByStageForPeriod(startPeriod, endPeriod, false),
                previousPeriod: this._buildGetGiftDrawsInsightsPerGiftByStageForPeriod(startPreviousPeriod, endPreviousPeriod, true),
            },
        };
    }

    private _buildFacetForInsightsByRestaurantStage(
        startPeriod: Date,
        endPeriod: Date,
        startPreviousPeriod: Date,
        endPreviousPeriod: Date
    ): any {
        return {
            $facet: {
                period: this._buildGetGiftDrawsInsightsPerRestaurantByStageForPeriod(
                    startPeriod,
                    DateTime.fromJSDate(endPeriod).plus({ days: 1 }).toJSDate(),
                    false
                ),
                previousPeriod: this._buildGetGiftDrawsInsightsPerRestaurantByStageForPeriod(startPreviousPeriod, endPreviousPeriod, true),
            },
        };
    }

    private _buildMatchByRestaurantIdStage(restaurantId: string): any {
        return {
            $match: {
                restaurantId: toDbId(restaurantId),
            },
        };
    }

    private _buildMatchByRestaurantIdsStage(restaurantIds: string[]): any {
        return {
            $match: {
                restaurantId: { $in: restaurantIds.map((restaurantId) => toDbId(restaurantId)) },
            },
        };
    }

    private _buildProjectAndUnwindAfterFacetStages(): any[] {
        return [
            {
                $project: {
                    temp: {
                        $concatArrays: ['$period', '$previousPeriod'],
                    },
                },
            },
            {
                $unwind: {
                    path: '$temp',
                },
            },
        ];
    }

    private _buildGroupByGiftIdWithMergeAndProjectStages(): any[] {
        return [
            {
                $group: {
                    _id: '$temp._id',
                    name: {
                        $first: '$temp.name',
                    },
                    giftDrawCount: {
                        $mergeObjects: {
                            temp: '$temp.giftDrawCount',
                        },
                    },
                    previousGiftDrawCount: {
                        $mergeObjects: {
                            temp: '$temp.previousGiftDrawCount',
                        },
                    },
                    retrievedGiftDrawCount: {
                        $mergeObjects: {
                            temp: '$temp.retrievedGiftDrawCount',
                        },
                    },
                    previousRetrievedGiftDrawCount: {
                        $mergeObjects: {
                            temp: '$temp.previousRetrievedGiftDrawCount',
                        },
                    },
                },
            },
            {
                $project: {
                    name: '$name',
                    giftDrawCount: '$giftDrawCount.temp',
                    previousGiftDrawCount: '$previousGiftDrawCount.temp',
                    retrievedGiftDrawCount: '$retrievedGiftDrawCount.temp',
                    previousRetrievedGiftDrawCount: '$previousRetrievedGiftDrawCount.temp',
                },
            },
        ];
    }

    private _buildGetGiftDrawsInsightsPerGiftByStageForPeriod(startPeriod: Date, endPeriod: Date, isPreviousPeriod: boolean): any {
        return [
            this._buildMatchByCreatedAtStage(startPeriod, endPeriod),
            this._buildGroupGiftDrawsByGiftIdStage(),
            ...this._buildAddCountFieldsStages(isPreviousPeriod),
            ...this._buildLookupGiftStage('_id'),
            this._buildProjectInsightsPerGiftStage(isPreviousPeriod),
        ];
    }

    private _buildGetGiftDrawsInsightsPerRestaurantByStageForPeriod(startPeriod: Date, endPeriod: Date, isPreviousPeriod: boolean): any {
        return [
            this._buildMatchByCreatedAtStage(startPeriod, endPeriod),
            this._buildGroupGiftDrawsByRestaurantIdStage(),
            ...this._buildAddCountFieldsStages(isPreviousPeriod),
            ...this._buildLookUpRestaurantStage('_id'),
            this._buildProjectInsightsPerRestaurantStage(isPreviousPeriod),
        ];
    }

    private _buildMatchByCreatedAtStage(startPeriod: Date, endPeriod: Date): any {
        return {
            $match: {
                createdAt: {
                    $gte: startPeriod,
                    $lt: endPeriod,
                },
            },
        };
    }

    private _buildGroupGiftDrawsByGiftIdStage(): any {
        return {
            $group: {
                _id: '$giftId',
                giftDraws: {
                    $push: {
                        retrievedAt: '$retrievedAt',
                        createdAt: '$createdAt',
                    },
                },
            },
        };
    }

    private _buildGroupGiftDrawsByRestaurantIdStage(): any {
        return {
            $group: {
                _id: '$restaurantId',
                giftDraws: {
                    $push: {
                        retrievedAt: '$retrievedAt',
                        createdAt: '$createdAt',
                    },
                },
            },
        };
    }

    private _buildAddCountFieldsStages(isPreviousPeriod: boolean): any[] {
        const addCountFieldsStageAccordingToPeriod = isPreviousPeriod
            ? {
                  $addFields: {
                      previousGiftDrawCount: {
                          $size: '$giftDraws',
                      },
                      previousRetrievedGiftDrawCount: {
                          $size: '$retrievedGiftDraws',
                      },
                  },
              }
            : {
                  $addFields: {
                      giftDrawCount: {
                          $size: '$giftDraws',
                      },
                      retrievedGiftDrawCount: {
                          $size: '$retrievedGiftDraws',
                      },
                  },
              };

        return [
            {
                $addFields: {
                    retrievedGiftDraws: {
                        $filter: {
                            input: '$giftDraws',
                            as: 'giftDraw',
                            cond: {
                                $ne: ['$$giftDraw.retrievedAt', null],
                            },
                        },
                    },
                },
            },
            addCountFieldsStageAccordingToPeriod,
        ];
    }

    private _buildLookUpRestaurantStage(localField = 'restaurantId'): any[] {
        return [
            {
                $lookup: {
                    from: 'restaurants',
                    localField,
                    foreignField: '_id',
                    as: 'restaurant',
                },
            },
            {
                $unwind: {
                    path: '$restaurant',
                },
            },
        ];
    }

    private _buildProjectInsightsPerGiftStage(isPreviousPeriod: boolean): any {
        return isPreviousPeriod
            ? {
                  $project: {
                      _id: 1,
                      name: '$gift.name',
                      previousGiftDrawCount: 1,
                      previousRetrievedGiftDrawCount: 1,
                  },
              }
            : {
                  $project: {
                      _id: 1,
                      name: '$gift.name',
                      giftDrawCount: 1,
                      retrievedGiftDrawCount: 1,
                  },
              };
    }

    private _buildProjectInsightsPerRestaurantStage(isPreviousPeriod: boolean): any {
        return isPreviousPeriod
            ? {
                  $project: {
                      _id: 1,
                      name: '$restaurant.name',
                      previousGiftDrawCount: 1,
                      previousRetrievedGiftDrawCount: 1,
                  },
              }
            : {
                  $project: {
                      _id: 1,
                      name: '$restaurant.name',
                      giftDrawCount: 1,
                      retrievedGiftDrawCount: 1,
                  },
              };
    }
}
