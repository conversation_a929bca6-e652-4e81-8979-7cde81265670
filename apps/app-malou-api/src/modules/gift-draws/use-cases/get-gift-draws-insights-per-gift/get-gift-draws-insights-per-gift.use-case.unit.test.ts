import { DateTime } from 'luxon';
import { container } from 'tsyringe';

import { GiftDrawInsightPerGiftDto } from '@malou-io/package-dto';
import { MalouComparisonPeriod } from '@malou-io/package-utils';

import { registerRepositories, TestCaseBuilderV2 } from ':helpers/tests/testing-utils';
import { getDefaultClient } from ':modules/clients/tests/clients.builder';
import { GetGiftDrawsInsightsPerGiftUseCase } from ':modules/gift-draws/use-cases/get-gift-draws-insights-per-gift/get-gift-draws-insights-per-gift.use-case';
import { getDefaultRestaurant } from ':modules/restaurants/tests/restaurant.builder';
import { getDefaultGift, getDefaultGiftDraw } from ':modules/wheels-of-fortune/tests/wheels-of-fortune.builder';

describe('GetGiftDrawsInsightsPerGiftUseCase', () => {
    beforeEach(() => {
        container.clearInstances();
        registerRepositories(['GiftDrawsRepository', 'RestaurantsRepository', 'GiftsRepository', 'ClientsRepository']);
    });
    it('should return the correct restaurant gift draw insights per gift according to the given period', async () => {
        const useCase = container.resolve(GetGiftDrawsInsightsPerGiftUseCase);
        const tenDaysAgo = DateTime.now().minus({ days: 10 }).toJSDate();
        const fiveDaysAgo = DateTime.now().minus({ days: 5 }).toJSDate();
        const startDate = DateTime.now().minus({ days: 7 }).toJSDate();
        const endDate = DateTime.now().toJSDate();

        const testCase = new TestCaseBuilderV2<'restaurants' | 'gifts' | 'giftDraws' | 'clients'>({
            seeds: {
                restaurants: {
                    data() {
                        return [
                            getDefaultRestaurant().uniqueKey('facebook_132987498279187').build(),
                            getDefaultRestaurant().uniqueKey('facebook_404282094862822').build(),
                        ];
                    },
                },
                gifts: {
                    data() {
                        return [
                            getDefaultGift().name('Gift 1').build(),
                            getDefaultGift().name('Gift 2').build(),
                            getDefaultGift().name('Gift 3').build(),
                        ];
                    },
                },
                clients: {
                    data() {
                        return [
                            getDefaultClient().email('<EMAIL>').build(),
                            getDefaultClient().email('<EMAIL>').build(),
                            getDefaultClient().email('<EMAIL>').build(),
                            getDefaultClient().email('<EMAIL>').build(),
                            getDefaultClient().email('<EMAIL>').build(),
                        ];
                    },
                },
                giftDraws: {
                    data(dependencies) {
                        return [
                            getDefaultGiftDraw()
                                .restaurantId(dependencies.restaurants()[0]._id)
                                .giftId(dependencies.gifts()[0]._id)
                                .clientId(dependencies.clients()[0]._id)
                                .retrievedAt(undefined)
                                .createdAt(tenDaysAgo)
                                .build(),
                            getDefaultGiftDraw()
                                .restaurantId(dependencies.restaurants()[1]._id)
                                .giftId(dependencies.gifts()[2]._id)
                                .clientId(dependencies.clients()[0]._id)
                                .retrievedAt(tenDaysAgo)
                                .createdAt(tenDaysAgo)
                                .build(),
                            getDefaultGiftDraw()
                                .restaurantId(dependencies.restaurants()[0]._id)
                                .giftId(dependencies.gifts()[1]._id)
                                .clientId(dependencies.clients()[1]._id)
                                .retrievedAt(tenDaysAgo)
                                .createdAt(tenDaysAgo)
                                .build(),
                            getDefaultGiftDraw()
                                .restaurantId(dependencies.restaurants()[1]._id)
                                .giftId(dependencies.gifts()[1]._id)
                                .clientId(dependencies.clients()[1]._id)
                                .retrievedAt(tenDaysAgo)
                                .createdAt(tenDaysAgo)
                                .build(),
                            getDefaultGiftDraw()
                                .restaurantId(dependencies.restaurants()[0]._id)
                                .giftId(dependencies.gifts()[0]._id)
                                .clientId(dependencies.clients()[2]._id)
                                .retrievedAt(undefined)
                                .createdAt(tenDaysAgo)
                                .build(),
                            getDefaultGiftDraw()
                                .restaurantId(dependencies.restaurants()[1]._id)
                                .giftId(dependencies.gifts()[0]._id)
                                .clientId(dependencies.clients()[2]._id)
                                .retrievedAt(tenDaysAgo)
                                .createdAt(tenDaysAgo)
                                .build(),
                            getDefaultGiftDraw()
                                .restaurantId(dependencies.restaurants()[0]._id)
                                .giftId(dependencies.gifts()[2]._id)
                                .clientId(dependencies.clients()[3]._id)
                                .retrievedAt(undefined)
                                .createdAt(tenDaysAgo)
                                .build(),
                            getDefaultGiftDraw()
                                .restaurantId(dependencies.restaurants()[1]._id)
                                .giftId(dependencies.gifts()[2]._id)
                                .clientId(dependencies.clients()[4]._id)
                                .retrievedAt(undefined)
                                .createdAt(tenDaysAgo)
                                .build(),
                            getDefaultGiftDraw()
                                .restaurantId(dependencies.restaurants()[0]._id)
                                .giftId(dependencies.gifts()[0]._id)
                                .clientId(dependencies.clients()[0]._id)
                                .retrievedAt(fiveDaysAgo)
                                .createdAt(fiveDaysAgo)
                                .build(),
                            getDefaultGiftDraw()
                                .restaurantId(dependencies.restaurants()[0]._id)
                                .giftId(dependencies.gifts()[1]._id)
                                .clientId(dependencies.clients()[0]._id)
                                .retrievedAt(fiveDaysAgo)
                                .createdAt(fiveDaysAgo)
                                .build(),
                            getDefaultGiftDraw()
                                .restaurantId(dependencies.restaurants()[0]._id)
                                .giftId(dependencies.gifts()[2]._id)
                                .clientId(dependencies.clients()[0]._id)
                                .retrievedAt(undefined)
                                .createdAt(fiveDaysAgo)
                                .build(),
                            getDefaultGiftDraw()
                                .restaurantId(dependencies.restaurants()[0]._id)
                                .giftId(dependencies.gifts()[2]._id)
                                .clientId(dependencies.clients()[0]._id)
                                .retrievedAt(fiveDaysAgo)
                                .createdAt(fiveDaysAgo)
                                .build(),
                            getDefaultGiftDraw()
                                .restaurantId(dependencies.restaurants()[1]._id)
                                .giftId(dependencies.gifts()[0]._id)
                                .clientId(dependencies.clients()[0]._id)
                                .retrievedAt(undefined)
                                .createdAt(fiveDaysAgo)
                                .build(),
                            getDefaultGiftDraw()
                                .restaurantId(dependencies.restaurants()[1]._id)
                                .giftId(dependencies.gifts()[0]._id)
                                .clientId(dependencies.clients()[0]._id)
                                .retrievedAt(fiveDaysAgo)
                                .createdAt(fiveDaysAgo)
                                .build(),
                        ];
                    },
                },
            },
            expectedResult(dependencies): GiftDrawInsightPerGiftDto[] {
                return [
                    {
                        gift: dependencies.gifts[0].name,
                        giftDrawCount: 1,
                        previousGiftDrawCount: 2,
                        retrievedGiftDrawCount: 1,
                        previousRetrievedGiftDrawCount: 0,
                    },
                    {
                        gift: dependencies.gifts[1].name,
                        giftDrawCount: 1,
                        previousGiftDrawCount: 1,
                        retrievedGiftDrawCount: 1,
                        previousRetrievedGiftDrawCount: 1,
                    },
                    {
                        gift: dependencies.gifts[2].name,
                        giftDrawCount: 2,
                        previousGiftDrawCount: 1,
                        retrievedGiftDrawCount: 1,
                        previousRetrievedGiftDrawCount: 0,
                    },
                ];
            },
        });
        await testCase.build();

        const seededObjects = testCase.getSeededObjects();
        const expectedResult = testCase.getExpectedResult();

        const restaurantId = seededObjects.restaurants[0]._id.toString();
        const result = await useCase.execute({ restaurantId, startDate, endDate, comparisonPeriod: MalouComparisonPeriod.PREVIOUS_PERIOD });

        expect(result).toIncludeAllMembers(expectedResult);
    });
});
