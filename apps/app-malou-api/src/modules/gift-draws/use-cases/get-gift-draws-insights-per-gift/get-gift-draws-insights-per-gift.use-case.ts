import { singleton } from 'tsyringe';

import { GiftDrawInsightPerGiftDto } from '@malou-io/package-dto';
import { getDateRangeFromMalouComparisonPeriod, MalouComparisonPeriod, MalouErrorCode } from '@malou-io/package-utils';

import { MalouError } from ':helpers/classes/malou-error';
import { GiftDrawDtoMapper } from ':modules/gift-draws/gift-draws.dto-mapper';
import { GiftDrawsRepository } from ':modules/gift-draws/gift-draws.repository';
import RestaurantsRepository from ':modules/restaurants/restaurants.repository';

@singleton()
export class GetGiftDrawsInsightsPerGiftUseCase {
    constructor(
        private readonly _giftDrawsRepository: GiftDrawsRepository,
        private readonly _giftDrawsDtoMapper: GiftDrawDtoMapper,
        private readonly _restaurantsRepository: RestaurantsRepository
    ) {}

    async execute({
        restaurantId,
        startDate,
        endDate,
        comparisonPeriod,
    }: {
        restaurantId: string;
        startDate: Date;
        endDate: Date;
        comparisonPeriod: MalouComparisonPeriod;
    }): Promise<GiftDrawInsightPerGiftDto[]> {
        const restaurant = await this._restaurantsRepository.getOpeningAndCreationDatesForRestaurant(restaurantId);
        if (!restaurant) {
            throw new MalouError(MalouErrorCode.RESTAURANT_NOT_FOUND, {
                metadata: { restaurantId },
            });
        }
        const { startDate: startPreviousPeriod, endDate: endPreviousPeriod } = this._getComparisonPeriod({
            startDate,
            endDate,
            comparisonPeriod,
            restaurantStartDate: restaurant.createdAt,
        });

        const giftDrawInsightsPerGift = await this._giftDrawsRepository.findGiftDrawsInsightsPerGift({
            restaurantId,
            startPeriod: startDate,
            endPeriod: endDate,
            startPreviousPeriod,
            endPreviousPeriod: endPreviousPeriod,
        });
        return this._giftDrawsDtoMapper.toGiftDrawInsightsPerGiftDto(giftDrawInsightsPerGift);
    }

    private _getComparisonPeriod({
        startDate,
        endDate,
        restaurantStartDate,
        comparisonPeriod,
    }: {
        startDate: Date;
        endDate: Date;
        restaurantStartDate: Date;
        comparisonPeriod: MalouComparisonPeriod;
    }): { startDate: Date; endDate: Date } {
        const period = getDateRangeFromMalouComparisonPeriod({
            dateFilters: { startDate, endDate },
            restaurantStartDate,
            comparisonPeriod,
        });

        if (!period.startDate || !period.endDate) {
            throw new MalouError(MalouErrorCode.INVALID_DATE_RANGE, {
                message: 'Invalid date range for previous period comparison',
                metadata: { startDate, comparisonPeriod },
            });
        }

        return { startDate: period.startDate, endDate: period.endDate };
    }
}
