import { singleton } from 'tsyringe';

import { DoorDashCredentialModel, EntityRepository, IDoorDashCredential } from '@malou-io/package-models';

export const DOORDASH_SUPER_CREDENTIAL_KEY = 'doordash-super-credential';

@singleton()
export default class DoorDashCredentialsRepository extends EntityRepository<IDoorDashCredential> {
    constructor() {
        super(DoorDashCredentialModel);
    }

    async getSuperCredentials(): Promise<IDoorDashCredential[]> {
        return this.find({
            filter: { key: DOORDASH_SUPER_CREDENTIAL_KEY },
            options: { lean: true },
        });
    }
}
