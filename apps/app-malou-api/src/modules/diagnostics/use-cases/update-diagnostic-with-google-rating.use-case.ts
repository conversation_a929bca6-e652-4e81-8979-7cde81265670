import { autoInjectable } from 'tsyringe';

import { DiagnosticDto } from '@malou-io/package-dto';
import { MalouErrorCode } from '@malou-io/package-utils';

import { MalouError } from ':helpers/classes/malou-error';
import DiagnosticsRepository from ':modules/diagnostics/diagnostic.repository';
import PlatformsRepository from ':modules/platforms/platforms.repository';
import { SlackChannel, SlackService } from ':services/slack.service';

@autoInjectable()
export class UpdateDiagnosticWithGoogleRatingUseCase {
    constructor(
        private readonly _platformsRepository: PlatformsRepository,
        private readonly _diagnosticsRepository: DiagnosticsRepository,
        private readonly _slackService: SlackService
    ) {}

    async execute(malouDiagnosticId: string): Promise<DiagnosticDto> {
        const partialDiagnostic = await this._diagnosticsRepository.getDiagnosticById(malouDiagnosticId);

        if (!partialDiagnostic) {
            throw new MalouError(MalouErrorCode.DIAGNOSTIC_NOT_FOUND, {
                message: 'Diagnostic not found',
                metadata: { malouDiagnosticId },
            });
        }

        if (partialDiagnostic.restaurant.rating && partialDiagnostic.restaurant.averageRatingForSimilarRestaurants) {
            return partialDiagnostic.toDto();
        }

        if (!partialDiagnostic.similarRestaurantIds || partialDiagnostic.similarRestaurantIds.length === 0) {
            throw new MalouError(MalouErrorCode.BAD_REQUEST, {
                message: 'No similar restaurants found for this diagnostic',
                metadata: { malouDiagnosticId },
            });
        }

        try {
            const averageRatingWithCount = await this._platformsRepository.getAverageGoogleRatingAndBetterAheadCompetitorsAheadOfRating(
                partialDiagnostic.similarRestaurantIds.map((id) => id.toString()),
                partialDiagnostic.restaurant.rating ?? 0
            );
            const diagnostic = await this._diagnosticsRepository.updateAverageRatingAndBetterCompetitorsCountForSimilarRestaurants(
                partialDiagnostic.id,
                averageRatingWithCount
            );
            if (!diagnostic) {
                throw new MalouError(MalouErrorCode.DIAGNOSTIC_CANNOT_UPDATE_DIAGNOSTIC, {
                    message: 'Cannot update diagnostic',
                    metadata: { malouDiagnosticId },
                });
            }
            return diagnostic.toDto();
        } catch (error) {
            this._slackService.sendAlert({
                channel: SlackChannel.MALOUPE_ALERTS,
                data: {
                    err: error,
                    message: 'Error while updating diagnostic with Google rating',
                    metadata: {
                        malouDiagnosticId,
                    },
                },
            });
            throw error;
        }
    }
}
