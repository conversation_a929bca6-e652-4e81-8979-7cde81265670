import { chunk } from 'lodash';
import { autoInjectable } from 'tsyringe';

import { DiagnosticDto } from '@malou-io/package-dto';
import { MalouErrorCode, PlatformKey } from '@malou-io/package-utils';

import { MalouError } from ':helpers/classes/malou-error';
import DiagnosticsRepository from ':modules/diagnostics/diagnostic.repository';
import { MINIMUM_AVERAGE_REVIEW_COUNT_FOR_SIMILAR_RESTAURANTS } from ':modules/diagnostics/diagnostics.constants';
import { ReviewFiltersInput } from ':modules/reviews/reviews.interfaces';
import { ReviewsRepository } from ':modules/reviews/reviews.repository';
import { SlackChannel, SlackService } from ':services/slack.service';

@autoInjectable()
export class UpdateDiagnosticWithReviewCountForSimilarRestaurantsUseCase {
    constructor(
        private readonly _reviewsRepository: ReviewsRepository,
        private readonly _diagnosticsRepository: DiagnosticsRepository,
        private readonly _slackService: SlackService
    ) {}

    async execute(malouDiagnosticId: string): Promise<DiagnosticDto> {
        const partialDiagnostic = await this._diagnosticsRepository.getDiagnosticById(malouDiagnosticId);

        if (!partialDiagnostic) {
            throw new MalouError(MalouErrorCode.DIAGNOSTIC_NOT_FOUND, {
                message: 'Diagnostic not found',
                metadata: { malouDiagnosticId },
            });
        }

        if (partialDiagnostic.averageReviewCountForSimilarRestaurants) {
            return partialDiagnostic.toDto();
        }

        if (!partialDiagnostic.similarRestaurantIds || partialDiagnostic.similarRestaurantIds.length === 0) {
            throw new MalouError(MalouErrorCode.BAD_REQUEST, {
                message: 'No similar restaurants found for this diagnostic',
                metadata: { malouDiagnosticId },
            });
        }

        try {
            const chunkSize = 10; // Best between performance and in memory size
            // We chunk the similarRestaurantIds to avoid memory issues and to not overload the database with
            // too many requests at once.
            // We can have up to 50 similar restaurants, so we chunk them in groups of 10.
            // This will result in 5 requests to the database at most.
            const similarRestaurantIdsChunks = chunk(partialDiagnostic.similarRestaurantIds, chunkSize);
            const reviewCountPerRestaurantChunks = await Promise.all(
                similarRestaurantIdsChunks.map((ids) =>
                    this._reviewsRepository.getEstimatedPublicReviewCountPerRestaurant(
                        this._getReviewCountFilters(ids.map((id) => id.toString()))
                    )
                )
            );
            const reviewCountPerRestaurant = reviewCountPerRestaurantChunks.flat();

            const top50PerCentRestaurantsBasedOnNumberOfReviews = reviewCountPerRestaurant
                .sort((a, b) => b.reviewCount - a.reviewCount)
                .slice(0, Math.ceil(partialDiagnostic.similarRestaurantIds.length / 2));

            const top50PerCentReviewCount = top50PerCentRestaurantsBasedOnNumberOfReviews.reduce((acc, restaurantReviewCount) => {
                return acc + restaurantReviewCount.reviewCount;
            }, 0);

            const top50PerCentAverageReviewCount = top50PerCentReviewCount / Math.ceil(partialDiagnostic.similarRestaurantIds.length / 2);

            const diagnostic = await this._diagnosticsRepository.updateAverageReviewCountForSimilarRestaurants(
                partialDiagnostic.id,
                Math.max(top50PerCentAverageReviewCount, MINIMUM_AVERAGE_REVIEW_COUNT_FOR_SIMILAR_RESTAURANTS)
            );

            if (!diagnostic?.instagramPage?.name) {
                const updatedDiagnostic = await this._diagnosticsRepository.updateIsDiagnosticComplete(partialDiagnostic.id, true);
                if (!updatedDiagnostic) {
                    throw new MalouError(MalouErrorCode.DIAGNOSTIC_CANNOT_UPDATE_DIAGNOSTIC, {
                        message: 'Cannot update diagnostic',
                        metadata: { malouDiagnosticId },
                    });
                }
                return updatedDiagnostic.toDto();
            }
            return diagnostic.toDto();
        } catch (error) {
            this._slackService.sendAlert({
                channel: SlackChannel.MALOUPE_ALERTS,
                data: {
                    err: error,
                    message: 'Error while updating diagnostic with average review count',
                    metadata: { malouDiagnosticId },
                },
            });
            throw error;
        }
    }

    // Will get the review count regardless of period
    private _getReviewCountFilters(restaurantIds: string[]): ReviewFiltersInput {
        return {
            restaurantIds,
            searchText: '',
            platforms: [PlatformKey.GMB],
            showPrivate: false,
            ratings: [1, 2, 3, 4, 5],
            answered: true,
            notAnswered: true,
            pending: true,
            archived: true,
            unarchived: true,
            withText: true,
            withoutText: false,
            answerable: false,
            privatePlatforms: undefined,
        };
    }
}
