import 'reflect-metadata';

import { container } from 'tsyringe';

import { newDbId } from '@malou-io/package-models';
import { PlatformPresenceStatus } from '@malou-io/package-utils';

import { registerRepositories, TestCaseBuilderV2 } from ':helpers/tests/testing-utils';
import { getDefaultRestaurant } from ':modules/restaurants/tests/restaurant.builder';
import { getDefaultPrivateReview } from ':modules/reviews/tests/private-reviews.builder';
import { getDefaultReview } from ':modules/reviews/tests/reviews.builder';
import { ArchiveReviewsUseCase } from ':modules/reviews/use-cases/archive-reviews/archive-reviews.use-case';

describe('ArchiveReviewsUseCase', () => {
    beforeEach(() => {
        container.reset();
        registerRepositories(['RestaurantsRepository', 'ReviewsRepository', 'PrivateReviewsRepository']);
    });

    describe('execute', () => {
        it('should archive multiple reviews successfully', async () => {
            const archiveReviewsUseCase = container.resolve(ArchiveReviewsUseCase);

            const testCase = new TestCaseBuilderV2<'restaurants' | 'reviews' | 'privateReviews'>({
                seeds: {
                    restaurants: {
                        data() {
                            return [getDefaultRestaurant().build()];
                        },
                    },
                    reviews: {
                        data(dependencies) {
                            return [
                                getDefaultReview()
                                    .platformPresenceStatus(PlatformPresenceStatus.FOUND)
                                    .restaurantId(dependencies.restaurants()[0]._id)
                                    .build(),
                                getDefaultReview()
                                    .platformPresenceStatus(PlatformPresenceStatus.FOUND)
                                    .restaurantId(dependencies.restaurants()[0]._id)
                                    .build(),
                            ];
                        },
                    },
                    privateReviews: {
                        data(dependencies) {
                            return [getDefaultPrivateReview().restaurantId(dependencies.restaurants()[0]._id).build()];
                        },
                    },
                },
                expectedResult: undefined,
            });

            await testCase.build();
            const seeds = testCase.getSeededObjects();

            const reviewIds = seeds.reviews.map((review) => review._id.toString());
            const privateReviewIds = seeds.privateReviews.map((review) => review._id.toString());
            const result = await archiveReviewsUseCase.execute([...reviewIds, ...privateReviewIds]);

            expect(result.archivedCount).toBe(3);
        });

        it('should return 0 archived count when no reviews match the IDs', async () => {
            const archiveReviewsUseCase = container.resolve(ArchiveReviewsUseCase);

            const testCase = new TestCaseBuilderV2<'restaurants' | 'reviews'>({
                seeds: {
                    restaurants: {
                        data() {
                            return [getDefaultRestaurant().build()];
                        },
                    },
                    reviews: {
                        data() {
                            return [];
                        },
                    },
                },
                expectedResult: undefined,
            });

            await testCase.build();

            const nonExistentReviewIds = [newDbId().toString(), newDbId().toString()];
            const result = await archiveReviewsUseCase.execute(nonExistentReviewIds);

            expect(result.archivedCount).toBe(0);
        });
    });
});
