import { singleton } from 'tsyringe';

import { toDbIds } from '@malou-io/package-models';

import { PrivateReviewsRepository } from ':modules/private-reviews/private-reviews.repository';
import { ReviewsRepository } from ':modules/reviews/reviews.repository';

@singleton()
export class ArchiveReviewsUseCase {
    constructor(
        private readonly _reviewsRepository: ReviewsRepository,
        private readonly _privateReviewsRepository: PrivateReviewsRepository
    ) {}

    async execute(reviewIds: string[]): Promise<{ archivedCount: number }> {
        const result = await this._reviewsRepository.updateMany({
            filter: { _id: { $in: toDbIds(reviewIds) } },
            update: { archived: true },
        });

        const privateResult = await this._privateReviewsRepository.updateMany({
            filter: { _id: { $in: toDbIds(reviewIds) } },
            update: { archived: true },
        });

        return { archivedCount: result.modifiedCount + privateResult.modifiedCount };
    }
}
