import { chunk, compact, omit, uniq, uniqBy } from 'lodash';
import { DateTime } from 'luxon';
import { singleton } from 'tsyringe';

import { DbId, IReview, IScan, toDbIds } from '@malou-io/package-models';
import { PlatformKey, WheelOfFortuneRedirectionPlatformKey } from '@malou-io/package-utils';

import { logger } from ':helpers/logger';
import { ReviewsRepository } from ':modules/reviews/reviews.repository';
import ScansRepository from ':modules/scans/repository/scans.repository';
import { ScanWithProjection } from ':modules/scans/scans.interface';
import { WheelOfFortuneSnapshotsRepository } from ':modules/wheel-of-fortune-snapshots/wheel-of-fortune-snapshots.repository';

const PERIOD_BEFORE_BEING_REDIRECTED_IN_MINUTES = 2;
export const PERIOD_BETWEEN_SCAN_AND_REVIEW_TO_MATCH_IN_MINUTES = 60 * 12;

type ReviewWithProjection = Pick<IReview, '_id' | 'restaurantId' | 'socialCreatedAt' | 'key' | 'rating' | 'socialId'>;
type ScanJobBaseFilter = {};

@singleton()
export class MatchReviewsToScansUseCase {
    constructor(
        private readonly _scansRepository: ScansRepository,
        private readonly _reviewsRepository: ReviewsRepository,
        private readonly _wheelOfFortuneSnapshotsRepository: WheelOfFortuneSnapshotsRepository
    ) {}

    async execute(
        period: { minRedirectedAt: Date; maxRedirectedAt: Date },
        minDateToBypassCheckFlag: Date,
        options?: { onlyShowLogs?: boolean; restaurantIds?: string[] }
    ): Promise<void> {
        const minRedirectedAt = period.minRedirectedAt;
        const maxRedirectedAt = period.maxRedirectedAt;

        const scansFilter = this._getScansFilter(minRedirectedAt, maxRedirectedAt, minDateToBypassCheckFlag, options?.restaurantIds);

        logger.info('Min and max redirectedAt and base filter', {
            minRedirectedAt,
            maxRedirectedAt,
            scansFilter,
        });

        const scans: ScanWithProjection[] = await this._getScans(scansFilter);

        if (scans.length === 0) {
            logger.info('No scan found');
            return;
        }

        logger.info('Scans count', { count: scans.length });
        const restaurantIds = uniqBy(
            scans.map((scan) => scan.nfcSnapshot?.restaurantId),
            (id) => id?.toString()
        );
        const alreadyMatchedReviewSocialIds = await this._getAlreadyMatchedReviewSocialIds(scansFilter, restaurantIds);

        const reviews = await this._getReviewsPossiblyMatchingWithScans(
            scans,
            restaurantIds,
            minRedirectedAt,
            maxRedirectedAt,
            alreadyMatchedReviewSocialIds
        );
        const reviewsCount = reviews.length;
        logger.info('Review count', { count: reviewsCount });

        let matchedCount = 0;
        const scansArrayChunked = chunk(scans, 100);
        let scansCheckedCount = 0;
        for (const scansChunked of scansArrayChunked) {
            const bulkOperations: any[] = [];
            for (const scan of scansChunked) {
                const reviewOrUndefined = await this._doesAReviewMatchForAScan(scan, reviews);
                if (reviewOrUndefined) {
                    matchedCount++;
                    reviews.splice(reviews.indexOf(reviewOrUndefined), 1);
                }
                const bulkOperation = this._constructBulkUpdateOperation(scan, reviewOrUndefined);
                bulkOperations.push(bulkOperation);
            }
            scansCheckedCount += scansChunked.length;
            if (options?.onlyShowLogs) {
                logger.info('Bulk operations', { bulkOperations });
            } else {
                logger.info('Scans checked count: ' + scansCheckedCount + '/' + scans.length);
                await this._scansRepository.bulkOperations({ operations: bulkOperations, options: { ordered: false } });
            }
        }
        logger.warn('Matched count', {
            matchedCount,
            reviewsCount,
            total: scans.length,
        });
    }

    private _getScansFilter(
        minRedirectedAt: Date,
        maxRedirectedAt: Date,
        minDateToBypassCheckFlag: Date,
        restaurantIds?: string[]
    ): ScanJobBaseFilter {
        const scansFilter = {
            matchedReviewSocialId: null,
            redirectedAt: { $exists: true, $gte: minRedirectedAt, $lte: maxRedirectedAt },
            $or: [{ isCheckedForMatchingReview: { $ne: true } }, { redirectedAt: { $gte: minDateToBypassCheckFlag } }],
        };
        if (restaurantIds) {
            scansFilter['nfcSnapshot.restaurantId'] = { $in: toDbIds(restaurantIds) };
        }
        return scansFilter;
    }

    private async _getScans(filter: ScanJobBaseFilter): Promise<ScanWithProjection[]> {
        const scans: ScanWithProjection[] = await this._scansRepository.find({
            filter,
            projection: {
                redirectedAt: 1,
                nfcSnapshot: 1,
                starClicked: 1,
                matchedReviewSocialId: 1,
            },
            options: {
                lean: true,
                // Sort scans from newest to oldest to match the most recent ones first
                sort: {
                    redirectedAt: -1,
                },
            },
        });
        return scans;
    }

    private async _getAlreadyMatchedReviewSocialIds(scansFilter: ScanJobBaseFilter, restaurantIds: DbId[]): Promise<string[]> {
        const scans = await this._scansRepository.find({
            filter: {
                ...omit(scansFilter, 'matchedReviewSocialId'),
                'nfcSnapshot.restaurantId': { $in: restaurantIds },
                matchedReviewSocialId: { $ne: null },
            },
            projection: {
                matchedReviewSocialId: 1,
            },
            options: { lean: true },
        });
        return scans.map((scan) => scan.matchedReviewSocialId!);
    }

    private async _getReviewsPossiblyMatchingWithScans(
        scans: ScanWithProjection[],
        restaurantIds: DbId[],
        minRedirectedAt: Date,
        maxRedirectedAt: Date,
        alreadyMatchedReviewSocialIds: string[]
    ): Promise<ReviewWithProjection[]> {
        if (scans.length === 0) {
            return [];
        }

        let minSocialCreatedAt: Date | null = null;
        let maxSocialCreatedAt: Date | null = null;
        let platformKeysFromScans = compact(uniq(scans.map((scan) => scan.nfcSnapshot?.platformKey)));
        if (platformKeysFromScans.includes('wheel_of_fortune')) {
            platformKeysFromScans.push(...Object.values(WheelOfFortuneRedirectionPlatformKey));
        }
        platformKeysFromScans = uniq(platformKeysFromScans);
        platformKeysFromScans.forEach((platformKey) => {
            const { minDateToMatchReview, maxDateToMatchReview } = this._getMinAndMaxDateToMatchReview(platformKey, {
                startDate: minRedirectedAt,
                endDate: maxRedirectedAt,
            });
            if (!minSocialCreatedAt || minDateToMatchReview.getTime() < minSocialCreatedAt.getTime()) {
                minSocialCreatedAt = minDateToMatchReview;
            }
            if (!maxSocialCreatedAt || maxDateToMatchReview.getTime() > maxSocialCreatedAt.getTime()) {
                maxSocialCreatedAt = maxDateToMatchReview;
            }
        });

        const reviewsFilter = {
            socialId: { $nin: alreadyMatchedReviewSocialIds },
            restaurantId: { $in: restaurantIds },
            key: { $in: platformKeysFromScans },
            socialCreatedAt: {
                $gte: minSocialCreatedAt,
                $lte: maxSocialCreatedAt,
            },
        };
        const reviews = await this._reviewsRepository.find({
            filter: reviewsFilter,
            projection: {
                restaurantId: 1,
                socialCreatedAt: 1,
                key: 1,
                rating: 1,
                socialId: 1,
            },
            options: { lean: true },
        });
        return reviews;
    }

    private async _doesAReviewMatchForAScan(
        scan: ScanWithProjection,
        reviews: ReviewWithProjection[]
    ): Promise<ReviewWithProjection | undefined> {
        const { minDateToMatchReview, maxDateToMatchReview } = this._getMinAndMaxDateToMatchReview(scan.nfcSnapshot.platformKey, {
            startDate: scan.redirectedAt!,
            endDate: scan.redirectedAt!,
        });

        let platformKeysToMatch = scan.nfcSnapshot?.platformKey ? [scan.nfcSnapshot.platformKey] : [];

        const isRedirectionFromWheelOfFortune = this._isRedirectingToWheelOfFortune(scan.nfcSnapshot);

        if (isRedirectionFromWheelOfFortune) {
            const wofId = this._extractWheelOfFortuneIdFromRedirectionLink(scan.nfcSnapshot);
            const wofSnapshot = wofId
                ? await this._wheelOfFortuneSnapshotsRepository.findSnapshotByWofIdAndDate(wofId, scan.redirectedAt!)
                : null;
            if (!wofSnapshot || !wofSnapshot.parameters.redirectionSettings!.shouldRedirect) {
                return undefined;
            }
            platformKeysToMatch = wofSnapshot.parameters.redirectionSettings!.platforms.map((platform) => platform.platformKey);
        }

        // We look for the review that matches the scan and is the closest to the scan's date
        const reviewsMatched = reviews.filter((review) => {
            const reviewSocialCreatedAt = this._getCleanSocialCreatedAt(review);

            return (
                review.restaurantId.toString() === scan.nfcSnapshot?.restaurantId?.toString() &&
                platformKeysToMatch.includes(review.key) &&
                (!scan.starClicked || review.rating === scan.starClicked) &&
                reviewSocialCreatedAt >= minDateToMatchReview &&
                reviewSocialCreatedAt <= maxDateToMatchReview
            );
        });
        if (reviewsMatched.length === 0) {
            return undefined;
        }
        const scanRedirectedAtTime = scan.redirectedAt!.getTime();
        let reviewMatched: ReviewWithProjection = reviewsMatched[0];
        let closestDateDifference = Math.abs(reviewMatched.socialCreatedAt.getTime() - scanRedirectedAtTime);
        for (const review of reviewsMatched) {
            const dateDifference = Math.abs(review.socialCreatedAt.getTime() - scanRedirectedAtTime);
            if (dateDifference < closestDateDifference) {
                closestDateDifference = dateDifference;
                reviewMatched = review;
            }
        }
        return reviewMatched;
    }

    private _isRedirectingToWheelOfFortune(nfcSnapshot: IScan['nfcSnapshot']): boolean {
        return !!nfcSnapshot.redirectionLink?.match(/wheel-of-fortune/);
    }

    private _extractWheelOfFortuneIdFromRedirectionLink(nfcSnapshot: IScan['nfcSnapshot']): string | undefined {
        return nfcSnapshot.redirectionLink?.match(/(?<=wofId=).{24}/)?.[0];
    }

    private _constructBulkUpdateOperation(scan: ScanWithProjection, reviewOrUndefined: ReviewWithProjection | undefined) {
        return {
            updateOne: {
                filter: { _id: scan._id },
                update: {
                    $set: {
                        matchedReviewSocialId: reviewOrUndefined?.socialId,
                        isCheckedForMatchingReview: true,
                    },
                },
            },
        };
    }

    private _getMinAndMaxDateToMatchReview(
        platformKey: PlatformKey | 'wheel_of_fortune' | 'no_redirection',
        scansToProcessPeriod: { startDate: Date; endDate: Date }
    ): { minDateToMatchReview: Date; maxDateToMatchReview: Date } {
        switch (platformKey) {
            case PlatformKey.TRIPADVISOR:
                // Tripadvisor reviews have a socialCreatedAt at midnight, so we only check it's the same day as the scan or the next day
                return {
                    minDateToMatchReview: DateTime.fromJSDate(scansToProcessPeriod.startDate).setZone('utc').startOf('day').toJSDate(),
                    maxDateToMatchReview: DateTime.fromJSDate(scansToProcessPeriod.endDate)
                        .plus({ days: 1 })
                        .setZone('utc')
                        .endOf('day')
                        .toJSDate(),
                };
            default:
                return {
                    minDateToMatchReview: DateTime.fromJSDate(scansToProcessPeriod.startDate)
                        .minus({ minutes: PERIOD_BEFORE_BEING_REDIRECTED_IN_MINUTES })
                        .toJSDate(),
                    maxDateToMatchReview: DateTime.fromJSDate(scansToProcessPeriod.endDate)
                        .plus({ minutes: PERIOD_BETWEEN_SCAN_AND_REVIEW_TO_MATCH_IN_MINUTES })
                        .toJSDate(),
                };
        }
    }

    private _getCleanSocialCreatedAt(review: ReviewWithProjection): Date {
        switch (review.key) {
            case PlatformKey.TRIPADVISOR:
                return DateTime.fromJSDate(review.socialCreatedAt).setZone('utc').startOf('day').toJSDate();
            default:
                return review.socialCreatedAt;
        }
    }
}
