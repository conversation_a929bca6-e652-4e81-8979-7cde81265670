import { AxiosRequestHeaders } from 'axios';

import { LowerCaseCountryCode } from '@malou-io/package-utils';

export interface GqlLaFourchetteReview {
    id: string;
    reviewRating: number;
    mealDate: string;
    review?: Review;
    author: Author;
    restaurantReply?: RestaurantReply;
}
export interface GqlLaFourchetteReviewV2 {
    id: string;
    ratingValue: 0 | 0.5 | 1 | 1.5 | 2 | 2.5 | 3 | 3.5 | 4 | 4.5 | 5 | 5.5 | 6 | 6.5 | 7 | 7.5 | 8 | 8.5 | 9 | 9.5 | 10;
    mealDate: string; // "2021-09-25T00:00:00.000Z"
    review?: Review;
    reviewer: {
        id: string;
        avatarUrl: string;
        firstName: string;
        lastName: string;
        reviewCount: number;
    };
    restaurantReply?: RestaurantReply;
}

export type GqlReviewLanguage = LowerCaseCountryCode | 'undetermined' | null;

export interface GqlLaFourchetteReviewV2WithLanguage extends GqlLaFourchetteReviewV2 {
    language: GqlReviewLanguage;
}

export interface Review {
    reviewBody: string;
}

export interface Author {
    id: string;
    avatar: string;
    firstName: string;
    lastName: string;
    reviewCount: number;
}

export interface RestaurantReply {
    body: string;
    status: string;
}

export interface GqlLaFourchetteReviewMapped {
    reviewRating: {
        ratingValue: number;
    };
    reviewBody?: string;
    mealDate: string;
    language: GqlReviewLanguage;
    restaurantReply?: RestaurantReply;
    id: string;
    author: {
        id: string;
        image: string;
        givenName: string;
        familyName: string;
    };
}

export interface ReviewsSummaryRes {
    data: Data;
}

interface Data {
    ratingSummary: RatingSummary;
}

interface RatingSummary {
    reviewCount: number;
    ratingCount: number;
    languageStats: LanguageStats;
}

interface LanguageStats {
    fr: number;
    en: number;
    la: number;
    null: number;
}

export enum WithOrWithoutTheForkReview {
    WITH_REVIEW = 'WITH_REVIEW',
    WITHOUT_REVIEW = 'WITHOUT_REVIEW',
}

export enum TheForkReviewSortDirection {
    DESC = 'DESC',
    ASC = 'ASC',
}

export enum TheForkReviewOrderBy {
    MEAL_DATE = 'MEAL_DATE',
}

export interface ReviewsGqlVariables {
    restaurantId: string;
    sortDirection: TheForkReviewSortDirection;
    orderBy: TheForkReviewOrderBy;
    withReview: WithOrWithoutTheForkReview;
    language: string;
    occasion: null;
    pagination: {
        limit: number;
        offset: number;
    };
}

export interface TheForkReviewGqlBody {
    query: string;
    variables: ReviewsGqlVariables;
}

export type TheForkReviewGqlHeaders = Pick<AxiosRequestHeaders, 'Accept-Language' | 'User-Agent' | 'Accept' | 'Cookie'>;

export interface TheForkReviewGqlRequest {
    operationName: string;
    query: string;
    variables: {
        restaurantId: string;
    };
}
export type TheForkReviewGqlResponse = [
    {
        data: {
            ratingSummary: RatingSummary;
            restaurantRatingsList: {
                ratings: GqlLaFourchetteReviewV2[];
            };
        };
    },
];
