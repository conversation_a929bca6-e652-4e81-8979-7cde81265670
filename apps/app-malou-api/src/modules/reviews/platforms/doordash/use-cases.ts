import assert from 'node:assert/strict';
import { singleton } from 'tsyringe';

import { IPlatform, IReview, IReviewComment } from '@malou-io/package-models';
import { MalouErrorCode, PlatformKey } from '@malou-io/package-utils';

import { MalouError } from ':helpers/classes/malou-error';
import { logger } from ':helpers/logger';
import { Platform } from ':modules/platforms/platforms.entity';
import PlatformsRepository from ':modules/platforms/platforms.repository';
import { DoordashPlatformsUseCases } from ':modules/platforms/platforms/doordash/doordash.use-cases';
import { UpsertPlatformUseCase } from ':modules/platforms/use-cases/upsert-platform/upsert-platform.use-case';
import { ReviewMapper } from ':modules/reviews/reviews.mapper';
import { reviewsFetchCounter } from ':modules/reviews/reviews.metrics';
import { ReviewsRepository } from ':modules/reviews/reviews.repository';
import { PlatformReplyPayload, PlatformReviewsUseCases, ReviewInputWithRestaurantAndPlatformIds } from ':modules/reviews/reviews.types';
import { DoorDashProvider } from ':providers/doordash/doordash.provider';
import { DoorDashReview } from ':providers/doordash/doordash.provider.interface';

@singleton()
export default class DoorDashReviewsUseCases implements PlatformReviewsUseCases {
    constructor(
        private readonly _reviewsRepository: ReviewsRepository,
        private readonly _platformsRepository: PlatformsRepository,
        private readonly _doordashPlatformsUseCases: DoordashPlatformsUseCases,
        private readonly _upsertPlatformUseCase: UpsertPlatformUseCase,
        private readonly _provider: DoorDashProvider
    ) {}

    async getReviewsData({ socialId, platformId }: { socialId?: string; platformId?: string }): Promise<DoorDashReview[]> {
        assert(platformId, 'Missing platformId for Doordash reviews');

        const platform = await this._platformsRepository.getPlatformById(platformId);
        if (!platform) {
            throw new MalouError(MalouErrorCode.PLATFORM_NOT_FOUND, {
                metadata: {
                    platformId,
                    socialId,
                },
            });
        }
        assert(socialId, 'Missing socialId for Doordash reviews');

        if (!platform.doordashBusinessId) {
            const restaurantId = platform.restaurantId.toString();
            const platformData = await this._doordashPlatformsUseCases.getOverviewData({ restaurantId });
            if (platformData) {
                const mappedData = this._doordashPlatformsUseCases.mapOverviewDataToMalou(platformData);
                await this._upsertPlatformUseCase.execute(restaurantId, platform.key, mappedData);
            }
            platform.doordashBusinessId = platformData?.businessId.toString();
        }
        assert(platform.doordashBusinessId, 'Missing doordashBusinessId for Doordash reviews');

        try {
            const reviews = await this._provider.getReviews({ storeId: socialId, businessId: platform.doordashBusinessId });

            reviewsFetchCounter.add(1, {
                source: PlatformKey.DOORDASH,
                status: 'success',
            });
            return reviews.cxReviewsList || [];
        } catch (error) {
            reviewsFetchCounter.add(1, {
                source: PlatformKey.DOORDASH,
                status: 'failure',
            });
            logger.error('Error fetching DoorDash reviews', {
                error,
                socialId,
                platformId,
            });
            return [];
        }
    }

    mapReviewsDataToMalou(platform: IPlatform, reviewsData): ReviewInputWithRestaurantAndPlatformIds[] {
        return reviewsData.map((r) => ReviewMapper.mapToMalouReview(platform, r));
    }

    async reply({
        review: _review,
        comment: _comment,
        platform: _platform,
    }: {
        review: IReview;
        comment: PlatformReplyPayload;
        platform?: Platform;
    }): Promise<any> {
        return;

        // TODO: handle DoorDash answer reply if we manage to get something else than a 403
        // const doordashComment = comment as DoordashReplyPayload;
        // const today = DateTime.now();
        // const reviewDate = review.socialCreatedAt ? DateTime.fromJSDate(review.socialCreatedAt) : null;
        // if (!reviewDate || reviewDate.plus({ days: DOORDASH_DAYS_UNTIL_CANT_BE_ANSWERED }) < today) {
        //     throw new MalouError(MalouErrorCode.REVIEW_TOO_OLD, {
        //         metadata: {
        //             reviewId: review.socialId,
        //             reviewDate: review.socialCreatedAt?.toISOString(),
        //             comment: doordashComment.comment,
        //         },
        //     });
        // }
        // if (!platform) {
        //     throw new MalouError(MalouErrorCode.PLATFORM_NOT_FOUND, {
        //         metadata: {
        //             platformId: review.platformId,
        //             reviewId: review._id,
        //         },
        //     });
        // }
        // assert(platform.doordashBusinessId, 'DoorDash business ID is required for replying to reviews');
        // assert(platform.name, 'DoorDash business name is required for replying to reviews');
        // assert(review.reviewer && review.reviewer.socialId && review.reviewer.doordashConsumerId, 'Review must have a reviewer to reply');
        // assert(review.rating, 'Review must have a rating to reply');

        // const repliedComment = await this._provider.replyReview({
        //     storeId: review.socialId,
        //     storeAddress: platform.address?.formattedAddress ?? '',
        //     businessId: platform.doordashBusinessId,
        //     businessName: platform.name,
        //     replyText: doordashComment.comment,
        //     rating: review.rating,
        //     reviewId: review.socialId,
        //     userId: parseInt(review.reviewer.socialId, 10),
        //     consumerId: review.reviewer.doordashConsumerId, // specific to Doordash, used to reply to reviews
        // });
        // return repliedComment;
    }

    pushReviewComment({ socialId, key, comment }: { socialId: string; key: string; comment: IReviewComment }): Promise<IReview> {
        return this._reviewsRepository.updateUniqueReviewComment({
            socialId,
            key,
            comment,
        });
    }

    updateComment(): void {
        throw new MalouError(MalouErrorCode.NOT_IMPLEMENTED, {
            message: 'DoorDashReviewsUseCases does not implement updateComment !',
        });
    }

    fetchTotalReviewCount(): Promise<number> {
        throw new MalouError(MalouErrorCode.NOT_IMPLEMENTED, {
            message: 'DoorDashReviewsUseCases does not implement fetchTotalReviewCount !',
        });
    }
}
