import assert from 'node:assert/strict';
import { singleton } from 'tsyringe';

import { toDbId } from '@malou-io/package-models';
import { filterByRequiredKeys, getUrlDomain, PlatformKey } from '@malou-io/package-utils';

import { logger } from ':helpers/logger';
import PlatformsRepository from ':modules/platforms/platforms.repository';
import RestaurantsRepository from ':modules/restaurants/restaurants.repository';
import { formatRestaurantAddress } from ':modules/store-locator/shared/utils';
import { GmbPlaceActionsProvider } from ':providers/google/gmb.place-actions.provider';
import { GmbRefreshTokenService } from ':services/credentials/gmb/gmb-refresh-token.service';

@singleton()
export class GetStoreCallToActionsSuggestionsService {
    constructor(
        private readonly _platformsRepository: PlatformsRepository,
        private readonly _gmbRefreshTokenService: GmbRefreshTokenService,
        private readonly _gmbPlaceActionsProvider: GmbPlaceActionsProvider,
        private readonly _restaurantsRepository: RestaurantsRepository
    ) {}

    async execute(restaurantId: string): Promise<Record<string, string>> {
        try {
            const [gmbLinks, restaurant] = await Promise.all([
                this._getLinksFromGmb(restaurantId),
                this._restaurantsRepository.findOne({ filter: { _id: toDbId(restaurantId) }, options: { lean: true } }),
            ]);
            assert(restaurant, 'Restaurant not found');

            const restaurantPlatforms = await this._platformsRepository.getPlatformsByRestaurantId(restaurantId);

            const connectedPlatforms = restaurantPlatforms
                .filter((platform) => !!platform.getSocialLink())
                .reduce(
                    (acc, platform) => {
                        const url = platform.getSocialLink();
                        if (url) {
                            acc[platform.key] = url;
                        }
                        return acc;
                    },
                    {} as Record<string, string>
                );

            const fullAddress = formatRestaurantAddress({ address: restaurant.address, restaurantId });

            const socialNetworksUrls =
                restaurant.socialNetworkUrls?.reduce(
                    (acc, { key, url }) => {
                        if (connectedPlatforms[key]) {
                            connectedPlatforms[key] = url;
                            return acc;
                        }
                        acc[key] = url;
                        return acc;
                    },
                    {} as Record<string, string>
                ) ?? {};

            return {
                ...gmbLinks,
                ...(restaurant.address?.formattedAddress && {
                    itinerary: `https://www.google.com/maps/dir/?api=1&destination=${encodeURIComponent(fullAddress)}`,
                }),
                ...(restaurant.orderUrl && { orderUrl: restaurant.orderUrl }),
                ...(restaurant.reservationUrl && { reservationUrl: restaurant.reservationUrl }),
                ...(restaurant.menuUrl && { menuUrl: restaurant.menuUrl }),
                ...(restaurant.website && { website: restaurant.website }),
                ...connectedPlatforms,
                ...socialNetworksUrls,
            };
        } catch (err) {
            logger.error('[STORE_LOCATOR] [Suggestions] Failed to fetch store call to actions', { err });

            return {};
        }
    }

    private async _getLinksFromGmb(restaurantId: string): Promise<Record<string, string>> {
        const links: Record<string, string> = {};

        try {
            const platform = await this._platformsRepository.getPlatformByRestaurantIdAndPlatformKey(restaurantId, PlatformKey.GMB);
            assert(platform, 'Platform not found for restaurant');

            const { credentials, apiEndpointV2 } = platform;
            const credentialId = credentials?.[0];
            assert(credentialId, 'No credential found for platform');
            assert(apiEndpointV2, 'No API endpoint V2 found for platform');

            const locationId = apiEndpointV2.replace('locations/', '');
            const { accessToken } = await this._gmbRefreshTokenService.getFreshTokenIfNecessary(credentialId);

            const {
                data: { placeActionLinks },
            } = await this._gmbPlaceActionsProvider.listPlaceActionLinks({
                accessToken,
                locationId,
            });

            if (placeActionLinks && placeActionLinks.length > 0) {
                const mappedActionLinks = filterByRequiredKeys(placeActionLinks, ['uri']).map(({ uri }) => ({
                    uri,
                    domain: getUrlDomain(uri),
                }));
                filterByRequiredKeys(mappedActionLinks, ['uri', 'domain']).forEach(({ uri, domain }) => {
                    links[domain] = uri;
                });
            }
        } catch (err) {
            logger.error('[STORE_LOCATOR] Failed to fetch GMB place action links', { err });
        }

        return links;
    }
}
