import { from<PERSON><PERSON><PERSON> } from 'file-type';
import lodash from 'lodash';
import { singleton } from 'tsyringe';

import { GetStoreLocatorStorePageDto } from '@malou-io/package-dto';
import { DbId, IRestaurant, IStoreLocatorRestaurantPage } from '@malou-io/package-models';
import { FacebookApiMediaType, getInstagramUserName, isNotNil, SocialNetworkKey } from '@malou-io/package-utils';

import { fetchImage } from ':helpers/fetch-image-from-remote';
import { logger } from ':helpers/logger';
import { AiMediaDescriptionService } from ':microservices/ai-media-description.service';
import { GenerateMediaDescriptionImageType } from ':modules/ai/interfaces/ai.interfaces';
import {
    InstagramPageDiscoveryService,
    InstagramPagePostsDetailsForStoreLocator,
} from ':modules/diagnostics/services/get-instagram-page/instagram-page-discovery.service';
import { StoreLocatorOrganizationConfiguration } from ':modules/store-locator/entities/store-locator.organization-configuration.entity';
import { RestaurantPopulatedForStoreLocator } from ':modules/store-locator/services/fetch-store-head/interfaces';
import { AwsS3 } from ':plugins/cloud-storage/s3';

export type InstagramProfileData = {
    socialAccount: {
        name: string;
        url: string;
        logoUrl: string;
        followersCount: number;
        publicationsCount: number;
    };
    publications: {
        likesCount?: number;
        image: {
            mediaId?: DbId;
            description: string;
            url: string;
        };
        url: string;
        socialId: string;
        mediaType: FacebookApiMediaType;
        caption?: string;
        mediaCount: number;
        isFirstMediaVideo: boolean;
    }[];
};

@singleton()
export class StoreLocatorInstagramDataService {
    private readonly _MINIMUM_PUBLICATIONS_COUNT = 3;

    constructor(
        private readonly _instagramPageDiscoveryService: InstagramPageDiscoveryService,
        private readonly _cloudStorageService: AwsS3,
        private readonly _aiMediaDescriptionService: AiMediaDescriptionService
    ) {}

    async fetchInstagramDataForRestaurants({
        organizationId,
        restaurants,
    }: {
        organizationId: string;
        restaurants: Pick<IRestaurant, '_id' | 'socialNetworkUrls' | 'name'>[];
    }): Promise<Record<string, InstagramProfileData>> {
        const instagramAccountsIds = lodash.uniq(
            restaurants
                .map((restaurant) => restaurant.socialNetworkUrls?.find(({ key }) => key === SocialNetworkKey.INSTAGRAM)?.url)
                .filter(isNotNil)
                .map((url) => getInstagramUserName(url))
                .filter(isNotNil)
        );

        const instagramData: Record<string, InstagramProfileData> = {};
        await Promise.all(
            instagramAccountsIds.map(async (accountName) => {
                const instaData = await this._fetchInstagramData({ organizationId, accountName });
                if (instaData) {
                    instagramData[accountName] = instaData;
                } else {
                    logger.warn('[STORE_LOCATOR] [Social networks] [Instagram] No data found for account', { accountName });
                }
            })
        );

        return instagramData;
    }

    async mapInstagramDataToSocialNetworksBlockSection({
        restaurant,
        restaurantPage,
        storeLocatorOrganizationConfig,
        instagramData,
    }: {
        restaurant: RestaurantPopulatedForStoreLocator;
        restaurantPage: IStoreLocatorRestaurantPage;
        storeLocatorOrganizationConfig: StoreLocatorOrganizationConfiguration;
        instagramData: InstagramProfileData;
    }): Promise<NonNullable<GetStoreLocatorStorePageDto['socialNetworksBlock']>['socialNetworks']['instagram'] | undefined> {
        if (!instagramData || !instagramData.publications?.length) {
            return undefined;
        }

        await Promise.all(
            instagramData.publications.map(async (publication) => {
                const response = await this._aiMediaDescriptionService.generateMediaDescription({
                    imageLink: publication.image.url,
                    imageType: GenerateMediaDescriptionImageType.ALT_TEXT_SOCIAL_MEDIA_POST,
                    language: restaurantPage.lang,
                    keywords: storeLocatorOrganizationConfig.aiSettings?.keywords?.map(({ text }) => text) ?? [],
                    restaurantName: restaurant.name,
                });

                if (response && response.aiResponse && response.aiResponse.description) {
                    publication.image.description = response.aiResponse.description;
                }
            })
        );

        return {
            publications: instagramData.publications.map(
                ({ caption, image, isFirstMediaVideo, likesCount, url, mediaCount, mediaType }) => ({
                    caption,
                    imageDescription: image.description,
                    imageUrl: image.url,
                    isFirstMediaVideo,
                    url,
                    mediaType,
                    mediaCount,
                    likesCount: this._formatNumberToString(likesCount ?? 0),
                })
            ),
            socialAccount: {
                logoUrl: instagramData.socialAccount.logoUrl,
                name: instagramData.socialAccount.name,
                url: instagramData.socialAccount.url,
                followersCount: this._formatNumberToString(instagramData.socialAccount.followersCount),
                publicationsCount: this._formatNumberToString(instagramData.socialAccount.publicationsCount),
            },
        };
    }

    private async _fetchInstagramData({
        accountName,
        organizationId,
    }: {
        accountName: string;
        organizationId: string;
    }): Promise<InstagramProfileData | undefined> {
        try {
            const insights = await this._instagramPageDiscoveryService.getPagePostsDetailsForStoreLocator({
                accountName,
            });

            if (lodash.isNil(insights)) {
                logger.error('[STORE_LOCATOR] [Social networks] [Instagram] Failed to fetch account data', { accountName });
                return undefined;
            }

            const {
                business_discovery: { media_count: publicationsCount, followers_count: followersCount, profile_picture_url, media },
            } = insights;

            if (media.data.length < this._MINIMUM_PUBLICATIONS_COUNT) {
                logger.error('[STORE_LOCATOR] [Social networks] [Instagram] Not enough publications', { accountName });
                return undefined;
            }

            const baseS3Url = `store-locator/organization/${organizationId}/social-networks/instagram/${accountName}`;
            const [profilePictureUrl, mappedPublications] = await Promise.all([
                this._uploadInstagramProfilePicture({ profilePictureUrl: profile_picture_url, baseS3Url }),
                this._mapInstagramPublications({ media, baseS3Url }),
            ]);

            return {
                socialAccount: {
                    name: `@${accountName}`,
                    url: `https://www.instagram.com/${accountName}`,
                    logoUrl: profilePictureUrl,
                    followersCount,
                    publicationsCount,
                },
                publications: mappedPublications,
            };
        } catch (err) {
            logger.error('[STORE_LOCATOR] [Social networks] [Instagram] Failed to generate block', err);
            return undefined;
        }
    }

    private async _uploadPicture({ pictureUrl, s3Key }: { pictureUrl: string; s3Key: string }): Promise<string | undefined> {
        try {
            const imageBuffer = await fetchImage(pictureUrl);
            if (!imageBuffer) {
                logger.error('[STORE_LOCATOR] [Social networks] [Instagram] Failed to fetch image', {
                    pictureUrl,
                    s3Key,
                });
                return undefined;
            }

            const fileTypeResult = await fromBuffer(imageBuffer);
            if (!fileTypeResult) {
                logger.error('[STORE_LOCATOR] [Social networks] [Instagram] Failed to get file type when uploading picture', {
                    pictureUrl,
                    s3Key,
                });
                return undefined;
            }
            const { ext, mime } = fileTypeResult;

            const uploadedUrl = await this._cloudStorageService.uploadBuffer({
                buffer: imageBuffer,
                fileKey: `${s3Key}.${ext}`,
                mimeType: mime,
            });

            logger.info('[STORE_LOCATOR] [Social networks] [Instagram] Uploaded image to S3', {
                pictureUrl,
                s3Key: `${s3Key}.${ext}`,
            });

            return uploadedUrl;
        } catch (err) {
            logger.error('[STORE_LOCATOR] [Social networks] [Instagram] Failed to upload picture', {
                pictureUrl,
                s3Key,
                err,
            });

            return undefined;
        }
    }

    private async _uploadInstagramProfilePicture({
        profilePictureUrl,
        baseS3Url,
    }: {
        profilePictureUrl: string;
        baseS3Url: string;
    }): Promise<string> {
        try {
            const s3Key = `${baseS3Url}/profile-picture`;
            const uploadedUrl = await this._uploadPicture({ pictureUrl: profilePictureUrl, s3Key });

            if (!uploadedUrl) {
                return profilePictureUrl;
            }

            return uploadedUrl;
        } catch (err) {
            return profilePictureUrl;
        }
    }

    private async _mapInstagramPublications({
        media,
        baseS3Url,
    }: {
        media: InstagramPagePostsDetailsForStoreLocator['business_discovery']['media'];
        baseS3Url: string;
    }): Promise<NonNullable<InstagramProfileData>['publications']> {
        try {
            await this._cloudStorageService.emptyDirectory(`${baseS3Url}/publications`);
        } catch (err) {
            logger.warn('[STORE_LOCATOR] [Social networks block] Failed to empty publications pictures folder', { err });
        }

        return await Promise.all(media.data.map(async (publication) => this._mapInstagramPublication({ publication, baseS3Url })));
    }

    private async _mapInstagramPublication({
        publication,
        baseS3Url,
    }: {
        publication: InstagramPagePostsDetailsForStoreLocator['business_discovery']['media']['data'][0];
        baseS3Url: string;
    }): Promise<NonNullable<InstagramProfileData>['publications'][number]> {
        const maxCarouselPoints = 6;
        const s3Key = `${baseS3Url}/publications/${publication.id}`;

        let pictureUrl = '';
        let isFirstMediaVideo = false;
        let mediaCount = 1;

        if (publication.media_type === FacebookApiMediaType.VIDEO) {
            pictureUrl = publication.thumbnail_url;
            isFirstMediaVideo = true;
        } else if (publication.media_type === FacebookApiMediaType.IMAGE) {
            pictureUrl = publication.media_url;
        } else {
            // CAROUSEL_ALBUM: inspect children
            const firstMedia = publication.children.data[0];
            mediaCount = publication.children.data.length;

            if (firstMedia.media_type === FacebookApiMediaType.VIDEO) {
                pictureUrl = firstMedia.thumbnail_url;
                isFirstMediaVideo = true;
            } else {
                pictureUrl = firstMedia.media_url;
            }
        }

        mediaCount = Math.min(mediaCount, maxCarouselPoints);
        const uploadedImageUrl = await this._uploadPicture({ pictureUrl, s3Key });

        return {
            // Sometimes like_count is not returned by the Facebook Graph API
            ...(publication.like_count && {
                likesCount: publication.like_count,
            }),
            socialId: publication.id,
            url: publication.permalink,
            mediaType: publication.media_type,
            image: {
                description: '',
                url: uploadedImageUrl || pictureUrl,
            },
            caption: publication.caption,
            isFirstMediaVideo,
            mediaCount,
        };
    }

    private _formatNumberToString(number: number): string {
        if (number < 1000) {
            return number.toString();
        }

        if (number < 1000000) {
            return `${(number / 1000).toFixed(1)}k`;
        }

        if (number < 1000000000) {
            return `${(number / 1000000).toFixed(1)}m`;
        }

        return number.toString();
    }
}
