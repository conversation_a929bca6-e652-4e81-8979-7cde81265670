import axios, { AxiosRequestConfig } from 'axios';
import { autoInjectable, inject } from 'tsyringe';
import UserAgent from 'user-agents';

import { ReadPreferenceMode, toDbId } from '@malou-io/package-models';
import {
    AggregationTimeScale,
    errorReplacer,
    FoundStatusOnPlatform,
    isNotNil,
    MalouErrorCode,
    MalouMetric,
    PlatformKey,
    StoredInDBInsightsMetric,
    TimeInMilliseconds,
    TimeInSeconds,
    waitFor,
} from '@malou-io/package-utils';

import { Config } from ':config';
import { MalouError } from ':helpers/classes/malou-error';
import { InjectionToken } from ':helpers/injection';
import { logger } from ':helpers/logger';
import { fetchUntilWorks } from ':microservices/node-crawler';
import { InsightsAggregator } from ':modules/platform-insights/platform-insights.aggregators';
import { platformRatingsFetchCounter } from ':modules/platform-insights/platform-insights.metrics';
import PlatformInsightsRepository from ':modules/platform-insights/platform-insights.repository';
import {
    DailyValue,
    MetricToDataValues,
    PlatformInsightUseCase,
    TimeScaleToMetricToDataValues,
} from ':modules/platform-insights/platform-insights.types';
import PlatformsRepository from ':modules/platforms/platforms.repository';
import { Cache } from ':plugins/cache';

@autoInjectable()
export class LaFourchettePlatformInsights implements PlatformInsightUseCase {
    private readonly _proxy = {
        protocol: 'http',
        host: Config.services.brightData.proxyHost as string,
        port: Config.services.brightData.proxyPort as number,
        auth: {
            username: Config.services.brightData.residentialProxyUsername as string,
            password: Config.services.brightData.residentialProxyPassword as string,
        },
    };

    constructor(
        private _platformsRepository: PlatformsRepository,
        private _platformInsightsRepository: PlatformInsightsRepository,
        @inject(InjectionToken.Cache) private readonly _cache: Cache
    ) {}

    getInsightsAggregated = async (
        restaurantId: string,
        metrics: MalouMetric[],
        aggregators: AggregationTimeScale[],
        filters
    ): Promise<TimeScaleToMetricToDataValues> => {
        let platform;
        try {
            platform = await this._platformsRepository.findOne({
                filter: { restaurantId, key: PlatformKey.LAFOURCHETTE },
                options: { lean: true },
            });
            if (!platform) {
                throw new MalouError(MalouErrorCode.PLATFORM_NOT_FOUND, {
                    metadata: { restaurantId, platformKey: PlatformKey.LAFOURCHETTE },
                });
            }
        } catch (err) {
            logger.warn('[ERROR_LAFOURCHETTE_PLATFORM]', err);
            return { error: true, message: JSON.stringify(err, errorReplacer) };
        }

        const { startDate, endDate } = filters;
        const insightsByDay: MetricToDataValues<DailyValue> = await this._getInsightsByDay(platform.socialId, metrics, startDate, endDate);
        if ('error' in insightsByDay) {
            return insightsByDay;
        }
        return new InsightsAggregator().aggregateInsights(insightsByDay, aggregators, startDate, endDate);
    };

    private _getInsightsByDay = async (
        pageId: string,
        metrics: MalouMetric[],
        startDate: Date,
        endDate: Date
    ): Promise<MetricToDataValues<DailyValue>> => {
        const insightsByDay: MetricToDataValues<DailyValue> = {};

        if (metrics.includes(MalouMetric.PLATFORM_RATING)) {
            insightsByDay[MalouMetric.PLATFORM_RATING] = await this._getPlatformRatingByDay(pageId, startDate, endDate);
        }

        if (!Object.keys(insightsByDay).length) {
            return { error: true, message: MalouErrorCode.INSIGHTS_NOT_FOUND };
        }

        return insightsByDay;
    };

    private _getPlatformRatingByDay = async (pageId: string, startDate: Date, endDate: Date): Promise<DailyValue[]> => {
        const ratingInsights = await this._platformInsightsRepository.find({
            filter: {
                metric: StoredInDBInsightsMetric.PLATFORM_RATING,
                socialId: pageId,
                platformKey: PlatformKey.LAFOURCHETTE,
                createdAt: { $gte: startDate, $lte: endDate },
            },
            options: { sort: { createdAt: -1 }, readPreference: ReadPreferenceMode.SECONDARY_PREFERRED },
        });
        return ratingInsights.map((f) => (f.value ? { date: new Date(f.year, f.month, f.day), value: f.value } : null)).filter(isNotNil);
    };

    fetchTodayRating = async (restaurantId: string): Promise<number | null | undefined> => {
        const platform = await this._platformsRepository.findOne({
            filter: {
                restaurantId: toDbId(restaurantId),
                key: PlatformKey.LAFOURCHETTE,
                foundStatusOnPlatform: { $ne: FoundStatusOnPlatform.NOT_FOUND },
            },
            options: { lean: true },
        });

        if (!platform) {
            throw new MalouError(MalouErrorCode.PLATFORM_NOT_FOUND, {
                metadata: { restaurantId, platformKey: PlatformKey.LAFOURCHETTE },
            });
        }

        try {
            const { cookie } = await this._getDatadomeCookie();
            const userAgent = new UserAgent().toString();

            let lastError: unknown;
            const attemptCount = 2;

            for (let i = 0; i < attemptCount; i++) {
                try {
                    const config: AxiosRequestConfig = {
                        method: 'POST',
                        url: 'https://www.thefork.fr/api/graphql',
                        headers: {
                            'User-Agent': userAgent,
                            'Accept-Language': 'fr-FR',
                            Accept: '*/*',
                            'Content-Type': 'application/json',
                            Host: 'www.thefork.fr',
                            Origin: 'https://www.thefork.fr',
                            Connection: 'keep-alive',
                            Pragma: 'no-cache',
                            Priority: 'u=3, i',
                            Referer: 'https://www.thefork.fr/restaurant/hestia-r819962',
                            'Sec-Fetch-Dest': 'empty',
                            'Sec-Fetch-Mode': 'cors',
                            'Sec-Fetch-Site': 'same-origin',
                            timezone: 'Europe/Paris',
                            'x-caller-name': '',
                            'x-request-id': '',
                            'x-request-id-stack': '',
                            'x-tf-ab-test': 'fake_dhp_top_100=in;revamp_rs=vara;seo_push_loc_near_me=con',
                            'x-thefork-cc': '15102-e75',
                            'x-thefork-product-id': '37',
                            'x-thefork-product-name': 'core-front-browser',
                            Cookie: cookie,
                        },
                        data: {
                            operationName: 'getRPTrackingInfos',
                            variables: {
                                restaurantId: platform?.socialId,
                            },
                            query: 'query getRPTrackingInfos($restaurantId: ID!) { restaurant(restaurantId: $restaurantId) { aggregateRatings { thefork { reviewCount ratingValue  }  } }}',
                        },
                        proxy: this._proxy,
                    };

                    const result = await axios.request(config).then((res) => res.data);
                    if (result.errors?.[0]?.extensions?.code === 'RESTAURANT_NOT_FOUND') {
                        platformRatingsFetchCounter.add(1, {
                            source: PlatformKey.LAFOURCHETTE,
                            status: 'success',
                        }); // Consider success even if restaurant is not found
                        logger.info('[RESTAURANT_CHURNED_FROM_THE_FORK]', { message: 'Restaurant not found', platform });
                        await this._platformsRepository.findOneAndUpdate({
                            filter: { _id: platform._id },
                            update: { foundStatusOnPlatform: FoundStatusOnPlatform.NOT_FOUND },
                        });
                        return;
                    }
                    logger.info('[SUCCESS_FETCHING_LAFOURCHETTE_RATING]', { result });
                    platformRatingsFetchCounter.add(1, {
                        source: PlatformKey.LAFOURCHETTE,
                        status: 'success',
                    });

                    return result?.data?.restaurant?.aggregateRatings?.thefork
                        ? (result.data.restaurant.aggregateRatings.thefork.ratingValue ?? null)
                        : undefined;
                } catch (e: any) {
                    if (e.response?.status === 404 && e?.response?.data?.errors?.[0]?.code === 'RESTAURANT_NOT_FOUND') {
                        platformRatingsFetchCounter.add(1, {
                            source: PlatformKey.LAFOURCHETTE,
                            status: 'success',
                        }); // Consider success even if restaurant is not found
                        logger.info('[RESTAURANT_CHURNED_FROM_THE_FORK]', { message: 'Restaurant not found', platform });
                        await this._platformsRepository.findOneAndUpdate({
                            filter: { _id: platform._id },
                            update: { foundStatusOnPlatform: FoundStatusOnPlatform.NOT_FOUND },
                        });
                        return;
                    }
                    lastError = e;
                    logger.warn('[ERROR_FETCHING_LAFOURCHETTE_RATING_WITH_USER_AGENT]', { userAgent, error: e.message });
                }
            }

            logger.error('[ERROR_FETCHING_LAFOURCHETTE_RATING]', { platform, lastError });
            platformRatingsFetchCounter.add(1, {
                source: PlatformKey.LAFOURCHETTE,
                status: 'failure',
            });
            return;
        } catch (e) {
            logger.error('[LAFOURCHETTE_RATING_UNEXPECTED_ERROR]', { e, platform });
            throw e;
        }
    };

    async insertTodayFollowers() {
        throw new MalouError(MalouErrorCode.NOT_IMPLEMENTED, {
            message: 'Method not implemented',
            metadata: {
                platform: PlatformKey.LAFOURCHETTE,
            },
        });
    }

    private _fetchDatadomeCookie = (): Promise<{ cookie?: string; status?: number } | undefined> => {
        return fetchUntilWorks({
            params: {
                url: 'https://api-js.datadome.co/js/',
                headers: {
                    'Content-type': 'application/x-www-form-urlencoded; charset=utf-8',
                },
                method: 'POST',
                body: {
                    ddk: '4980A61279181687DE605B235F81B9',
                    Referer: 'https%3A%2F%2Fwww.thefork.fr%2F',
                },
                timeout: 5000,
            },
            isResponseValid: (res) => {
                return !!(res?.status === 200 && res?.cookie);
            },
        });
    };

    private _getDatadomeCookie = async (): Promise<{ cookie: string; clearKey: string }> => {
        const getCookieKey = (index: number) => `the-fork-datadome-cookie-ratings-${index}`;
        const cookieKeys = Array.from(Array(10).keys()).map(getCookieKey);

        const getRandomCookieKey = (): string => cookieKeys[Math.floor(Math.random() * cookieKeys.length)];

        const waitForValidCookie = async (cookieObj: { cookie: string; date: string }) => {
            const cookieSetTime = new Date(cookieObj.date).getTime();
            const currentTime = Date.now();
            const elapsed = currentTime - cookieSetTime;
            const waitTime = TimeInMilliseconds.MINUTE - elapsed;

            if (waitTime > 0) {
                await waitFor(waitTime);
            }
        };

        const fetchAndStoreCookie = async (key: string): Promise<string> => {
            const res = await this._fetchDatadomeCookie();
            const cookie = res!.cookie!;
            await this._cache.set(key, JSON.stringify({ cookie, date: new Date().toISOString() }), TimeInSeconds.MINUTE * 120);
            return cookie;
        };

        const cookieKey = getRandomCookieKey();
        const cachedCookieStr = (await this._cache.get(cookieKey)) as string;

        if (!cachedCookieStr?.length) {
            logger.info('[THE_FORK_DATADOME_COOKIE] - No cookie found, fetching a new one');
            const newCookie = await fetchAndStoreCookie(cookieKey);
            await waitFor(TimeInMilliseconds.MINUTE);
            fetchAndStoreCookie(cookieKey).catch((e) => {
                logger.info('[THE_FORK_DATADOME_COOKIE] - Error while fetching a new cookie', e);
            });
            return { cookie: newCookie, clearKey: cookieKey };
        }

        const cachedCookie: { cookie: string; date: string } = JSON.parse(cachedCookieStr);
        await waitForValidCookie(cachedCookie);
        logger.info('[THE_FORK_DATADOME_COOKIE] - Using this dome cookie', cachedCookie.cookie);
        fetchAndStoreCookie(cookieKey).catch((e) => {
            logger.info('[THE_FORK_DATADOME_COOKIE] - Error while fetching a new cookie', e);
        }); // we reset the cookie each time we use it once
        return { cookie: cachedCookie.cookie, clearKey: cookieKey };
    };
}
