import { singleton } from 'tsyringe';
import { v4 } from 'uuid';

import { DownloadInsightsAsPdfResponseDto } from '@malou-io/package-dto';
import { PuppeteerLambdaEvent, PuppeteerLambdaEventName } from '@malou-io/package-service-interfaces';
import { MalouErrorCode } from '@malou-io/package-utils';

import { MalouError } from ':helpers/classes/malou-error';
import { logger } from ':helpers/logger';
import { PuppeteerServiceV2 } from ':microservices/puppeteer-v2.service';
import { DownloadInsightsAsPdfBody } from ':modules/platform-insights/platform-insights.types';

@singleton()
export class DownloadInsightsAsPdfUseCase {
    constructor(private readonly puppeteerServiceV2: PuppeteerServiceV2) {}

    async execute(body: DownloadInsightsAsPdfBody): Promise<DownloadInsightsAsPdfResponseDto> {
        const params: PuppeteerLambdaEvent = {
            eventType: PuppeteerLambdaEventName.INSIGHTS_PDF,
            payload: {
                jwtToken: body.jwtToken,
                baseUrl: process.env.BASE_URL,
                callBackUrl: body.callbackUrl,
                pdfParams: body.params,
                awsBucketName: process.env.AWS_BUCKET,
                awsBucketPath: this._computeBucketPath(body),
            },
        };

        const response = await this.puppeteerServiceV2.generatePdfAndGetS3Url(params);

        if ('error' in response) {
            logger.warn('[DownloadInsightsAsPdfUseCase] Error when calling puppeteer-v2 lambda, throwing error', { error: response.error });
            throw new MalouError(MalouErrorCode.DOWNLOAD_INSIGHTS_AS_PDF_FAILED, { metadata: { error: response.error } });
        }

        return response.data;
    }

    private _computeBucketPath(body: DownloadInsightsAsPdfBody): string {
        const dateISOString = new Date().toISOString();
        let bucketPath = 'insights-pdf';
        if (body.restaurantId) {
            bucketPath += `/${body.restaurantId}`;
        } else {
            bucketPath += `/aggregated/${body.userId}`;
        }
        bucketPath += `/${body.insightTab}-${dateISOString}-${v4()}.pdf`;
        return bucketPath;
    }
}
