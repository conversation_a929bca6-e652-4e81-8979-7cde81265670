import { singleton } from 'tsyringe';

import { getPlatformKeysWithKeywordSearchImpressions } from '@malou-io/package-utils';

import { Config } from ':config';
import { logger } from ':helpers/logger';
import PlatformsRepository from ':modules/platforms/platforms.repository';
import RestaurantsRepository from ':modules/restaurants/restaurants.repository';
import { UseCaseQueueTag } from ':queues/sqs-template/constant';
import { GenericSqsProducer } from ':queues/sqs-template/generic-sqs-producer';

type MessageBody = {
    platformId: string;
};

@singleton()
export class CreateMessageQueuesToPerformMonthlySaveKeywordSearchUseCase extends GenericSqsProducer<MessageBody> {
    constructor(
        private readonly _restaurantRepository: RestaurantsRepository,
        private readonly _platformRepository: PlatformsRepository
    ) {
        super({
            queueUrl: Config.services.sqs.monthlySaveKeywordSearchImpressionsQueueUrl,
            useCaseQueueTag: UseCaseQueueTag.CREATE_MESSAGES_MONTHLY_SAVE_KEYWORD_SEARCH_IMPRESSIONS,
        });
    }

    async execute(): Promise<void> {
        const restaurantIds = await this._restaurantRepository.getAllActiveRestaurantIds();
        const platformsWithKeywordSearchImpressions = getPlatformKeysWithKeywordSearchImpressions();

        const platforms = await this._platformRepository.getPlatformsByRestaurantIdsAndPlatformKeys(
            restaurantIds,
            platformsWithKeywordSearchImpressions
        );

        logger.info(`${this.getLogsSuffix()} Count`, {
            count: platforms.length,
        });

        for (const platform of platforms) {
            await this.sendMessage({ platformId: platform._id.toString() });
        }

        return;
    }
}
