import { NextFunction, Request, Response } from 'express';
import assert from 'node:assert/strict';
import { singleton } from 'tsyringe';

import {
    CreateScanBodyDto,
    createScanBodyValidator,
    GetScanInsightsBodyDto,
    getScanInsightsBodyValidator,
    GetScanInsightsForRestaurantBodyDto,
    getScanInsightsForRestaurantBodyValidator,
    GetScanInsightsForRestaurantParamsDto,
    getScanInsightsForRestaurantParamsValidator,
    PatchScanBodyDto,
    patchScanBodyValidator,
    PatchScanParamsDto,
    patchScanParamsValidator,
    ScanDto,
    ScanForAggregatedInsightsDto,
    ScanForRestaurantInsightsDto,
    SearchScanQueryDto,
    searchScanQueryValidator,
} from '@malou-io/package-dto';
import { ApiResultV2, errorReplacer } from '@malou-io/package-utils';

import { Body, Params, Query } from ':helpers/decorators/validators';
import { logger } from ':helpers/logger';
import { ReviewsDtoMapper } from ':modules/reviews/reviews.mapper.dto';
import { GetScanInsightsForRestaurantUseCase } from ':modules/scans/use-cases/get-scan-insights-for-restaurant/get-scan-insights-for-restaurant.use-case';
import { GetScanInsightsForRestaurantsUseCase } from ':modules/scans/use-cases/get-scan-insights-for-restaurants/get-scan-insights-for-restaurants.use-case';

import { ScansDtoMapper } from './scans.dto-mapper';
import { ScansUseCases } from './scans.use-cases';

@singleton()
export class ScansController {
    constructor(
        private readonly _scansUseCases: ScansUseCases,
        private readonly _scansDtoMapper: ScansDtoMapper,
        private readonly _reviewsDtoMapper: ReviewsDtoMapper,
        private readonly _getScanInsightsForRestaurantsUseCase: GetScanInsightsForRestaurantsUseCase,
        private readonly _getScanInsightsForRestaurantUseCase: GetScanInsightsForRestaurantUseCase
    ) {}

    @Query(searchScanQueryValidator)
    async handleSearchScan(
        req: Request<any, any, any, SearchScanQueryDto>,
        res: Response<ApiResultV2<ScanDto[]>>,
        next: NextFunction
    ): Promise<void> {
        try {
            const scanSearchFilters = this._scansDtoMapper.toScanSearchFilters(req.query);
            const scans = await this._scansUseCases.searchScan(scanSearchFilters);
            const dtos = scans.map((e) => ({
                ...this._scansDtoMapper.toScanDto(e),
                matchedReview: e.matchedReview ? this._reviewsDtoMapper.toReviewResponseDto(e.matchedReview) : undefined,
            }));
            res.json({ data: dtos });
        } catch (err) {
            logger.error('[ERROR_AGGREGATED_STATS] [SCANS]', {
                error: JSON.stringify(err, errorReplacer),
                query: JSON.stringify(req.query),
            });
            next(err);
        }
    }

    @Body(getScanInsightsBodyValidator)
    async handleGetInsightsForRestaurants(
        req: Request<any, any, GetScanInsightsBodyDto>,
        res: Response<ApiResultV2<ScanForAggregatedInsightsDto>>,
        next: NextFunction
    ): Promise<void> {
        try {
            const result = await this._getScanInsightsForRestaurantsUseCase.execute(req.body);
            res.json({ data: result });
        } catch (err) {
            next(err);
        }
    }

    @Params(getScanInsightsForRestaurantParamsValidator)
    @Body(getScanInsightsForRestaurantBodyValidator)
    async handleGetInsightsForRestaurant(
        req: Request<any, any, GetScanInsightsForRestaurantBodyDto, GetScanInsightsForRestaurantParamsDto>,
        res: Response<ApiResultV2<ScanForRestaurantInsightsDto>>,
        next: NextFunction
    ): Promise<void> {
        try {
            const restaurantId = req.params.restaurantId;
            const { endScannedAt, nfcIds, startScannedAt, comparisonPeriod } = req.body;
            const result = await this._getScanInsightsForRestaurantUseCase.execute(restaurantId, {
                endScannedAt,
                nfcIds,
                startScannedAt,
                comparisonPeriod,
            });
            res.json({ data: result });
        } catch (err) {
            next(err);
        }
    }

    @Body(createScanBodyValidator)
    async handleCreateScan(req: Request<any, any, CreateScanBodyDto>, res: Response<ApiResultV2<ScanDto>>, next: NextFunction) {
        try {
            const createScan = this._scansDtoMapper.toCreateScan(req.body);
            const scan = await this._scansUseCases.createScan(createScan);
            const dto = this._scansDtoMapper.toScanDto(scan);
            res.json({ data: dto });
        } catch (err) {
            next(err);
        }
    }

    @Params(patchScanParamsValidator)
    @Body(patchScanBodyValidator)
    async handlePatchScan(
        req: Request<PatchScanParamsDto, any, PatchScanBodyDto>,
        res: Response<ApiResultV2<ScanDto>>,
        next: NextFunction
    ) {
        try {
            const patchScan = this._scansDtoMapper.toPatchScan(req.body);
            const scan = await this._scansUseCases.patchScan(req.params.scanId, patchScan);
            assert(scan, 'Scan not found');
            const dto = this._scansDtoMapper.toScanDto(scan);
            res.json({ data: dto });
        } catch (err) {
            next(err);
        }
    }
}
