import { singleton } from 'tsyringe';

import { GetScanInsightsForRestaurantBodyDto, ScanForRestaurantInsightsDto } from '@malou-io/package-dto';
import { getDateRangeFromMalouComparisonPeriod, MalouComparisonPeriod, MalouErrorCode } from '@malou-io/package-utils';

import { MalouError } from ':helpers/classes/malou-error';
import RestaurantsRepository from ':modules/restaurants/restaurants.repository';
import ScansRepository from ':modules/scans/repository/scans.repository';
import { ScansDtoMapper } from ':modules/scans/scans.dto-mapper';

@singleton()
export class GetScanInsightsForRestaurantUseCase {
    constructor(
        private readonly _scansRepository: ScansRepository,
        private readonly _scansDtoMapper: ScansDtoMapper,
        private readonly _restaurantsRepository: RestaurantsRepository
    ) {}

    async execute(restaurantId: string, params: GetScanInsightsForRestaurantBodyDto): Promise<ScanForRestaurantInsightsDto> {
        const restaurant = await this._restaurantsRepository.getOpeningAndCreationDatesForRestaurant(restaurantId);
        if (!restaurant) {
            throw new MalouError(MalouErrorCode.RESTAURANT_NOT_FOUND, {
                metadata: { restaurantId },
            });
        }

        const { startScannedAt, endScannedAt, comparisonPeriod } = params;

        const { startDate: periodStartScannedAt, endDate: periodEndScannedAt } = this._getPeriod({
            startDate: new Date(startScannedAt),
            endDate: new Date(endScannedAt),
            comparisonPeriod,
            restaurantStartDate: new Date(restaurant.createdAt),
        });

        const promises = [
            this._scansRepository.getScansForRestaurantInsights(restaurantId, {
                ...params,
                startScannedAt: new Date(startScannedAt),
                endScannedAt: new Date(endScannedAt),
            }),
            this._scansRepository.getScansForRestaurantInsights(restaurantId, {
                ...params,
                startScannedAt: periodStartScannedAt,
                endScannedAt: periodEndScannedAt,
            }),
        ];

        const result = await Promise.all(promises);

        const scans = result[0]?.map((s) => this._scansDtoMapper.toScanForRestaurantInsightsDto(s)) ?? [];
        const previousScans = result[1]?.map((s) => this._scansDtoMapper.toScanForRestaurantInsightsDto(s)) ?? [];

        return {
            previousScans,
            scans,
        };
    }

    private _getPeriod({
        startDate,
        endDate,
        restaurantStartDate,
        comparisonPeriod,
    }: {
        startDate: Date;
        endDate: Date;
        restaurantStartDate: Date;
        comparisonPeriod: MalouComparisonPeriod;
    }): { startDate: Date; endDate: Date } {
        const period = getDateRangeFromMalouComparisonPeriod({
            dateFilters: { startDate, endDate },
            restaurantStartDate,
            comparisonPeriod,
        });

        if (!period.startDate || !period.endDate) {
            throw new MalouError(MalouErrorCode.INVALID_DATE_RANGE, {
                message: 'Invalid date range for previous period comparison',
                metadata: { startDate, comparisonPeriod },
            });
        }

        return { startDate: period.startDate, endDate: period.endDate };
    }
}
