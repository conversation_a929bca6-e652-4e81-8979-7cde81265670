import { toDbIds } from '@malou-io/package-models';
import { PlatformPresenceStatus } from '@malou-io/package-utils';

export const lookupMatchedReviewStage = (restaurantIds?: string[]) => {
    const lookupStage = restaurantIds
        ? {
              $lookup: {
                  from: 'reviews',
                  localField: 'matchedReviewSocialId',
                  foreignField: 'socialId',
                  pipeline: [
                      {
                          $match: {
                              restaurantId: { $in: toDbIds(restaurantIds) },
                              platformPresenceStatus: { $ne: PlatformPresenceStatus.NOT_FOUND },
                          },
                      },
                  ],
                  as: 'matchedReview',
              },
          }
        : {
              $lookup: {
                  from: 'reviews',
                  localField: 'matchedReviewSocialId',
                  foreignField: 'socialId',
                  pipeline: [
                      {
                          $match: {
                              platformPresenceStatus: { $ne: PlatformPresenceStatus.NOT_FOUND },
                          },
                      },
                  ],
                  as: 'matchedReview',
              },
          };

    return [
        lookupStage,
        {
            $unwind: { path: '$matchedReview', preserveNullAndEmptyArrays: true },
        },
    ];
};
