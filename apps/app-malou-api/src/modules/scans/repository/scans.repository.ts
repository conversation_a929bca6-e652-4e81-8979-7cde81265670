import { singleton } from 'tsyringe';

import { GetScanInsightsBodyDto } from '@malou-io/package-dto';
import {
    EntityRepository,
    IScan,
    IScanForAggregatedInsights,
    IScanForRestaurantInsightsWithReviewRating,
    ReadPreferenceMode,
    ScanModel,
    toDbId,
    toDbIds,
} from '@malou-io/package-models';

import { lookupMatchedReviewStage } from ':modules/scans/repository/scans.pipelines';

@singleton()
export default class ScansRepository extends EntityRepository<IScan> {
    constructor() {
        super(ScanModel);
    }

    async getScansForAggregatedInsights(params: GetScanInsightsBodyDto): Promise<IScanForAggregatedInsights[]> {
        const { restaurantIds, nfcIds, startScannedAt, endScannedAt } = params;
        return this.find({
            filter: {
                nfcId: { $in: toDbIds(nfcIds) },
                'nfcSnapshot.restaurantId': { $in: toDbIds(restaurantIds) },
                scannedAt: { $gte: new Date(startScannedAt), $lte: new Date(endScannedAt) },
            },
            projection: {
                nfcId: 1,
                matchedReviewSocialId: 1,
                'nfcSnapshot.name': 1,
                'nfcSnapshot.chipName': 1,
                'nfcSnapshot.restaurantId': 1,
                'nfcSnapshot.platformKey': 1,
                'nfcSnapshot.redirectionLink': 1,
            },
            options: {
                lean: true,
                comment: 'getScansForAggregatedInsights',
                readPreference: ReadPreferenceMode.SECONDARY_PREFERRED,
            },
        });
    }

    async getScansForRestaurantInsights(
        restaurantId: string,
        params: {
            nfcIds: string[];
            startScannedAt: Date;
            endScannedAt: Date;
        }
    ): Promise<IScanForRestaurantInsightsWithReviewRating[]> {
        const { nfcIds, startScannedAt, endScannedAt } = params;
        const matchStage = {
            $match: {
                nfcId: { $in: toDbIds(nfcIds) },
                'nfcSnapshot.restaurantId': toDbId(restaurantId),
                scannedAt: { $gte: new Date(startScannedAt), $lte: new Date(endScannedAt) },
            },
        };
        const projectionStage = {
            $project: {
                nfcId: 1,
                matchedReviewSocialId: 1,
                scannedAt: 1,
                starClicked: 1,
                'nfcSnapshot.name': 1,
                'nfcSnapshot.chipName': 1,
                'nfcSnapshot.restaurantId': 1,
                'nfcSnapshot.platformKey': 1,
                'nfcSnapshot.redirectionLink': 1,
            },
        };
        const pipeline = [matchStage, projectionStage, ...lookupMatchedReviewStage([restaurantId])];
        return this.aggregate(pipeline, {
            comment: 'getScansForRestaurantInsights',
            readPreference: ReadPreferenceMode.SECONDARY_PREFERRED,
        });
    }
}
