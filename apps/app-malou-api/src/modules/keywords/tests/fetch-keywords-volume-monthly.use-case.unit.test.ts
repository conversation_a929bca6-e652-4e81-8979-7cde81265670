import { container } from 'tsyringe';

import { DbId } from '@malou-io/package-models';
import { ApplicationLanguage } from '@malou-io/package-utils';
/* eslint-disable-next-line no-restricted-imports */
import * as utils from '@malou-io/package-utils/lib/functions/wait-for';

import { registerRepositories, TestCaseBuilderV2 } from ':helpers/tests/testing-utils';
import { KeywordsTempRepository } from ':modules/keywords/keywords-temp.repository';
import { KeywordsVolumePort } from ':modules/keywords/services/keyword-volume-service/keyword-volume.port';
import { getDefaultKeywordTemp } from ':modules/keywords/tests/keyword.builder';
import { FetchKeywordsVolumeMonthlyUseCase } from ':modules/keywords/use-cases/fetch-keywords-volume-monthly/fetch-keywords-volume-monthly.use-case';
import { AwsScheduler } from ':plugins/scheduler/aws-scheduler';

describe('FetchKeywordsVolumeMonthlyUseCase', () => {
    beforeAll(() => {
        registerRepositories(['KeywordsTempRepository', 'RestaurantKeywordsRepository', 'RestaurantsRepository', 'BricksRepository']);
    });

    describe('execute', () => {
        const defaultVolume1 = 10;
        const defaultVolume2 = 20;
        const defaultVolume3 = 30;

        const apiLocationId1 = '12345';
        const apiLocationId2 = '54321';

        class KeywordsVolumePortMock {
            getKeywordsVolume(keywords: string[], apiLocationId: string) {
                const data = keywords.map((keyword) => {
                    switch (apiLocationId) {
                        case apiLocationId1:
                            return { text: keyword, volume: defaultVolume1 };
                        case apiLocationId2:
                            return { text: keyword, volume: defaultVolume2 };
                        default:
                            return { text: keyword, volume: defaultVolume3 };
                    }
                });

                return Promise.resolve({ data, source: 'mock' });
            }
            getDailyKeywordToolQuotaRemaining() {
                const MAX_CALL_COUNT = 3;
                const MINIMUM_VOLUME_PROVIDER_CALLS_COUNT_FOR_ONBOARDING = 30;
                return MAX_CALL_COUNT + MINIMUM_VOLUME_PROVIDER_CALLS_COUNT_FOR_ONBOARDING;
            }

            getMaxKeywordsPerRequest() {
                const MAX_KEYWORDS_PER_REQUEST = 10;
                return MAX_KEYWORDS_PER_REQUEST;
            }

            getMaxApiCallsPerMinute() {
                const MAX_API_CALLS_PER_MINUTE = 10;
                return MAX_API_CALLS_PER_MINUTE;
            }
        }

        class AwsSchedulerMock {
            scheduleTask(_data: {
                task: string;
                payload: any;
                at: Date;
                target: {
                    url: string;
                    role: string;
                };
                logPrefix?: string;
            }) {
                return;
            }
        }

        container.register(KeywordsVolumePort, { useValue: new KeywordsVolumePortMock() as any });
        container.register(AwsScheduler, { useValue: new AwsSchedulerMock() as any });

        it('should fetch the keywords volume and save them', async () => {
            const fetchKeywordsVolumeMonthlyUseCase = container.resolve(FetchKeywordsVolumeMonthlyUseCase);
            const keywordsRepository = container.resolve(KeywordsTempRepository);

            const testCase = new TestCaseBuilderV2<'keywordsTemp'>({
                seeds: {
                    keywordsTemp: {
                        data() {
                            return [
                                getDefaultKeywordTemp()
                                    .lastVolumeFetchDate(undefined)
                                    .apiLocationId(apiLocationId1)
                                    .text('restaurant groupe')
                                    .build(),
                                getDefaultKeywordTemp()
                                    .lastVolumeFetchDate(undefined)
                                    .apiLocationId(apiLocationId1)
                                    .text('ce texte est ouf')
                                    .build(),
                                getDefaultKeywordTemp()
                                    .lastVolumeFetchDate(undefined)
                                    .apiLocationId(apiLocationId1)
                                    .text('best keyword ever')
                                    .build(),
                                getDefaultKeywordTemp()
                                    .lastVolumeFetchDate(undefined)
                                    .apiLocationId(apiLocationId2)
                                    .text('just wow')
                                    .build(),
                                getDefaultKeywordTemp()
                                    .lastVolumeFetchDate(undefined)
                                    .apiLocationId(apiLocationId2)
                                    .text('happy test')
                                    .build(),
                                getDefaultKeywordTemp()
                                    .lastVolumeFetchDate(undefined)
                                    .apiLocationId(apiLocationId2)
                                    .text('happy dev')
                                    .build(),
                                getDefaultKeywordTemp()
                                    .lastVolumeFetchDate(undefined)
                                    .apiLocationId(apiLocationId2)
                                    .text('happy app')
                                    .build(),
                                getDefaultKeywordTemp()
                                    .lastVolumeFetchDate(undefined)
                                    .apiLocationId(apiLocationId2)
                                    .text('restaurant groupe')
                                    .build(),
                                getDefaultKeywordTemp()
                                    .lastVolumeFetchDate(undefined)
                                    .apiLocationId(apiLocationId2)
                                    .text('best keyword ever')
                                    .build(),
                                getDefaultKeywordTemp()
                                    .lastVolumeFetchDate(undefined)
                                    .apiLocationId('9999999')
                                    .text('best keyword ever')
                                    .build(),
                            ];
                        },
                    },
                },
                expectedResult(dependencies): { _id: DbId; volume: number }[] {
                    return [
                        { _id: dependencies.keywordsTemp[0]._id, volume: defaultVolume1 },
                        { _id: dependencies.keywordsTemp[1]._id, volume: defaultVolume1 },
                        { _id: dependencies.keywordsTemp[2]._id, volume: defaultVolume1 },
                        { _id: dependencies.keywordsTemp[3]._id, volume: defaultVolume2 },
                        { _id: dependencies.keywordsTemp[4]._id, volume: defaultVolume2 },
                        { _id: dependencies.keywordsTemp[5]._id, volume: defaultVolume2 },
                        { _id: dependencies.keywordsTemp[6]._id, volume: defaultVolume2 },
                        { _id: dependencies.keywordsTemp[7]._id, volume: defaultVolume2 },
                        { _id: dependencies.keywordsTemp[8]._id, volume: defaultVolume2 },
                        { _id: dependencies.keywordsTemp[9]._id, volume: defaultVolume3 },
                    ];
                },
            });
            await testCase.build();

            const expectedResult = testCase.getExpectedResult();
            jest.spyOn(utils, 'waitFor').mockImplementation(() => Promise.resolve(1));

            await fetchKeywordsVolumeMonthlyUseCase.execute();

            const keywords = await keywordsRepository.find({ filter: {}, options: { lean: true } });
            const result = keywords.map((keyword) => ({ _id: keyword._id, volume: keyword.volume }));

            expect(result).toIncludeSameMembers(expectedResult);
        });

        it('should fetch the keywords volume and save them for keywords with same apiLocationId and text but different language', async () => {
            const fetchKeywordsVolumeMonthlyUseCase = container.resolve(FetchKeywordsVolumeMonthlyUseCase);
            const keywordsRepository = container.resolve(KeywordsTempRepository);

            const keywordText = 'restaurant';

            const testCase = new TestCaseBuilderV2<'keywordsTemp'>({
                seeds: {
                    keywordsTemp: {
                        data() {
                            return [
                                getDefaultKeywordTemp()
                                    .apiLocationId(apiLocationId1)
                                    .text(keywordText)
                                    .lastVolumeFetchDate(undefined)
                                    .volume(0)
                                    .language(ApplicationLanguage.EN)
                                    .build(),
                                getDefaultKeywordTemp()
                                    .apiLocationId(apiLocationId1)
                                    .text(keywordText)
                                    .lastVolumeFetchDate(undefined)
                                    .volume(0)
                                    .language(ApplicationLanguage.FR)
                                    .build(),
                            ];
                        },
                    },
                },
                expectedResult(dependencies): { _id: DbId; volume: number }[] {
                    return dependencies.keywordsTemp.map((keyword) => {
                        return { _id: keyword._id, volume: defaultVolume1 };
                    });
                },
            });
            await testCase.build();

            const expectedResult = testCase.getExpectedResult();
            jest.spyOn(utils, 'waitFor').mockImplementation(() => Promise.resolve(1));

            await fetchKeywordsVolumeMonthlyUseCase.execute();

            const keywords = await keywordsRepository.find({ filter: {}, options: { lean: true } });
            const result = keywords.map((keyword) => ({ _id: keyword._id, volume: keyword.volume }));

            expect(result).toIncludeSameMembers(expectedResult);
        });

        it('should only call the provider MAX_CALL_COUNT times', async () => {
            const fetchKeywordsVolumeMonthlyUseCase = container.resolve(FetchKeywordsVolumeMonthlyUseCase);
            const keywordsRepository = container.resolve(KeywordsTempRepository);

            const testCase = new TestCaseBuilderV2<'keywordsTemp'>({
                seeds: {
                    keywordsTemp: {
                        data() {
                            return [
                                getDefaultKeywordTemp()
                                    .lastVolumeFetchDate(undefined)
                                    .apiLocationId(apiLocationId1)
                                    .text('restaurant groupe')
                                    .build(),
                                getDefaultKeywordTemp()
                                    .lastVolumeFetchDate(undefined)
                                    .apiLocationId(apiLocationId1)
                                    .text('ce texte est ouf')
                                    .build(),
                                getDefaultKeywordTemp()
                                    .lastVolumeFetchDate(undefined)
                                    .apiLocationId(apiLocationId1)
                                    .text('best keyword ever')
                                    .build(),
                                getDefaultKeywordTemp()
                                    .lastVolumeFetchDate(undefined)
                                    .apiLocationId(apiLocationId2)
                                    .text('just wow')
                                    .build(),
                                getDefaultKeywordTemp()
                                    .lastVolumeFetchDate(undefined)
                                    .apiLocationId(apiLocationId2)
                                    .text('happy test')
                                    .build(),
                                getDefaultKeywordTemp()
                                    .lastVolumeFetchDate(undefined)
                                    .apiLocationId(apiLocationId2)
                                    .text('happy dev')
                                    .build(),
                                getDefaultKeywordTemp()
                                    .lastVolumeFetchDate(undefined)
                                    .apiLocationId(apiLocationId2)
                                    .text('happy app')
                                    .build(),
                                getDefaultKeywordTemp()
                                    .lastVolumeFetchDate(undefined)
                                    .apiLocationId(apiLocationId2)
                                    .text('restaurant groupe')
                                    .build(),
                                getDefaultKeywordTemp()
                                    .lastVolumeFetchDate(undefined)
                                    .apiLocationId(apiLocationId2)
                                    .text('best keyword ever')
                                    .build(),
                                getDefaultKeywordTemp()
                                    .lastVolumeFetchDate(undefined)
                                    .apiLocationId('9999999')
                                    .text('best keyword ever')
                                    .build(),
                                getDefaultKeywordTemp()
                                    .lastVolumeFetchDate(undefined)
                                    .apiLocationId('9999999')
                                    .text('best keyword ever 2')
                                    .build(),
                                getDefaultKeywordTemp()
                                    .lastVolumeFetchDate(undefined)
                                    .apiLocationId('7777777')
                                    .volume(0)
                                    .text('best keyword ever')
                                    .build(),
                            ];
                        },
                    },
                },
                expectedResult(dependencies): { _id: DbId; volume: number }[] {
                    return [
                        { _id: dependencies.keywordsTemp[0]._id, volume: defaultVolume1 },
                        { _id: dependencies.keywordsTemp[1]._id, volume: defaultVolume1 },
                        { _id: dependencies.keywordsTemp[2]._id, volume: defaultVolume1 },
                        { _id: dependencies.keywordsTemp[3]._id, volume: defaultVolume2 },
                        { _id: dependencies.keywordsTemp[4]._id, volume: defaultVolume2 },
                        { _id: dependencies.keywordsTemp[5]._id, volume: defaultVolume2 },
                        { _id: dependencies.keywordsTemp[6]._id, volume: defaultVolume2 },
                        { _id: dependencies.keywordsTemp[7]._id, volume: defaultVolume2 },
                        { _id: dependencies.keywordsTemp[8]._id, volume: defaultVolume2 },
                        { _id: dependencies.keywordsTemp[9]._id, volume: defaultVolume3 },
                        { _id: dependencies.keywordsTemp[10]._id, volume: defaultVolume3 },
                        { _id: dependencies.keywordsTemp[11]._id, volume: 0 },
                    ];
                },
            });
            await testCase.build();

            const expectedResult = testCase.getExpectedResult();
            jest.spyOn(utils, 'waitFor').mockImplementation(() => Promise.resolve(1));

            await fetchKeywordsVolumeMonthlyUseCase.execute();

            const keywords = await keywordsRepository.find({ filter: {}, options: { lean: true } });
            const result = keywords.map((keyword) => ({ _id: keyword._id, volume: keyword.volume }));

            expect(result).toIncludeSameMembers(expectedResult);
        });

        it('should call the provider multiple times according to the max keywords per call', async () => {
            const fetchKeywordsVolumeMonthlyUseCase = container.resolve(FetchKeywordsVolumeMonthlyUseCase);
            const keywordsRepository = container.resolve(KeywordsTempRepository);

            const testCase = new TestCaseBuilderV2<'keywordsTemp'>({
                seeds: {
                    keywordsTemp: {
                        data() {
                            return [
                                getDefaultKeywordTemp()
                                    .lastVolumeFetchDate(undefined)
                                    .apiLocationId(apiLocationId1)
                                    .text('restaurant groupe')
                                    .build(),
                                getDefaultKeywordTemp()
                                    .lastVolumeFetchDate(undefined)
                                    .apiLocationId(apiLocationId1)
                                    .text('ce texte est ouf')
                                    .build(),
                                getDefaultKeywordTemp()
                                    .lastVolumeFetchDate(undefined)
                                    .apiLocationId(apiLocationId1)
                                    .text('best keyword ever')
                                    .build(),
                                getDefaultKeywordTemp()
                                    .lastVolumeFetchDate(undefined)
                                    .apiLocationId(apiLocationId2)
                                    .text('just wow')
                                    .build(),
                                getDefaultKeywordTemp()
                                    .lastVolumeFetchDate(undefined)
                                    .apiLocationId(apiLocationId2)
                                    .text('happy test')
                                    .build(),
                                getDefaultKeywordTemp()
                                    .lastVolumeFetchDate(undefined)
                                    .apiLocationId(apiLocationId2)
                                    .text('happy dev')
                                    .build(),
                                getDefaultKeywordTemp()
                                    .lastVolumeFetchDate(undefined)
                                    .apiLocationId(apiLocationId2)
                                    .text('happy app')
                                    .build(),
                                getDefaultKeywordTemp()
                                    .lastVolumeFetchDate(undefined)
                                    .apiLocationId(apiLocationId2)
                                    .text('restaurant groupe')
                                    .build(),
                                getDefaultKeywordTemp()
                                    .lastVolumeFetchDate(undefined)
                                    .apiLocationId(apiLocationId2)
                                    .text('best keyword ever')
                                    .build(),
                                getDefaultKeywordTemp()
                                    .lastVolumeFetchDate(undefined)
                                    .apiLocationId(apiLocationId2)
                                    .text('bar tranquille')
                                    .build(),
                                getDefaultKeywordTemp()
                                    .lastVolumeFetchDate(undefined)
                                    .apiLocationId(apiLocationId2)
                                    .text('jeux olympiques')
                                    .build(),
                                getDefaultKeywordTemp()
                                    .lastVolumeFetchDate(undefined)
                                    .apiLocationId(apiLocationId2)
                                    .text('bar a tapas')
                                    .build(),
                                getDefaultKeywordTemp()
                                    .lastVolumeFetchDate(undefined)
                                    .apiLocationId(apiLocationId2)
                                    .text('restaurant chic')
                                    .build(),
                                getDefaultKeywordTemp()
                                    .lastVolumeFetchDate(undefined)
                                    .apiLocationId(apiLocationId2)
                                    .text('a lot of keywords')
                                    .build(),
                                getDefaultKeywordTemp()
                                    .lastVolumeFetchDate(undefined)
                                    .apiLocationId(apiLocationId2)
                                    .text('like really a lot')
                                    .build(),
                                getDefaultKeywordTemp()
                                    .lastVolumeFetchDate(undefined)
                                    .apiLocationId('9999999')
                                    .volume(0)
                                    .text('best keyword ever')
                                    .build(),
                                getDefaultKeywordTemp()
                                    .lastVolumeFetchDate(undefined)
                                    .apiLocationId('7777777')
                                    .volume(0)
                                    .text('best keyword ever')
                                    .build(),
                            ];
                        },
                    },
                },
                expectedResult(dependencies): { _id: DbId; volume: number }[] {
                    return [
                        { _id: dependencies.keywordsTemp[0]._id, volume: defaultVolume1 },
                        { _id: dependencies.keywordsTemp[1]._id, volume: defaultVolume1 },
                        { _id: dependencies.keywordsTemp[2]._id, volume: defaultVolume1 },
                        { _id: dependencies.keywordsTemp[3]._id, volume: defaultVolume2 },
                        { _id: dependencies.keywordsTemp[4]._id, volume: defaultVolume2 },
                        { _id: dependencies.keywordsTemp[5]._id, volume: defaultVolume2 },
                        { _id: dependencies.keywordsTemp[6]._id, volume: defaultVolume2 },
                        { _id: dependencies.keywordsTemp[7]._id, volume: defaultVolume2 },
                        { _id: dependencies.keywordsTemp[8]._id, volume: defaultVolume2 },
                        { _id: dependencies.keywordsTemp[9]._id, volume: defaultVolume2 },
                        { _id: dependencies.keywordsTemp[10]._id, volume: defaultVolume2 },
                        { _id: dependencies.keywordsTemp[11]._id, volume: defaultVolume2 },
                        { _id: dependencies.keywordsTemp[12]._id, volume: defaultVolume2 },
                        { _id: dependencies.keywordsTemp[13]._id, volume: defaultVolume2 },
                        { _id: dependencies.keywordsTemp[14]._id, volume: defaultVolume2 },
                        { _id: dependencies.keywordsTemp[15]._id, volume: 0 },
                        { _id: dependencies.keywordsTemp[16]._id, volume: 0 },
                    ];
                },
            });
            await testCase.build();

            const expectedResult = testCase.getExpectedResult();
            jest.spyOn(utils, 'waitFor').mockImplementation(() => Promise.resolve(1));

            await fetchKeywordsVolumeMonthlyUseCase.execute();

            const keywords = await keywordsRepository.find({ filter: {}, options: { lean: true } });
            const result = keywords.map((keyword) => ({ _id: keyword._id, volume: keyword.volume }));

            expect(result).toIncludeSameMembers(expectedResult);
        });

        it('should not program next fetch if all keywords are updated', async () => {
            const fetchKeywordsVolumeMonthlyUseCase = container.resolve(FetchKeywordsVolumeMonthlyUseCase);
            const awsScheduler = container.resolve(AwsScheduler);
            const scheduleTaskSpy = jest.spyOn(awsScheduler, 'scheduleTask');

            const testCase = new TestCaseBuilderV2<'keywordsTemp'>({
                seeds: {
                    keywordsTemp: {
                        data() {
                            return [];
                        },
                    },
                },
                expectedResult(): void {
                    return;
                },
            });
            await testCase.build();

            await fetchKeywordsVolumeMonthlyUseCase.execute();

            expect(scheduleTaskSpy).not.toHaveBeenCalled();
        });

        it('should program next fetch if all keywords are not updated', async () => {
            const fetchKeywordsVolumeMonthlyUseCase = container.resolve(FetchKeywordsVolumeMonthlyUseCase);
            const awsScheduler = container.resolve(AwsScheduler);
            const scheduleTaskSpy = jest.spyOn(awsScheduler, 'scheduleTask');

            const testCase = new TestCaseBuilderV2<'keywordsTemp'>({
                seeds: {
                    keywordsTemp: {
                        data() {
                            return [
                                getDefaultKeywordTemp()
                                    .lastVolumeFetchDate(undefined)
                                    .apiLocationId(apiLocationId1)
                                    .text('restaurant groupe')
                                    .build(),
                                getDefaultKeywordTemp()
                                    .lastVolumeFetchDate(undefined)
                                    .apiLocationId(apiLocationId1)
                                    .text('ce texte est ouf')
                                    .build(),
                                getDefaultKeywordTemp()
                                    .lastVolumeFetchDate(undefined)
                                    .apiLocationId(apiLocationId1)
                                    .text('best keyword ever')
                                    .build(),
                                getDefaultKeywordTemp()
                                    .lastVolumeFetchDate(undefined)
                                    .apiLocationId(apiLocationId2)
                                    .text('just wow')
                                    .build(),
                                getDefaultKeywordTemp()
                                    .lastVolumeFetchDate(undefined)
                                    .apiLocationId(apiLocationId2)
                                    .text('happy test')
                                    .build(),
                                getDefaultKeywordTemp()
                                    .lastVolumeFetchDate(undefined)
                                    .apiLocationId(apiLocationId2)
                                    .text('happy dev')
                                    .build(),
                                getDefaultKeywordTemp()
                                    .lastVolumeFetchDate(undefined)
                                    .apiLocationId(apiLocationId2)
                                    .text('happy app')
                                    .build(),
                                getDefaultKeywordTemp()
                                    .lastVolumeFetchDate(undefined)
                                    .apiLocationId(apiLocationId2)
                                    .text('restaurant groupe')
                                    .build(),
                                getDefaultKeywordTemp()
                                    .lastVolumeFetchDate(undefined)
                                    .apiLocationId(apiLocationId2)
                                    .text('best keyword ever')
                                    .build(),
                                getDefaultKeywordTemp()
                                    .lastVolumeFetchDate(undefined)
                                    .apiLocationId(apiLocationId2)
                                    .text('bar tranquille')
                                    .build(),
                                getDefaultKeywordTemp()
                                    .lastVolumeFetchDate(undefined)
                                    .apiLocationId(apiLocationId2)
                                    .text('jeux olympiques')
                                    .build(),
                                getDefaultKeywordTemp()
                                    .lastVolumeFetchDate(undefined)
                                    .apiLocationId(apiLocationId2)
                                    .text('bar a tapas')
                                    .build(),
                                getDefaultKeywordTemp()
                                    .lastVolumeFetchDate(undefined)
                                    .apiLocationId(apiLocationId2)
                                    .text('restaurant chic')
                                    .build(),
                                getDefaultKeywordTemp()
                                    .lastVolumeFetchDate(undefined)
                                    .apiLocationId(apiLocationId2)
                                    .text('a lot of keywords')
                                    .build(),
                                getDefaultKeywordTemp()
                                    .lastVolumeFetchDate(undefined)
                                    .apiLocationId(apiLocationId2)
                                    .text('like really a lot')
                                    .build(),
                                getDefaultKeywordTemp()
                                    .lastVolumeFetchDate(undefined)
                                    .apiLocationId('9999999')
                                    .volume(0)
                                    .text('best keyword ever')
                                    .build(),
                                getDefaultKeywordTemp()
                                    .lastVolumeFetchDate(undefined)
                                    .apiLocationId('7777777')
                                    .volume(0)
                                    .text('best keyword ever')
                                    .build(),
                            ];
                        },
                    },
                },
                expectedResult(): void {
                    return;
                },
            });
            await testCase.build();
            jest.spyOn(utils, 'waitFor').mockImplementation(() => Promise.resolve(1));

            await fetchKeywordsVolumeMonthlyUseCase.execute();

            expect(scheduleTaskSpy).toHaveBeenCalled();
        });
    });
});
