import { singleton } from 'tsyringe';

import { toDbId } from '@malou-io/package-models';
import { isNotNil, PlatformKey } from '@malou-io/package-utils';

import { logger } from ':helpers/logger';
import { CommentsRepository } from ':modules/comments/comments.repository';
import { CommentMappingService } from ':modules/comments/comments.service';
import { mapCommentsDataToMalou as fbMapCommentsDataToMalou } from ':modules/comments/platforms/facebook/facebook.use-cases';
import { MediasUseCases } from ':modules/media/medias.use-cases';
import { CreateCommentNotificationProducer } from ':modules/notifications/queues/create-comment-notification/create-comment-notification.producer';
import PlatformInsightsRepository from ':modules/platform-insights/platform-insights.repository';
import { Platform } from ':modules/platforms/platforms.entity';
import PlatformsRepository from ':modules/platforms/platforms.repository';
import { UpsertPlatformPostInsightsService } from ':modules/post-insights/v2/services/upsert-platform-post-insights/upsert-platform-post-insights.service';
import { FbPostData } from ':modules/posts/platforms/facebook/facebook-post.interface';
import { FacebookPostService } from ':modules/posts/platforms/facebook/facebook-post.service';
import { FacebookPostsUseCases } from ':modules/posts/platforms/facebook/use-cases';
import PostsRepository from ':modules/posts/posts.repository';
import PostsUseCases from ':modules/posts/posts.use-cases';
import RestaurantsRepository from ':modules/restaurants/restaurants.repository';
import { webhookValueValidator } from ':modules/webhooks/platforms/facebook/posts/use-cases/handle-post-and-comments.use-case.interface';
import { SlackChannel, SlackService } from ':services/slack.service';

// https://developers.facebook.com/docs/graph-api/webhooks/getting-started/webhooks-for-instagram
@singleton()
export class FacebookWebhookPostAndCommentsHandlerUseCase {
    constructor(
        private readonly _facebookPostsUseCases: FacebookPostsUseCases,
        private readonly _mediasUseCases: MediasUseCases,
        private readonly _postsUseCases: PostsUseCases,
        private readonly _commentsRepository: CommentsRepository,
        private readonly _platformInsightsRepository: PlatformInsightsRepository,
        private readonly _platformsRepository: PlatformsRepository,
        private readonly _postsRepository: PostsRepository,
        private readonly _restaurantsRepository: RestaurantsRepository,
        private readonly _createCommentNotificationProducer: CreateCommentNotificationProducer,
        private readonly _commentsMappingService: CommentMappingService,
        private readonly _facebookPostService: FacebookPostService,
        private readonly _slackService: SlackService,
        private readonly _upsertPlatformPostInsightsService: UpsertPlatformPostInsightsService
    ) {}

    async execute(webhookValue: any, pageId: string): Promise<string> {
        try {
            const isWebhookValueValid = await webhookValueValidator.safeParseAsync(webhookValue);
            if (!isWebhookValueValid.success) {
                logger.error('[WEBHOOK_HANDLE_POST_ERROR][FACEBOOK] - Invalid webhook value', {
                    error: isWebhookValueValid.error,
                    webhookValue,
                });
                this._sendSlackAlertForInvalidWebhookValue(webhookValue, pageId);
                return 'invalid webhook value';
            }

            const parsedWebhookValue = isWebhookValueValid.data;

            if (!('post_id' in parsedWebhookValue) || !parsedWebhookValue.post_id || parsedWebhookValue.verb === 'unblock') {
                logger.info('[WEBHOOK_HANDLE_POST_ERROR][FACEBOOK] - Nothing to do for this event', { webhookValue, pageId });
                return 'nothing todo';
            }

            const postIdToFetch = parsedWebhookValue.post_id;

            const platforms = await this._platformsRepository.getPlatformsBySocialIdAndPlatformKey(pageId, PlatformKey.FACEBOOK);
            const inactiveRestaurantIds = await this._restaurantsRepository.getInactiveRestaurantIds();

            const platformsForActiveRestaurants = platforms.filter(
                (platform) => !inactiveRestaurantIds.includes(platform.restaurantId.toString())
            );

            if (platformsForActiveRestaurants.length === 0) {
                logger.error('[WEBHOOK_HANDLE_POST_ERROR][FACEBOOK] - No platform found for active restaurants', { postIdToFetch, pageId });
                return 'no platform found for active restaurants';
            }

            const isPostRemoved = parsedWebhookValue.item === 'post' && parsedWebhookValue.verb === 'remove';
            if (isPostRemoved) {
                for (const platform of platformsForActiveRestaurants) {
                    await this._handlePostRemoved(postIdToFetch, platform._id.toString(), platform.restaurantId.toString());
                }
                return 'post removed';
            }

            const fbPost = await this._facebookPostService.fetchPostFromSocialId(postIdToFetch, pageId, platformsForActiveRestaurants);

            if (!fbPost) {
                logger.error('[WEBHOOK_HANDLE_POST_ERROR][FACEBOOK] - No post found', { postIdToFetch, pageId });
                return 'no post found';
            }

            if (isNotNil(fbPost.story)) {
                logger.info('[WEBHOOK_HANDLE_POST][FACEBOOK] - Post is a story, nothing to do', { postIdToFetch, pageId });
                return 'story';
            }

            for (const platform of platformsForActiveRestaurants) {
                const isPhotoOrVideoOrPostOrStatus = ['photo', 'video', 'post', 'status'].includes(parsedWebhookValue.item);
                if (isPhotoOrVideoOrPostOrStatus) {
                    await this._upsertPost(fbPost, platform);
                }

                const isShareOrReactionOrComment = ['share', 'reaction', 'comment'].includes(parsedWebhookValue.item);
                if (isShareOrReactionOrComment) {
                    await this._upsertPlatformPostInsightsService.upsertFbPostInsights({ posts: [fbPost], platformSocialId: pageId });
                }

                const isComment = parsedWebhookValue.item === 'comment';
                if (isComment) {
                    await this._upsertComments(fbPost, platform, parsedWebhookValue.comment_id);

                    const restaurantId = platform.restaurantId;
                    if (restaurantId)
                        await this._restaurantsRepository.findOneAndUpdate({
                            filter: { _id: restaurantId },
                            update: { commentsLastUpdate: new Date() },
                        });
                }
            }

            return 'ok';
        } catch (error) {
            logger.error('[WEBHOOK_HANDLE_POST_ERROR][FACEBOOK] - Error handling post', { error, webhookValue, pageId });
            this._sendSlackAlertForError(error, webhookValue, pageId);
            return 'error handling post';
        }
    }

    private _sendSlackAlertForInvalidWebhookValue(webhookValue: any, pageId: string): void {
        const stringifiedWebhookValue = JSON.stringify(webhookValue);
        const maxCharactersDisplayed = 300;
        const errorDisplayed =
            stringifiedWebhookValue.length > maxCharactersDisplayed
                ? stringifiedWebhookValue.slice(0, maxCharactersDisplayed) + '...'
                : stringifiedWebhookValue;
        const line1 = `Invalid webhook value received for page: ${pageId}`;
        const line2 = `\`\`\`${errorDisplayed}\`\`\``;
        const text = `${line1}\n${line2}`;
        this._slackService.sendMessage({ text, channel: SlackChannel.POSTS_V2_ALERTS, shouldPing: true });
    }

    private _sendSlackAlertForError(error: any, webhookValue: any, pageId: string): void {
        this._slackService.sendAlert({ data: { error, webhookValue, pageId }, channel: SlackChannel.POSTS_V2_ALERTS, shouldPing: true });
    }

    private async _upsertPost(fbPost: FbPostData, platform: Platform): Promise<void> {
        const malouPostData = this._facebookPostsUseCases.mapPostDataToMalou(fbPost, platform);
        await this._postsUseCases.upsertPostFromFacebookWebhook(malouPostData);
    }

    private async _upsertComments(fbPost: FbPostData, platform: Platform, commentId: string): Promise<void> {
        const platformId = platform._id.toString();
        const mappedComments = fbMapCommentsDataToMalou([fbPost], platform);

        const innerPromises = mappedComments.map(async (mappedComment) => {
            const commentUpdated = await this._commentsMappingService.getMappedCommentWithMalouInformations(mappedComment, platformId);
            return this._commentsRepository.upsert({
                filter: {
                    socialId: mappedComment.socialId,
                    platformId: platform._id,
                },
                update: commentUpdated,
                options: { lean: true },
            });
        });

        const upsertedComments = await Promise.all(innerPromises);

        const webhookUpsertedComment = upsertedComments.filter(isNotNil).find((comment) => comment.socialId === commentId);

        if (webhookUpsertedComment?._id) {
            await this._createCommentNotificationProducer.execute({ commentId: webhookUpsertedComment._id.toString() });
        }
    }

    private async _handlePostRemoved(postSocialId: string, platformId: string, restaurantId: string): Promise<void> {
        await Promise.all([
            // Delete insights
            this._platformInsightsRepository.deleteMany({
                filter: {
                    socialId: postSocialId,
                    platformKey: PlatformKey.FACEBOOK,
                    platformId: toDbId(platformId),
                },
            }),
            // Delete comments
            this._commentsRepository.deleteMany({
                filter: {
                    postSocialId: postSocialId,
                    platformKey: PlatformKey.FACEBOOK,
                    restaurantId: toDbId(restaurantId),
                },
            }),
        ]);

        // Delete post
        const posts = await this._postsRepository.find({
            filter: {
                socialId: postSocialId,
                platformKey: PlatformKey.FACEBOOK,
                platformId: toDbId(platformId),
            },
        });

        await Promise.all([
            ...posts.map(async (post) => {
                await this._mediasUseCases.updateMediaPostIds(post._id, { $pull: { postIds: post._id } });
            }),
            this._postsRepository.deleteMany({
                filter: {
                    socialId: postSocialId,
                    platformKey: PlatformKey.FACEBOOK,
                    platformId: toDbId(platformId),
                },
            }),
        ]);
    }
}
