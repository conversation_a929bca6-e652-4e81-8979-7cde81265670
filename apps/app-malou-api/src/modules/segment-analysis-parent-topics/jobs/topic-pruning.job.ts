import { singleton } from 'tsyringe';

import { AUTO_REPLY_MAX_FAILED_ATTEMPTS, errorReplacer, MalouErrorCode } from '@malou-io/package-utils';

import { defaultComputeRetryDelayInMinutes, GenericJobDefinition } from ':agenda-jobs/job-template/generic-job-definition';
import { MalouError } from ':helpers/classes/malou-error';
import { AgendaJobName } from ':helpers/enums/agenda-job-name.enum';
import RestaurantsRepository from ':modules/restaurants/restaurants.repository';
import { PruneParentTopicsUseCase } from ':modules/segment-analysis-parent-topics/use-cases/prune-parent-topics/prune-parent-topics.use-case';
import { SlackChannel, SlackService } from ':services/slack.service';

@singleton()
export class TopicPruningJob extends GenericJobDefinition {
    constructor(
        private readonly _restaurantsRepository: RestaurantsRepository,
        private readonly _slackService: SlackService,
        private readonly _pruneParentTopicsUseCase: PruneParentTopicsUseCase
    ) {
        super({
            agendaJobName: AgendaJobName.TOPIC_PRUNING,
            shouldDeleteJobOnSuccess: true,
            retryStrategy: {
                maxAttemptsCount: AUTO_REPLY_MAX_FAILED_ATTEMPTS,
                executeAfterMaxAttemptsCount: async (err: Error) => {
                    return this._sendSlackAlertForPruningTopics(err);
                },
                computeRetryDelayInMinutes: defaultComputeRetryDelayInMinutes,
            },
        });
    }

    async executeJob(): Promise<void> {
        const activeRestaurantIds = await this._restaurantsRepository.getAllActiveRestaurantIds();
        for (const restaurantId of activeRestaurantIds) {
            await this._pruneParentTopicsUseCase.execute(restaurantId, new Date());
        }
    }

    async _sendSlackAlertForPruningTopics(error: Error): Promise<void> {
        this._slackService.sendAlert({
            channel: SlackChannel.REVIEWS_ALERTS,
            data: {
                err: new MalouError(MalouErrorCode.SEGMENT_ANALYSIS_TOPIC_PRUNING_ERROR, {
                    message: error.message ?? JSON.stringify(error, errorReplacer),
                    metadata: { rawError: error },
                }),
                metadata: {
                    description: '[TOPIC_PRUNING] An error occurred while pruning topics.',
                },
            },
        });
    }
}
