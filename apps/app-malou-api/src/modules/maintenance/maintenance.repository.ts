import { singleton } from 'tsyringe';

import { EntityRepository, IMaintenance, MaintenanceModel } from '@malou-io/package-models';
import { MaintenanceStatus, trespassUsers } from '@malou-io/package-utils';

@singleton()
export default class MaintenanceRepository extends EntityRepository<IMaintenance> {
    constructor() {
        super(MaintenanceModel);
    }

    insertFirst = () => this.create({ data: { status: MaintenanceStatus.UP, until: new Date(), trespass: trespassUsers } });
}
