import { chunk } from 'lodash';
import { singleton } from 'tsyringe';

import { Config } from ':config';
import { logger } from ':helpers/logger';
import RestaurantsRepository from ':modules/restaurants/restaurants.repository';
import { SynchronizeStoriesUseCase } from ':modules/stories/use-cases/synchronize-stories/synchronize-stories.use-case';
import { UseCaseQueueTag } from ':queues/sqs-template/constant';
import { GenericSqsConsumer } from ':queues/sqs-template/generic-sqs-consumer';

/*
    development: https://eu-west-3.console.aws.amazon.com/scheduler/home?region=eu-west-3#schedules/default/synchronizeStoriesDevelopment
    staging: https://eu-west-3.console.aws.amazon.com/scheduler/home?region=eu-west-3#schedules/default/synchronizeStoriesStaging
    production: https://eu-west-3.console.aws.amazon.com/scheduler/home?region=eu-west-3#schedules/default/synchronizeStoriesProduction
*/

@singleton()
export class SynchronizeStoriesConsumer extends GenericSqsConsumer {
    private readonly CHUNK_SIZE = 3;

    constructor(
        private readonly _restaurantRepository: RestaurantsRepository,
        private readonly _synchronizeStoriesUseCase: SynchronizeStoriesUseCase
    ) {
        super({
            queueUrl: Config.services.sqs.synchronizeStoriesQueueUrl,
            useCaseQueueTag: UseCaseQueueTag.SYNCHRONIZE_STORIES,
            shouldAwaitExecution: false,
        });
    }

    async handleMessage(): Promise<void> {
        const allRestaurantIds = await this._restaurantRepository.getAllActiveRestaurantIds();

        logger.info('[SynchronizeStoriesConsumer]  started - restaurants to process', { restaurantIdsLength: allRestaurantIds.length });
        const restaurantIdsChunks = chunk(allRestaurantIds, this.CHUNK_SIZE);

        for (const restaurantIds of restaurantIdsChunks) {
            await Promise.all(
                restaurantIds.map((restaurantId) =>
                    this._synchronizeStoriesUseCase.execute({ restaurantId }).catch((err) => {
                        logger.error('[SynchronizeStoriesConsumer] Error while synchronizing stories for restaurant', {
                            error: err,
                            restaurantId,
                        });
                    })
                )
            );
        }
    }
}
