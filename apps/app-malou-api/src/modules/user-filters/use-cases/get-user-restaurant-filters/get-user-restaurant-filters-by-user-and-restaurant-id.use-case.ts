import assert from 'node:assert/strict';
import { autoInjectable } from 'tsyringe';

import { UserRestaurantFiltersResponseDto } from '@malou-io/package-dto';
import { getPlatformKeysWithReview, getPlatformKeysWithRSStats, MalouErrorCode, PlatformKey } from '@malou-io/package-utils';

import { MalouError } from ':helpers/classes/malou-error';
import PlatformsRepository from ':modules/platforms/platforms.repository';
import {
    initializeUserRestaurantReviewsFilters,
    initializeUserRestaurantStatisticsFilters,
} from ':modules/user-filters/entities/user-filters-initializer';
import UserFiltersRepository from ':modules/user-filters/repositories/user-filters.repository';

@autoInjectable()
export class GetUserRestaurantFiltersByUserIdAndRestaurantIdUseCase {
    constructor(
        private readonly _userFiltersRepository: UserFiltersRepository,
        private readonly _platformsRepository: PlatformsRepository
    ) {}

    async execute({ userId, restaurantId }: { userId: string; restaurantId: string }): Promise<UserRestaurantFiltersResponseDto> {
        const userFilters = await this._userFiltersRepository.getUserFiltersByUserId(userId);
        if (!userFilters) {
            throw new MalouError(MalouErrorCode.NOT_FOUND, { message: 'User filter not yet created' });
        }
        const userStatisticsFiltersPerRestaurant = userFilters.statisticsFiltersPerRestaurant.find(
            (filter) => filter.restaurantId === restaurantId
        );
        const userReviewsFiltersPerRestaurant = userFilters.reviewsFiltersPerRestaurant.find(
            (filter) => filter.restaurantId === restaurantId
        );

        if (!userStatisticsFiltersPerRestaurant && !userReviewsFiltersPerRestaurant) {
            const defaultPlatformsForRestaurant = await this._platformsRepository.getPlatformsByRestaurantId(restaurantId);
            const defaultPlatformKeysForReviewPart = defaultPlatformsForRestaurant
                .map((platform) => platform.key)
                .filter((key) => getPlatformKeysWithReview().includes(key))
                .concat(PlatformKey.PRIVATE);
            const defaultPlatformKeysForSocialPart = defaultPlatformsForRestaurant
                .map((platform) => platform.key)
                .filter((key) => getPlatformKeysWithRSStats().includes(key));
            const newUserRestaurantReviewsFilters = initializeUserRestaurantReviewsFilters();
            const newUserRestaurantStatisticsFilters = initializeUserRestaurantStatisticsFilters(
                defaultPlatformKeysForReviewPart,
                defaultPlatformKeysForSocialPart
            );
            await this._userFiltersRepository.updateFiltersPerRestaurant({
                userId,
                restaurantId,
                newUserRestaurantStatisticsFilters,
                newUserRestaurantReviewsFilters,
            });
            // These values are set in initializeUserRestaurantStatisticsFilters
            assert(newUserRestaurantStatisticsFilters.dates.startDate);
            assert(newUserRestaurantStatisticsFilters.dates.endDate);
            return {
                id: userFilters.id,
                restaurantId,
                statisticsFilters: {
                    ...newUserRestaurantStatisticsFilters,
                    dates: {
                        startDate: newUserRestaurantStatisticsFilters.dates.startDate.toISOString(),
                        endDate: newUserRestaurantStatisticsFilters.dates.endDate.toISOString(),
                        period: newUserRestaurantStatisticsFilters.dates.period,
                    },
                },
                reviewsFilters: newUserRestaurantReviewsFilters,
            };
        }

        return userFilters.toUserRestaurantFiltersDTO(restaurantId);
    }
}
