import assert from 'node:assert/strict';
import { container } from 'tsyringe';

import { newDbId } from '@malou-io/package-models';
import { MalouErrorCode, PlatformKey } from '@malou-io/package-utils';

import { registerRepositories, TestCaseBuilderV2 } from ':helpers/tests/testing-utils';
import { getDefaultPlatform } from ':modules/platforms/tests/platform.builder';
import { MOCKED_NOW_DATE_TIME } from ':modules/roi/tests/roi.constants';
import UserFiltersRepository from ':modules/user-filters/repositories/user-filters.repository';
import {
    getDefaultUserFilters,
    getDefaultUserRestaurantReviewsFilters,
    getDefaultUserRestaurantStatisticsFilters,
} from ':modules/user-filters/tests/user-filters.builder';
import { GetUserRestaurantFiltersByUserIdAndRestaurantIdUseCase } from ':modules/user-filters/use-cases/get-user-restaurant-filters/get-user-restaurant-filters-by-user-and-restaurant-id.use-case';

describe('GetUserRestaurantFiltersByUserIdAndRestaurantIdUseCase', () => {
    beforeAll(() => {
        const mockDateNow = jest.fn(() => MOCKED_NOW_DATE_TIME);
        global.Date.now = mockDateNow;

        registerRepositories(['UserFiltersRepository', 'PlatformsRepository']);
    });

    it('should throw an error if user filters are not found', async () => {
        const testCases = new TestCaseBuilderV2<'userFilters'>({
            seeds: {
                userFilters: {
                    data() {
                        return [getDefaultUserFilters().build()];
                    },
                },
            },
            expectedResult: () => [],
            expectedErrorCode: MalouErrorCode.NOT_FOUND,
        });
        await testCases.build();

        const getUserRestaurantFiltersByUserIdAndRestaurantIdUseCase = container.resolve(
            GetUserRestaurantFiltersByUserIdAndRestaurantIdUseCase
        );

        const userId = newDbId().toString();
        const restaurantId = newDbId().toString();
        await expect(getUserRestaurantFiltersByUserIdAndRestaurantIdUseCase.execute({ userId, restaurantId })).rejects.toThrow(
            expect.objectContaining({
                malouErrorCode: testCases.getExpectedErrorCode(),
            })
        );
    });

    it('should create user restaurant statistics filters if not found', async () => {
        const userId = newDbId();
        const restaurantId = newDbId();
        const testCases = new TestCaseBuilderV2<'userFilters' | 'platforms'>({
            seeds: {
                userFilters: {
                    data() {
                        return [getDefaultUserFilters().userId(userId).build()];
                    },
                },
                platforms: {
                    data() {
                        return [
                            getDefaultPlatform().restaurantId(restaurantId).key(PlatformKey.GMB).build(),
                            getDefaultPlatform().restaurantId(restaurantId).key(PlatformKey.FACEBOOK).build(),
                            getDefaultPlatform().restaurantId(restaurantId).key(PlatformKey.INSTAGRAM).build(),
                            getDefaultPlatform().restaurantId(restaurantId).key(PlatformKey.TRIPADVISOR).build(),
                        ];
                    },
                },
            },
            expectedResult: () => [],
        });

        await testCases.build();

        const getUserRestaurantFiltersByUserIdAndRestaurantIdUseCase = container.resolve(
            GetUserRestaurantFiltersByUserIdAndRestaurantIdUseCase
        );
        const userFiltersRepository = container.resolve(UserFiltersRepository);

        await getUserRestaurantFiltersByUserIdAndRestaurantIdUseCase.execute({
            userId: userId.toString(),
            restaurantId: restaurantId.toString(),
        });

        const userFilters = await userFiltersRepository.getUserFiltersByUserId(userId.toString());
        assert(userFilters);

        expect(userFilters.statisticsFiltersPerRestaurant).toHaveLength(1);
        const restaurantStatisticsFilters = userFilters.statisticsFiltersPerRestaurant[0];
        expect(restaurantStatisticsFilters.restaurantId.toString()).toEqual(restaurantId.toString());

        // Verify that platforms are properly set in the filters
        const expectedEReputationPlatforms = [PlatformKey.GMB, PlatformKey.FACEBOOK, PlatformKey.TRIPADVISOR, PlatformKey.PRIVATE];
        const expectedSocialNetworksPlatforms = [PlatformKey.FACEBOOK, PlatformKey.INSTAGRAM];

        expect(restaurantStatisticsFilters.filters.platforms.E_REPUTATION).toEqual(expect.arrayContaining(expectedEReputationPlatforms));
        expect(restaurantStatisticsFilters.filters.platforms.SOCIAL_NETWORKS).toEqual(
            expect.arrayContaining(expectedSocialNetworksPlatforms)
        );

        // Verify the platforms are exactly what we expect (no extra platforms)
        expect(restaurantStatisticsFilters.filters.platforms.E_REPUTATION).toHaveLength(expectedEReputationPlatforms.length);
        expect(restaurantStatisticsFilters.filters.platforms.SOCIAL_NETWORKS).toHaveLength(expectedSocialNetworksPlatforms.length);
    });

    it('should return user restaurant statistics and reviews filters', async () => {
        const userId = newDbId();
        const restaurantId = newDbId();
        const testCases = new TestCaseBuilderV2<'userFilters'>({
            seeds: {
                userFilters: {
                    data() {
                        return [
                            getDefaultUserFilters()
                                .userId(userId)
                                .statisticsPerRestaurant([getDefaultUserRestaurantStatisticsFilters().restaurantId(restaurantId).build()])
                                .reviewsPerRestaurant([getDefaultUserRestaurantReviewsFilters().restaurantId(restaurantId).build()])
                                .build(),
                        ];
                    },
                },
            },
            expectedResult: (dependencies) => {
                const restaurantStatisticsFilters: any = getDefaultUserRestaurantStatisticsFilters()
                    .restaurantId(restaurantId)
                    .build().filters;
                restaurantStatisticsFilters.dates = {
                    startDate: restaurantStatisticsFilters.dates.startDate ?? null,
                    endDate: restaurantStatisticsFilters.dates.endDate ?? null,
                    period: restaurantStatisticsFilters.dates.period,
                };

                const restaurantReviewsFilters: any = getDefaultUserRestaurantReviewsFilters().build().filters;
                return {
                    id: dependencies.userFilters[0]._id.toString(),
                    restaurantId: restaurantId.toString(),
                    statisticsFilters: restaurantStatisticsFilters,
                    reviewsFilters: restaurantReviewsFilters,
                };
            },
        });
        await testCases.build();

        const getUserRestaurantFiltersByUserIdAndRestaurantIdUseCase = container.resolve(
            GetUserRestaurantFiltersByUserIdAndRestaurantIdUseCase
        );
        const userFiltersRepository = container.resolve(UserFiltersRepository);

        const data = await getUserRestaurantFiltersByUserIdAndRestaurantIdUseCase.execute({
            userId: userId.toString(),
            restaurantId: restaurantId.toString(),
        });

        const expectedResult = await testCases.getExpectedResult();
        expect(data).toEqual(expectedResult);

        const userFilters = await userFiltersRepository.getUserFiltersByUserId(userId.toString());
        expect(userFilters?.statisticsFiltersPerRestaurant).toHaveLength(1);
        expect(userFilters?.reviewsFiltersPerRestaurant).toHaveLength(1);
    });

    it('should return the correct user restaurant statistics and reviews filters', async () => {
        const userId = newDbId();
        const restaurantId = newDbId();
        const testCases = new TestCaseBuilderV2<'userFilters'>({
            seeds: {
                userFilters: {
                    data() {
                        return [
                            getDefaultUserFilters()
                                .userId(userId)
                                .statisticsPerRestaurant([
                                    getDefaultUserRestaurantStatisticsFilters().restaurantId(restaurantId).build(),
                                    getDefaultUserRestaurantStatisticsFilters().build(),
                                    getDefaultUserRestaurantStatisticsFilters().build(),
                                ])
                                .reviewsPerRestaurant([
                                    getDefaultUserRestaurantReviewsFilters().restaurantId(restaurantId).build(),
                                    getDefaultUserRestaurantReviewsFilters().build(),
                                    getDefaultUserRestaurantReviewsFilters().build(),
                                ])
                                .build(),
                        ];
                    },
                },
            },
            expectedResult: (dependencies) => {
                const restaurantStatisticsFilters: any = getDefaultUserRestaurantStatisticsFilters()
                    .restaurantId(restaurantId)
                    .build().filters;
                restaurantStatisticsFilters.dates = {
                    startDate: restaurantStatisticsFilters.dates.startDate ?? null,
                    endDate: restaurantStatisticsFilters.dates.endDate ?? null,
                    period: restaurantStatisticsFilters.dates.period,
                };

                const restaurantReviewsFilters: any = getDefaultUserRestaurantReviewsFilters().build().filters;
                return {
                    id: dependencies.userFilters[0]._id.toString(),
                    restaurantId: restaurantId.toString(),
                    statisticsFilters: restaurantStatisticsFilters,
                    reviewsFilters: restaurantReviewsFilters,
                };
            },
        });

        await testCases.build();

        const getUserRestaurantFiltersByUserIdAndRestaurantIdUseCase = container.resolve(
            GetUserRestaurantFiltersByUserIdAndRestaurantIdUseCase
        );
        const userFiltersRepository = container.resolve(UserFiltersRepository);

        const data = await getUserRestaurantFiltersByUserIdAndRestaurantIdUseCase.execute({
            userId: userId.toString(),
            restaurantId: restaurantId.toString(),
        });

        const expectedResult = await testCases.getExpectedResult();
        expect(data).toEqual(expectedResult);

        const userFilters = await userFiltersRepository.getUserFiltersByUserId(userId.toString());
        expect(userFilters?.statisticsFiltersPerRestaurant).toHaveLength(3);
        expect(userFilters?.reviewsFiltersPerRestaurant).toHaveLength(3);
    });
});
