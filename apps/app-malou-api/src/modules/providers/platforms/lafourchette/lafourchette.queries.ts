import { Config } from ':config';
import { TheForkReviewGqlHeaders } from ':modules/reviews/platforms/lafourchette/reviews.interface';

import { datadomeCookies } from './datadome';

export const getTheForkGqlRequestBody = (operationName: string, query: string, variables: any) => ({
    operationName,
    variables,
    query,
});

export function getReviewsGqlHeaders(): TheForkReviewGqlHeaders {
    return {
        'User-Agent':
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/123.0.0.0 Safari/537.36',
        'Accept-Language': 'fr-FR',
        Accept: '*/*',
        Cookie: datadomeCookies[Math.floor(Math.random() * datadomeCookies.length)],
    };
}

export function getTheForkApiUrl({ useProxy }: { useProxy: boolean }) {
    return process.env.SCRAPPER_PROXY_TOKEN && useProxy
        ? `${Config.vendors.scrapperProxy.baseUrl + process.env.SCRAPPER_PROXY_TOKEN}&url=${
              Config.platforms.lafourchette.api.url
          }/api/graphql`
        : `${Config.platforms.lafourchette.api.url}/api/graphql`;
}

export const getAutocompleteSuggestionsVariables = (searchText: string) => ({
    query: {
        abTest: [{ type: 'EMPTY_STATE', value: 1 }],
        text: searchText,
        types: ['RESTAURANT_TAG', 'RESTAURANT', 'SALE_TYPE_TAG', 'SHORTCUT'],
    },
});

export const AUTOCOMPLETE_SUGGESTIONS_OP_NAME = 'getWhatAutocompleteSuggestions';
export const AUTOCOMPLETE_SUGGESTIONS_QUERY = `query getWhatAutocompleteSuggestions($query: SearchAutocompleteQuery) {

    autocomplete(query: $query) {
        ... on SearchAutocompleteResult {
            id
            type
            name {
                text
                highlight {
                    length
                    offset
                }
            }
        }
        ... on SearchAutocompleteRestaurant {
            zipcode: zipCode
            origin
            city: cityName
            country: countryName
            thumbnailPhotoUrl
        }
        ... on SearchAutocompleteRestaurantTag {
            categoryId
            categoryName
            categoryDescription
        }
        ... on SearchAutocompleteShortcut {
            categoryId
            shortcutCategoryName: categoryName
            categoryDescription
        }
    }
}
`;
