import { singleton } from 'tsyringe';

import { fetchUntilWorks } from ':microservices/node-crawler';

import { TheForkAutocompleteSuggestion } from './interface';
import {
    AUTOCOMPLETE_SUGGESTIONS_OP_NAME,
    AUTOCOMPLETE_SUGGESTIONS_QUERY,
    getAutocompleteSuggestionsVariables,
    getReviewsGqlHeaders,
    getTheForkApiUrl,
    getTheForkGqlRequestBody,
} from './lafourchette.queries';

const SEARCH_SOCIAL_IDS_MAX_RETRIES = 0;

@singleton()
export class TheForkProvider {
    async searchSocialIds(searchText: string): Promise<TheForkAutocompleteSuggestion[]> {
        const gqlRequestVariables = getAutocompleteSuggestionsVariables(searchText);
        const body = getTheForkGqlRequestBody(AUTOCOMPLETE_SUGGESTIONS_OP_NAME, AUTOCOMPLETE_SUGGESTIONS_QUERY, gqlRequestVariables);
        const headers = getReviewsGqlHeaders();

        const result = await fetchUntilWorks({
            params: {
                method: 'POST',
                url: getTheForkApiUrl({ useProxy: false }),
                headers,
                body,
            },
            isResponseValid: (res) => !!res?.data?.autocomplete,
            retries: SEARCH_SOCIAL_IDS_MAX_RETRIES,
            crawlerCount: 1,
        });

        return result?.data?.autocomplete;
    }
}
