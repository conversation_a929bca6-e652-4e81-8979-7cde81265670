import assert from 'node:assert/strict';
import { container } from 'tsyringe';

import { ICredential, ID, IReview, ITripadvisorCredential } from '@malou-io/package-models';
import { MalouErrorCode, PlatformKey, platformsKeys, TimeInMilliseconds, USER_AGENTS } from '@malou-io/package-utils';

import { MalouError } from ':helpers/classes/malou-error';
import { logger } from ':helpers/logger';
import { fetchUntilWorks } from ':microservices/node-crawler';
import TripadvisorCredentialsRepository from ':modules/credentials/platforms/tripadvisor/tripadvisor.repository';
import * as tripCredentialsUseCases from ':modules/credentials/platforms/tripadvisor/tripadvisor.use-cases';
import { getPaginatedReviewsQuery, getReplyQuery } from ':modules/providers/platforms/tripadvisor/tripadvisor.queries';
import { PlatformHeaderConfigOptions, PlatformReplyOptions } from ':modules/providers/use-cases';

const tripadvisorRepository = container.resolve(TripadvisorCredentialsRepository);

export interface TripadvisorReview {
    title: string;
    authorDisplayName: string;
    snippet: string;
    createTime: string;
    ratingPercent: number;
    ratingDenominator: number;
    sourceLanguage: string;
    globalId: string;
    provider: string;
    responseSubmissionStatus: string;
    favoriteReviewState: string;
}

export interface HrmReviewList {
    reviews: TripadvisorReview[];
    totalResults: number;
    counts: number[];
}

export interface TripAdvisorGetPaginatedReviewResponse {
    data: { HRM_reviewList: HrmReviewList[] };
}

export namespace TripadvisorProvider {
    export const updateReviewReply = async (
        { review, comment, locationId }: { review: IReview; comment: any; locationId: number },
        { headerConfig }: PlatformReplyOptions
    ) => {
        const socialId = +review.socialId;
        const query = [getReplyQuery(review, comment, locationId)];
        if (!headerConfig?.credentials) {
            const credentials = await getTripAdvisorCredentials();
            Object.assign(headerConfig ?? {}, { credentials });
        }
        const headers = getHeaders({ ...(headerConfig ?? {}), socialId });
        return fetchUntilWorks({
            params: {
                url: `${platformsKeys.TRIPADVISOR.apiUrl}/ids`,
                headers,
                body: query,
                method: 'POST',
                timeout: 2000,
            },
            isResponseValid: (response) => response?.[0]?.data?.postReviewResponse?.submissionSuccess,
            retries: 5,
        });
    };

    export const getPaginatedReviews = async (
        locationId: number,
        page: number,
        { headerConfig }: PlatformReplyOptions
    ): Promise<TripAdvisorGetPaginatedReviewResponse[]> => {
        const query = [getPaginatedReviewsQuery(locationId, page)];
        if (!headerConfig?.credentials) {
            const credentials = await getTripAdvisorCredentials();
            Object.assign(headerConfig ?? {}, { credentials });
        }
        const headers = getHeaders(headerConfig ?? {});
        try {
            const result = await fetchUntilWorks({
                params: {
                    url: `${platformsKeys.TRIPADVISOR.apiUrl}/ids`,
                    headers,
                    body: query,
                    method: 'POST',
                    timeout: 2000,
                },
                isResponseValid: (response) => response?.[0]?.data?.HRM_reviewList,
                retries: 5,
            });
            return result;
        } catch (error) {
            logger.error('[TRIPADVISOR_GET_PAGINATED_REVIEWS] Error while getting paginated reviews via GCP', {
                error,
                locationId,
            });
            return [];
        }
    };

    /**
     * Generate an axios instance ready to hit TripAdvisor's api with the correct credentials
     */
    export const getHeaders = function ({ credentials, userAgent, socialId }: PlatformHeaderConfigOptions) {
        const selectedUserAgent = userAgent || selectRandomDefaultUserAgent();
        const locationId = socialId ? `locationId=${socialId}&` : '';
        const referer = `https://www.tripadvisor.fr/reviews?${locationId}screen=allreviews`;
        const headers = {
            accept: '*/*',
            'accept-language': 'fr-FR,fr;q=0.9,en-US;q=0.8,en;q=0.7,ar-DZ;q=0.6,ar;q=0.5',
            'cache-control': 'no-cache',
            'content-type': 'application/json',
            cookie: credentials?.cookie,
            pragma: 'no-cache',
            referer,
            'User-Agent': selectedUserAgent,
            'sec-ch-device-memory': '8',
            'sec-ch-ua': '"Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"',
            'sec-ch-ua-arch': '"arm"',
            'sec-ch-ua-full-version-list': '"Not;A=Brand";v="********", "Google Chrome";v="139.0.7258.128", "Chromium";v="139.0.7258.128"',
            'sec-ch-ua-mobile': '?0',
            'sec-ch-ua-model': '""',
            'sec-ch-ua-platform': '"macOS"',
            'sec-fetch-dest': 'empty',
            'sec-fetch-mode': 'same-origin',
            'sec-fetch-site': 'same-origin',
        };

        return headers;
    };

    export const getAllReviews = async (_credential: ICredential, _restaurantSocialId: string, _restaurantId?: ID) => {
        throw new MalouError(MalouErrorCode.NOT_IMPLEMENTED, {
            metadata: {
                platform: PlatformKey.TRIPADVISOR,
            },
        });
    };

    export const getRecentReviews = async (_credential: ICredential, _restaurantSocialId: string, _restaurantId?: ID) => {
        throw new MalouError(MalouErrorCode.NOT_IMPLEMENTED, {
            metadata: {
                platform: PlatformKey.TRIPADVISOR,
            },
        });
    };

    export const getTripAdvisorCredentials = async (): Promise<ITripadvisorCredential> => {
        let credential = await tripadvisorRepository.findOne({ filter: { key: 'tripadvisor-malou' }, options: { lean: true } });
        if (!credential) {
            throw new MalouError(MalouErrorCode.NOT_FOUND);
        }

        // refresh token if necessary
        const now = new Date().getTime();
        const tokenDelayOfValidity = 1 * TimeInMilliseconds.DAY;
        const endTokenValidity = credential?.updatedAt?.getTime() + tokenDelayOfValidity;
        if (!endTokenValidity || endTokenValidity - now < 0 || !credential.accessToken) {
            credential = await _refreshToken({ credential });
        }
        return credential;
    };

    const _refreshToken = async ({ credential }: { credential: ITripadvisorCredential }): Promise<ITripadvisorCredential> => {
        try {
            const { cookie } = await tripCredentialsUseCases.getApiTokenAndCookies();
            const updatedCredential = await tripadvisorRepository.findOneAndUpdate({
                filter: { _id: credential._id },
                update: { cookie },
                options: { lean: true },
            });
            assert(updatedCredential, 'Failed to update credential');
            return updatedCredential;
        } catch {
            // Don't change anything if there is an error refreshing the credentials
            return credential;
        }
    };

    const selectRandomDefaultUserAgent = () => {
        return USER_AGENTS[Math.floor(Math.random() * USER_AGENTS.length)];
    };
}
