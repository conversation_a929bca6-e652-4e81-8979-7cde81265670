import { singleton } from 'tsyringe';

import { NotificationDTO } from '@malou-io/package-dto';
import { PermissionsRevokedMailTemplate } from '@malou-io/package-emails';
import {
    ApplicationLanguage,
    getPlatformDefinition,
    HeapEventName,
    MalouErrorCode,
    mapApplicationLanguageToLocale,
    NotificationType,
    PlatformKey,
} from '@malou-io/package-utils';

import { Config } from ':config';
import { MalouError } from ':helpers/classes/malou-error';
import { PlatformDisconnectedNotification } from ':modules/notifications/entities/platform-disconnected-notification.entity';
import { Translation } from ':services/translation.service';

import { EmailNotificationPayload, NotificationMapper } from '../notification.mapper.interface';

interface PlatformDisconnectedNotificationEmailPayload extends EmailNotificationPayload {
    notification: PlatformDisconnectedNotification;
}

@singleton()
export class PlatformDisconnectedNotificationChannelsMapper
    implements NotificationMapper<PlatformDisconnectedNotification, PlatformDisconnectedNotificationEmailPayload, NotificationDTO, null>
{
    constructor(private readonly _translationService: Translation) {}

    async mapToEmail(
        notification: PlatformDisconnectedNotification,
        apiKey: string
    ): Promise<PlatformDisconnectedNotificationEmailPayload> {
        const defaultLanguage = notification.user.defaultLanguage as ApplicationLanguage;
        const platformKey = notification.data.platform.key;
        const lang = mapApplicationLanguageToLocale(defaultLanguage);
        const translatorFunctions = this._translationService.fromLang({ lang }).mailing.permissions.connection_revoked;
        const link = this._getLink({ restaurantId: notification.data.restaurantIds[0], platformKey: notification.data.platform.key });
        const disconnectPlatformLink = this._getDisconnectPlatformLink({
            restaurantId: notification.data.restaurantIds[0],
            platformKey: notification.data.platform.key,
        });
        const platformName = getPlatformDefinition(platformKey)?.fullName;
        if (!platformName) {
            throw new MalouError(MalouErrorCode.NOT_FOUND, {
                message: '[PlatformDisconnectedNotificationChannelsMapper] Platform Name not found',
                metadata: { platformKey },
            });
        }
        const subject = translatorFunctions.subject({
            platformName,
            restaurantName:
                notification.data.restaurantIds.length > 1
                    ? this._translationService.fromLang({ lang }).common.your_locations().toLowerCase()
                    : notification.data.mainRestaurantName,
        });
        const trackingUrl = this._buildTrackingUrl({
            apiKey,
            notificationId: notification.id,
            userEmail: notification.user.email,
        });

        return {
            emailSubject: subject,
            heapEventName: HeapEventName.NOTIFICATION_PLATFORM_DISCONNECTED_EMAIL_SENT,
            notification,
            templateFunc: () =>
                PermissionsRevokedMailTemplate({
                    trackingUrl,
                    link,
                    disconnectPlatformLink,
                    restaurantName: notification.data.mainRestaurantName,
                    receiver: notification.user.name,
                    locale: lang,
                    platformName,
                    restaurantNames: [notification.data.mainRestaurantName, ...notification.data.otherRestaurantNames],
                }),
        };
    }

    async mapToWeb(notification: PlatformDisconnectedNotification): Promise<NotificationDTO> {
        return notification.toDTO();
    }

    async mapToPushNotification(_notification: PlatformDisconnectedNotification): Promise<any> {
        return null;
    }

    private _getLink({ restaurantId, platformKey }: { restaurantId: string; platformKey: PlatformKey }): string {
        return `${process.env.BASE_URL}/restaurants/${restaurantId}/settings/platforms/connection?reconnectPlatform=${platformKey}&nchannel=email&type=${NotificationType.PLATFORM_DISCONNECTED}`;
    }

    private _getDisconnectPlatformLink({ restaurantId, platformKey }: { restaurantId: string; platformKey: PlatformKey }): string {
        return `${process.env.BASE_URL}/restaurants/${restaurantId}/settings/platforms/connection?disconnectPlatform=${platformKey}&nchannel=email&type=${NotificationType.PLATFORM_DISCONNECTED}`;
    }

    private _buildTrackingUrl({
        apiKey,
        notificationId,
        userEmail,
    }: {
        apiKey: string;
        notificationId: string;
        userEmail: string;
    }): string {
        const urlWithoutQuery = `${Config.baseApiUrl}/notifications/emails/opened`;
        const today = new Date().getTime();
        return `${urlWithoutQuery}?nid=${notificationId}&receiverEmail=${userEmail}&t=${today}&api_key=${apiKey}&type=${NotificationType.PLATFORM_DISCONNECTED}`;
    }
}
