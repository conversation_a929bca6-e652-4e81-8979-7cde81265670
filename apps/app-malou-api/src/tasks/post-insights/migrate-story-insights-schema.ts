import 'reflect-metadata';

import ':env';

import { Cursor } from 'mongoose';
import { container, singleton } from 'tsyringe';

import { DbId, IStoryPostInsight } from '@malou-io/package-models';
import { isNotNil, MalouMetric, PlatformKey, PostInsightEntityType } from '@malou-io/package-utils';

import PlatformsRepository from ':modules/platforms/platforms.repository';
import { PostInsightsRepository } from ':modules/post-insights/post-insights.repository';
import StoryInsightsRepository from ':modules/post-insights/v2/repositories/story-insights.repository';
import RestaurantsRepository from ':modules/restaurants/restaurants.repository';

interface IOldPostInsight {
    _id: DbId;
    platformKey: PlatformKey;
    socialId: string;
    date: string;
    data: {
        metric: StoryMetric;
        value: number | null;
    }[];
    platformSocialId: string;
    socialLink?: string;
    createdAt: string;
    updatedAt: string;
}

type StoryMetric =
    | MalouMetric.IMPRESSIONS
    | MalouMetric.REACH
    | MalouMetric.TAPS_FORWARD
    | MalouMetric.TAPS_BACK
    | MalouMetric.REPLIES
    | MalouMetric.TAPS_EXITS
    | 'exits';

interface IStoryInsightData {
    impressions: number | null;
    reach: number | null;
    tapsForward: number | null;
    tapsBack: number | null;
    replies: number | null;
    tapsExits: number | null;
}

@singleton()
class MigrateStoryInsightsSchemaTask {
    constructor(
        private readonly _postInsightRepository: PostInsightsRepository,
        private readonly _storyInsightsRepository: StoryInsightsRepository,
        private readonly _restaurantsRepository: RestaurantsRepository,
        private readonly _platformsRepository: PlatformsRepository
    ) {}

    async run(): Promise<void> {
        const allRestaurantIds = await this._restaurantsRepository.getAllActiveRestaurantIds();
        const allPlatforms = await this._platformsRepository.getPlatformsByRestaurantIdsAndPlatformKeys(allRestaurantIds, [
            PlatformKey.INSTAGRAM,
        ]);

        const platformSocialIds = allPlatforms.map((platform) => platform.socialId).filter(isNotNil);
        const uniquePlatformSocialIds = Array.from(new Set(platformSocialIds));

        const cursor: Cursor<IOldPostInsight, never> = await this._postInsightRepository
            .aggregate([{ $match: { platformSocialId: { $in: uniquePlatformSocialIds }, entityType: { $exists: false } } }])
            .cursor();

        await cursor.eachAsync(
            async (postInsights) => {
                console.log(`Migrating ${postInsights.length} post insights to story insights...`);
                const storyPostInsights: Omit<IStoryPostInsight, '_id'>[] = postInsights.map((postInsight) =>
                    this._mapToStoryPostInsight(postInsight)
                );

                const bulkOps = storyPostInsights.map((storyPostInsight) => ({
                    updateOne: {
                        filter: {
                            socialId: storyPostInsight.socialId,
                            platformSocialId: storyPostInsight.platformSocialId,
                        },
                        update: storyPostInsight,
                        upsert: true,
                    },
                }));

                await this._storyInsightsRepository.bulkOperations({ operations: bulkOps });
            },
            {
                batchSize: 1000,
            }
        );
    }

    private _mapToStoryPostInsight(postInsight: IOldPostInsight): Omit<IStoryPostInsight, '_id'> {
        const metricsObject = this._transformMetricsArrayToObject(postInsight.data);
        return {
            platformKey: postInsight.platformKey,
            socialId: postInsight.socialId,
            platformSocialId: postInsight.platformSocialId,
            data: metricsObject,
            entityType: PostInsightEntityType.STORY,
            lastFetchedAt: new Date(),
            postSocialCreatedAt: new Date(postInsight.date),
            createdAt: new Date(postInsight.createdAt),
            updatedAt: new Date(postInsight.updatedAt),
        };
    }

    private _transformMetricsArrayToObject(metricsArray: { metric: StoryMetric; value: number | null }[]): IStoryInsightData {
        return metricsArray.reduce((acc, { metric, value }) => {
            if (metric === 'exits') {
                acc.tapsExits = value ?? null;
                return acc;
            }
            if (metric === MalouMetric.TAPS_EXITS) {
                acc.tapsExits = value ?? null;
                return acc;
            }
            if (metric === MalouMetric.TAPS_FORWARD) {
                acc.tapsForward = value ?? null;
                return acc;
            }
            if (metric === MalouMetric.TAPS_BACK) {
                acc.tapsBack = value ?? null;
                return acc;
            }
            acc[metric] = value ?? null;
            return acc;
        }, {} as IStoryInsightData);
    }
}

const task = container.resolve(MigrateStoryInsightsSchemaTask);

task.run()
    .then(() => {
        console.log('Migration completed successfully.');
        process.exit(0);
    })
    .catch((error) => {
        console.error('Migration failed:', error);
        process.exit(1);
    });
