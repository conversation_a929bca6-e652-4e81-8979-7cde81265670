/* Use this task as a playground to run stuff using any app logic.
You can also just copy paste to another file to keep track of previous run tasks */
import 'reflect-metadata';

import ':env';

import ':di';
import { writeFileSync } from 'fs';
import { container, singleton } from 'tsyringe';

import { PlatformKey, waitFor } from '@malou-io/package-utils';

import { logger } from ':helpers/logger';
import PlatformsRepository from ':modules/platforms/platforms.repository';
import RestaurantsRepository from ':modules/restaurants/restaurants.repository';
import ':plugins/db';
import { GmbBusinessVerificationsProvider } from ':providers/google/gmb.business-verifications.provider';
import { GmbRefreshTokenService } from ':services/credentials/gmb/gmb-refresh-token.service';

@singleton()
class DefaultTask {
    constructor(
        private readonly _platformsRepository: PlatformsRepository,
        private readonly _restaurantsRepository: RestaurantsRepository,
        private readonly _gmbBusinessVerificationsProvider: GmbBusinessVerificationsProvider,
        private readonly _gmbRefreshTokenService: GmbRefreshTokenService
    ) {}

    async execute() {
        logger.info('Starting GMB Voice of Merchant State task...');

        // Fetch all active restaurants
        const activeRestaurants = await this._restaurantsRepository.find({
            filter: { active: true },
            options: { lean: true },
        });

        logger.info(`Found ${activeRestaurants.length} active restaurants`);

        // Get all restaurant IDs
        const restaurantIds = activeRestaurants.map((restaurant) => restaurant._id.toString());

        // Fetch GMB platforms for active restaurants
        const gmbPlatforms = await this._platformsRepository.getPlatformsByRestaurantIdsAndPlatformKey(restaurantIds, PlatformKey.GMB);

        logger.info(`Found ${gmbPlatforms.length} GMB platforms linked to active restaurants`);

        const results: Array<{
            restaurantId: string;
            name: string | undefined;
            locationGmbSocialId: string;
            voiceOfMerchantStatus: any;
        }> = [];

        for (const platform of gmbPlatforms) {
            try {
                // Get the first credential
                const credentialId = platform.credentials?.[0];
                if (!credentialId) {
                    logger.warn(`No credentials found for platform ${platform._id}`);
                    continue;
                }

                // Extract location ID from socialId
                const locationId = platform.apiEndpointV2?.split('locations/')[1];
                if (!locationId) {
                    logger.warn(`No socialId found for platform ${platform._id}`);
                    continue;
                }

                // Get fresh token if necessary
                const credential = await this._gmbRefreshTokenService.getFreshTokenIfNecessary(credentialId);

                // Fetch Voice of Merchant State
                const voiceOfMerchantResponse = await this._gmbBusinessVerificationsProvider.getVoiceOfMerchantState({
                    accessToken: credential.accessToken,
                    locationId,
                });

                await waitFor(200);

                const result = {
                    restaurantId: platform.restaurantId.toString(),
                    name: platform.name,
                    locationGmbSocialId: locationId,
                    voiceOfMerchantStatus: voiceOfMerchantResponse.data,
                };

                results.push(result);
                logger.info(`Processed platform ${platform._id} for restaurant ${platform.restaurantId}`);
            } catch (error) {
                logger.error(`Error processing platform ${platform._id}:`, error);

                // Add error result
                results.push({
                    restaurantId: platform.restaurantId.toString(),
                    name: platform.name,
                    locationGmbSocialId: platform.socialId || 'unknown',
                    voiceOfMerchantStatus: { error: error instanceof Error ? error.message : String(error) },
                });
            }
        }

        // Write results to file
        const fileName = `gmb-voice-of-merchant-states-${new Date().toISOString().split('T')[0]}.json`;
        writeFileSync(fileName, JSON.stringify(results, null, 2));

        logger.info(`Voice of Merchant states written to ${fileName}`);
        logger.info(`Processed ${results.length} platforms total`);
    }
}

const task = container.resolve(DefaultTask);
task.execute()
    .then(() => {
        process.exit(0);
    })
    .catch((error) => {
        console.error('err :>>', error);
        process.exit(1);
    });
