import 'reflect-metadata';

import ':env';

import { container, singleton } from 'tsyringe';

import { toDbId } from '@malou-io/package-models';
import {
    StoreLocatorCentralizationPageElementIds,
    StoreLocatorCommonElementIds,
    StoreLocatorLanguage,
    StoreLocatorRestaurantPageElementIds,
} from '@malou-io/package-utils';

import { StoreLocatorOrganizationConfigRepository } from ':modules/store-locator/store-locator-organization-config.repository';

interface OrganizationConfiguration {
    organizationId: string;
    cloudfrontDistributionId: string;
    baseUrl: string;
    fonts: {
        class:
            | 'primary'
            | 'primary-bold'
            | 'secondary'
            | 'secondary-bold'
            | 'tertiary'
            | 'tertiary-bold'
            | 'fourth'
            | 'fourth-bold'
            | 'fifth'
            | 'fifth-bold'
            | 'sixth'
            | 'sixth-bold'
            | 'seventh'
            | 'seventh-bold'
            | 'eighth'
            | 'eighth-bold'
            | 'ninth'
            | 'ninth-bold'
            | 'tenth'
            | 'tenth-bold';
        src: string;
        weight?: '400' | '700' | '900';
        style?: 'normal' | 'italic';
    }[];
    colors: {
        class: 'primary' | 'secondary' | 'tertiary' | 'fourth' | 'fifth' | 'sixth' | 'seventh' | 'eighth' | 'ninth' | 'tenth';
        value: string;
    }[];
    languages: {
        primary: StoreLocatorLanguage;
        secondary: StoreLocatorLanguage[];
    };
}
@singleton()
class CreateOrganizationConfigurationTask {
    constructor(private readonly _storeLocatorOrganizationConfigRepository: StoreLocatorOrganizationConfigRepository) {}

    async execute(): Promise<void> {
        // const config = await this._getTailwindConfiguration('67cf1ef531d778287af0d2ef');
        try {
            await this._handleOrganizationConfiguration({
                organizationId: '',
                cloudfrontDistributionId: '',
                baseUrl: '',
                fonts: [
                    {
                        class: 'primary',
                        src: 'https://malou-production.s3.eu-west-3.amazonaws.com/store-locator/organization/6216176cfad4028e790f2c68/fonts/Inter-VariableFont_opszwght.ttf',
                    },
                    {
                        class: 'secondary',
                        src: 'https://malou-production.s3.eu-west-3.amazonaws.com/store-locator/organization/6216176cfad4028e790f2c68/fonts/BriceGX.ttf',
                    },
                ],
                colors: [
                    {
                        class: 'primary',
                        value: '#666f66',
                    },
                    {
                        class: 'secondary',
                        value: '#C0A365',
                    },
                    {
                        class: 'tertiary',
                        value: '#020203',
                    },
                ],
                languages: {
                    primary: StoreLocatorLanguage.EN,
                    secondary: [StoreLocatorLanguage.FR],
                },
            });
        } catch (error) {
            console.error('Error creating organization configuration:', error);
        }
    }

    /*
    *   Example base url:
    *   https://restaurants.bioburger.fr
    * 
    *   Example fonts: 
        {
            class: 'primary',
            src: 'https://malou-production.s3.eu-west-3.amazonaws.com/store-locator/organization/655e40ed9d2240f0d1f4bce4/fonts/brandon_blk.woff2',
        },
    *   
    *   Example colors:
    *   {
            class: 'primary',
            src: 'https://malou-production.s3.eu-west-3.amazonaws.com/store-locator/organization/655e40ed9d2240f0d1f4bce4/fonts/brandon_blk.woff2',
        },
    */

    private async _handleOrganizationConfiguration({
        baseUrl,
        cloudfrontDistributionId,
        organizationId,
        fonts,
        colors,
        languages,
    }: OrganizationConfiguration) {
        const storePageStyles = {
            [StoreLocatorRestaurantPageElementIds.INFORMATION_WRAPPER]: [
                'bg-primary',
                'text-fourth',
                'mt-[110px]',
                'xl:h-[calc(100vh-110px)]',
            ],
            [StoreLocatorRestaurantPageElementIds.INFORMATION_TITLE]: ['font-primary', 'text-secondary'],
            [StoreLocatorRestaurantPageElementIds.INFORMATION_ICONS]: ['fill-secondary'],
            [StoreLocatorRestaurantPageElementIds.INFORMATION_OPENING_SOON_BANNER]: ['bg-secondary', 'text-white', 'rounded-sm'],
            [StoreLocatorRestaurantPageElementIds.INFORMATION_BANNER]: ['bg-primary'],
            [StoreLocatorRestaurantPageElementIds.INFORMATION_BANNER_CTA_1]: [
                'bg-secondary',
                'text-white',
                'hover:opacity-70',
                'rounded-sm',
            ],
            [StoreLocatorRestaurantPageElementIds.INFORMATION_BANNER_CTA_2]: [
                'border-primary',
                'bg-primary',
                'text-white',
                'hover:opacity-70',
                'rounded-sm',
            ],
            [StoreLocatorRestaurantPageElementIds.INFORMATION_BANNER_CTA_ICON]: ['fill-white'],
            [StoreLocatorRestaurantPageElementIds.GALLERY_WRAPPER]: ['bg-white', 'text-primary'],
            [StoreLocatorRestaurantPageElementIds.GALLERY_TITLE]: ['font-primary'],
            [StoreLocatorRestaurantPageElementIds.GALLERY_PICTURE]: ['rounded-lg'],
            [StoreLocatorRestaurantPageElementIds.REVIEWS_WRAPPER]: ['bg-primary'],
            [StoreLocatorRestaurantPageElementIds.REVIEWS_TITLE]: ['text-white', 'font-primary'],
            [StoreLocatorRestaurantPageElementIds.REVIEWS_CTA]: [
                'bg-secondary',
                'text-white',
                'border-secondary',
                'hover:opacity-70',
                'rounded-lg',
            ],
            [StoreLocatorRestaurantPageElementIds.CALL_TO_ACTIONS_WRAPPER]: ['bg-secondary'],
            [StoreLocatorRestaurantPageElementIds.CALL_TO_ACTIONS_TITLE]: ['text-white', 'font-primary'],
            [StoreLocatorRestaurantPageElementIds.CALL_TO_ACTIONS_CTA]: [
                'bg-transparent',
                'border-white',
                'text-white',
                'hover:opacity-70',
                'rounded-lg',
            ],
            [StoreLocatorRestaurantPageElementIds.SOCIAL_NETWORKS_WRAPPER]: ['bg-white'],
            [StoreLocatorRestaurantPageElementIds.SOCIAL_NETWORKS_TITLE]: ['text-primary', 'font-primary'],
            [StoreLocatorRestaurantPageElementIds.SOCIAL_NETWORKS_PROFILE]: ['text-black'],
            [StoreLocatorRestaurantPageElementIds.SOCIAL_NETWORKS_PROFILE_NAME]: ['font-black'],
            [StoreLocatorRestaurantPageElementIds.DESCRIPTIONS_BLOCK_EVEN]: ['bg-white', 'text-black'],
            [StoreLocatorRestaurantPageElementIds.DESCRIPTIONS_BLOCK_TITLE_EVEN]: ['font-primary', 'text-tertiary'],
            [StoreLocatorRestaurantPageElementIds.DESCRIPTIONS_BLOCK_UNEVEN]: ['bg-white', 'text-black'],
            [StoreLocatorRestaurantPageElementIds.DESCRIPTIONS_BLOCK_TITLE_UNEVEN]: ['font-primary', 'text-tertiary'],
            [StoreLocatorRestaurantPageElementIds.FAQ_WRAPPER]: ['bg-primary'],
            [StoreLocatorRestaurantPageElementIds.FAQ_TITLE]: ['text-tertiary'],
            [StoreLocatorRestaurantPageElementIds.FAQ_ITEM]: ['bg-primary'],
            [StoreLocatorRestaurantPageElementIds.FAQ_ITEM_QUESTION]: ['text-tertiary'],
            [StoreLocatorRestaurantPageElementIds.FAQ_ITEM_ANSWER]: ['text-tertiary'],
            [StoreLocatorRestaurantPageElementIds.FAQ_ICON_WRAPPER]: ['bg-white'],
            [StoreLocatorRestaurantPageElementIds.FAQ_ICON]: ['fill-primary'],
        };
        const mapPageStyles = {
            [StoreLocatorCentralizationPageElementIds.STORE_LIST_SEARCH_WRAPPER]: ['bg-primary'],
            [StoreLocatorCentralizationPageElementIds.STORE_LIST_SEARCH_INPUT]: ['bg-secondary', 'border-secondary'],
            [StoreLocatorCentralizationPageElementIds.STORE_LIST_SEARCH_INPUT_ICON]: ['fill-white'],
            [StoreLocatorCentralizationPageElementIds.STORE_LIST_SEARCH_BUTTON]: ['bg-secondary', 'border-secondary'],
            [StoreLocatorCentralizationPageElementIds.STORE_LIST_ITEMS_WRAPPER]: ['bg-fourth'],
            [StoreLocatorCentralizationPageElementIds.STORE_LIST_ITEM]: ['text-tertiary', 'font-primary', 'bg-white', 'border-secondary'],
            [StoreLocatorCentralizationPageElementIds.STORE_LIST_ITEM_TITLE]: ['font-primary'],
            [StoreLocatorCentralizationPageElementIds.STORE_LIST_ITEM_DISTANCE_BLOCK]: ['bg-secondary', 'text-white'],
            [StoreLocatorCentralizationPageElementIds.MAP_POPUP_WRAPPER]: ['text-tertiary', 'font-primary'],
            [StoreLocatorCentralizationPageElementIds.MAP_POPUP_TITLE]: [],
            [StoreLocatorCentralizationPageElementIds.MAP_AND_STORE_LIST_WRAPPER]: ['mt-20'],
            [StoreLocatorCentralizationPageElementIds.MAP_MARKER_GROUP]: ['bg-primary', 'text-tertiary'],
            [StoreLocatorCentralizationPageElementIds.MAP_PAGE_ICONS]: ['fill-secondary'],
            [StoreLocatorCentralizationPageElementIds.MAP_PAGE_STORE_NOT_OPEN_YET]: ['bg-secondary', 'text-primary'],
        };

        const commonStyles = {
            [StoreLocatorCommonElementIds.WHITE_MARK_WRAPPER]: ['font-secondary', 'bg-tertiary', 'text-white', 'p-8'],
            [StoreLocatorCommonElementIds.WHITE_MARK_LOGO]: ['fill-white'],
        };

        await this._storeLocatorOrganizationConfigRepository.create({
            data: {
                organizationId: toDbId(organizationId),
                cloudfrontDistributionId,
                baseUrl,
                languages,
                isLive: false,
                shouldDisplayWhiteMark: true,
                styles: {
                    fonts,
                    colors,
                    pages: {
                        store: storePageStyles,
                        map: mapPageStyles,
                        common: commonStyles,
                        storeDraft: {},
                        mapDraft: {},
                    },
                },
                blocksSettings: {
                    faq: {
                        questionsTemplate: [],
                    },
                    reviews: {
                        isOnlyFiveStarsReviews: false,
                    },
                },
            },
        });
    }
}

const task = container.resolve(CreateOrganizationConfigurationTask);

task.execute()
    .then(() => {
        console.log('Task completed');
        process.exit(0);
    })
    .catch((error) => {
        console.error('Error while executing task', error);
        process.exit(1);
    });
