import 'reflect-metadata';

import ':env';

import fs from 'fs';
import path from 'path';
import { container, singleton } from 'tsyringe';

import { PlatformKey, TimeInMilliseconds, TimeInSeconds, waitFor } from '@malou-io/package-utils';

import { logger } from ':helpers/logger';
import { FetchAndSaveKeywordsSearchImpressionsUseCase } from ':modules/keyword-search-impressions/use-cases/fetch-and-save-keyword-search-impressions/fetch-and-save-keywords-search-impressions.use-case';
import PlatformsRepository from ':modules/platforms/platforms.repository';
import RestaurantsRepository from ':modules/restaurants/restaurants.repository';
import ':plugins/db';
import { GmbBusinessProfilePerformanceProvider } from ':providers/google/gmb.business-profile-performance.provider';

@singleton()
class FetchRestaurantsDataTask {
    constructor(
        private readonly _fetchAndSaveKeywordsSearchImpressionsUseCase: FetchAndSaveKeywordsSearchImpressionsUseCase,
        private readonly _restaurantRepository: RestaurantsRepository,
        private readonly _platformRepository: PlatformsRepository
    ) {}

    async run() {
        console.log('Fetching restaurants data');
        const ARBITRARY_VALUE_OF_MAX_QPM = 0.6;
        const restaurantIds = await this._getRestaurantIdsToFetchData();

        if (restaurantIds.length === 0) {
            logger.info('FetchAndSaveKeywordsSearchImpressionsUseCase - No restaurant ids to fetch data');
            return;
        }

        const platforms = await this._platformRepository.getPlatformsByRestaurantIdsAndPlatformKeys(restaurantIds, [PlatformKey.GMB]);

        const platformslength = platforms.length;
        let i = 0;

        for (const platform of platforms) {
            // wait for the time needed to avoid hitting the QPM limit
            await waitFor(
                (TimeInSeconds.MINUTE / (GmbBusinessProfilePerformanceProvider.MAX_QPM * ARBITRARY_VALUE_OF_MAX_QPM)) *
                    this._getNbOfWorkers() *
                    TimeInMilliseconds.SECOND *
                    2
            );

            logger.info(
                // eslint-disable-next-line max-len
                `FetchAndSaveKeywordsSearchImpressionsUseCase - Fetching data for platformId ${platform._id.toString()} (${i}/${platformslength})`
            );

            await this._fetchAndSaveKeywordsSearchImpressionsUseCase.execute({ platformId: platform._id.toString() });

            i++;
        }
    }

    private _getNbOfWorkers() {
        if (process.env.NODE_ENV === 'production') {
            return 3;
        }
        if (process.env.NODE_ENV === 'staging' || process.env.NODE_ENV === 'development') {
            return 2;
        }
        return 1;
    }

    private async _getRestaurantIdsToFetchData(): Promise<string[]> {
        const filePath = path.join(__dirname, 'restaurant-ids-without-data.json');
        if (fs.existsSync(filePath)) {
            const fileContent = fs.readFileSync(filePath, 'utf-8');
            return JSON.parse(fileContent);
        }

        return [];
        // Warning: this will fetch all restaurant ids from the database
        // const restaurantIds = await this._restaurantRepository.getAllActiveRestaurantIds();
        // return restaurantIds;
    }
}

const task = container.resolve(FetchRestaurantsDataTask);

task.run()
    .then(() => {
        logger.info('FetchAndSaveKeywordsSearchImpressionsUseCase - Task completed');
        process.exit(0);
    })
    .catch((error) => {
        logger.error('FetchAndSaveKeywordsSearchImpressionsUseCase - Task failed', { error });
        process.exit(1);
    });
