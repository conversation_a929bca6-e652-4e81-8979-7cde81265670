import 'reflect-metadata';

import ':env';

import { autoInjectable, container } from 'tsyringe';

import { toDbId } from '@malou-io/package-models';
import {
    AutomationFeature,
    getPlatformKeysAutomatableWithValidation,
    PlatformKey,
    ReviewReplyAutomationComment,
    ReviewReplyAutomationMethod,
} from '@malou-io/package-utils';

import { INewReviewReplyAutomation } from ':modules/automations/features/review-replies/review-replies.mapper.dto';
import AutomationsUseCases from ':modules/automations/features/review-replies/review-replies.use-cases';
import RestaurantsRepository from ':modules/restaurants/restaurants.repository';
import ':plugins/db';

@autoInjectable()
class InitPowerLocationsAutomations {
    constructor(
        private readonly _automationsUseCases: AutomationsUseCases,
        private readonly _restaurantsRepository: RestaurantsRepository
    ) {}

    async execute(): Promise<void> {
        const restaurantIds = ['640854f4f4b8969832bfbb60'];
        for (const restaurantId of restaurantIds) {
            const restaurant = await this._restaurantsRepository?.getRestaurantById(restaurantId);
            if (restaurant?.active) {
                console.log('Creating automations for restaurant:', restaurantId, '...', 'restaurant name:', restaurant?.name ?? 'N/A');
                await this._upsertAutomationForRestaurant(restaurantId);
            }
        }

        console.log('Automations created successfully!');
    }

    private async _upsertAutomationForRestaurant(restaurantId: string) {
        const restaurantAutomations = this._buildDefaultRestaurantAutomations(restaurantId);
        const existingAutomations = await this._automationsUseCases?.getRestaurantReviewReplyAutomations(toDbId(restaurantId));

        const automationsToUpsert = restaurantAutomations.filter((automation) => {
            const activeExistingAutomation = existingAutomations.find(
                (existing) =>
                    automation.ratingCategory === existing.ratingCategory &&
                    automation.withComment === existing.withComment &&
                    automation.feature === existing.feature &&
                    existing.active === true
            );
            if (activeExistingAutomation?.active) {
                return false;
            }
            return true;
        });

        if (automationsToUpsert.length === 0) {
            console.log(`No new automations to create for restaurant ${restaurantId}`);
            return;
        }

        console.log(`Found ${automationsToUpsert.length} automations to create for restaurant ${restaurantId}`);
        return this._automationsUseCases?.upsertReviewReplyAutomations(automationsToUpsert);
    }

    private _buildDefaultRestaurantAutomations(restaurantId: string): INewReviewReplyAutomation[] {
        const defaultRatings = [3, 4, 5];
        const defaultPlatforms = this._getAllAutoResponsePlatforms();

        return Object.entries(defaultPlatforms).flatMap(([withComment, platformKeys]) => {
            return platformKeys.flatMap((platformKey) => {
                return defaultRatings.map((rating) => {
                    return {
                        platformKey,
                        restaurantId: toDbId(restaurantId),
                        feature: AutomationFeature.REPLY_TO_REVIEW,
                        replyMethod: ReviewReplyAutomationMethod.AI,
                        ratingCategory: rating,
                        withComment: withComment as ReviewReplyAutomationComment,
                        active: true,
                        templateIds: [],
                        shouldValidateAiBeforeSend: true,
                    };
                });
            });
        });
    }

    private _getAllAutoResponsePlatforms(): {
        [ReviewReplyAutomationComment.WITH_COMMENT]: PlatformKey[];
        [ReviewReplyAutomationComment.WITHOUT_COMMENT]: PlatformKey[];
    } {
        return {
            [ReviewReplyAutomationComment.WITH_COMMENT]: getPlatformKeysAutomatableWithValidation(
                ReviewReplyAutomationComment.WITH_COMMENT
            ),
            [ReviewReplyAutomationComment.WITHOUT_COMMENT]: getPlatformKeysAutomatableWithValidation(
                ReviewReplyAutomationComment.WITHOUT_COMMENT
            ),
        };
    }
}

const task = container.resolve(InitPowerLocationsAutomations);

task.execute()
    .then(() => {
        process.exit(0);
    })
    .catch((error) => {
        console.error('err :>>', error);
        process.exit(1);
    });
