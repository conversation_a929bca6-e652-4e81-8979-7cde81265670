import AWS from 'aws-sdk';
import { singleton } from 'tsyringe';

import { PuppeteerLambdaEvent, PuppeteerLambdaEventName } from '@malou-io/package-service-interfaces';

import { Config } from ':config';

@singleton()
export class PuppeteerServiceV2 {
    private readonly LAMBDA = new AWS.Lambda({
        region: Config.services.aws.region,
        accessKeyId: Config.services.aws.key,
        secretAccessKey: Config.services.aws.secret,
    });

    async generatePdfAndGetS3Url(
        params: PuppeteerLambdaEvent<PuppeteerLambdaEventName.INSIGHTS_PDF>
    ): Promise<{ data: string } | { error: any }> {
        const { functionName } = Config.services.puppeteerV2;
        return this._callLambda(params, functionName);
    }

    private async _callLambda(payload: PuppeteerLambdaEvent, functionName: string): Promise<{ data: string } | { error: any }> {
        const params = {
            FunctionName: functionName,
            Payload: JSON.stringify(payload),
        };

        return new Promise((resolve, reject) => {
            this.LAMBDA.invoke(params, (err, data) => {
                if (err) {
                    reject(err);
                } else {
                    resolve(data);
                }
            });
        }).then((data) => {
            const result = JSON.parse((data as AWS.Lambda.InvocationResponse).Payload as string);

            if (result.errorMessage) {
                return { error: result };
            }
            return { data: result };
        });
    }
}
