import { platformsKeys } from '@malou-io/package-utils';

import { Config } from ':config';
import { getPreferredReviewsQuery } from ':modules/providers/platforms/tripadvisor/tripadvisor.queries';
import { getReviewsData } from ':modules/reviews/platforms/yelp/use-cases';

import { fetchUntilWorks } from './node-crawler';

describe.skip('fetchUntilWorks', () => {
    it('should fetch until works', async () => {
        const result = await fetchUntilWorks({
            params: {
                method: 'POST',
                url: 'https://www.tripadvisor.fr/Restaurant_Review-g187147-d15272557-Reviews-or10-Le_Bistrot_de_Madeleine-Paris_Ile_de_France.html',
                headers: {
                    'Content-type': 'application/x-www-form-urlencoded; charset=utf-8',
                    'X-Requested-With': 'XMLHttpRequest',
                    accept: '*/*',
                    'accept-language': 'fr-FR,fr;q=0.9,en-US;q=0.8,en;q=0.7',
                    Connection: 'keep-alive',
                    origin: 'https://www.tripadvisor.fr',
                    Cookie: 'datadome=X0_R2aXCdxQoSTCNGqPqrD2WcOCMh8U09yttJ3x0PhhUXF77qNR0yXOfNIaEFO1jsNcJ02Rab_g4fgWLrkgb7QSDzIOLvT_TfrdBKlBkyT3TnW7aAn7aVRSOn1CtmnRt; Max-Age=31104000; Domain=.tripadvisor.com; Path=/; Secure; SameSite=Lax',
                    // Cookie: "datadome=a4bQO49as74XNeEdEB13pUROx2a4rtdkB~Qu7ZSkhWlV4relLs~avD07IJJ2wZDS8tFVCO9DvLU9OKFE2RYa12zpjdy3vEIJuJfjJawxGfR3BMhPiVDHDV1As9bM5via; Max-Age=31104000; Domain=.tripadvisor.com; Path=/; Secure; SameSite=Lax",
                    // Cookie: "datadome=pUIL8vUCjI41P_jm_wDSztujIvtTgkluX2vpdthAbteP2MSfgVbLrMuXb_1o52SL7Br_FpaerTZbNxTwgT7DFi2v1te12~_dW2SQmUvdLXgu2RA8_fhLJPjiAUaAwQUN; Max-Age=31104000; Domain=.tripadvisor.fr; Path=/; Secure; SameSite=Lax",
                    // Cookie: "datadome=qczedibwAz2f6N47f7FSCJEKJ7KmSfgGPY75O4crT3LP6tI__fGj18D4qIeN8h5duCBjKW5VB6atOzR_x6dl1F7F9apvfMBlRm_pWA3Pk2jWA~Gd8LzCbfS39xniNPAF; Max-Age=31104000; Domain=.tripadvisor.com; Path=/; Secure; SameSite=Lax",
                    // Cookie: "OptanonConsent=isGpcEnabled=0&datestamp=Wed+Apr+03+2024+15%3A06%3A25+GMT%2B0200+(heure+d%E2%80%99%C3%A9t%C3%A9+d%E2%80%99Europe+centrale)&version=202310.2.0&browserGpcFlag=0&isIABGlobal=false&hosts=&consentId=146c5c4e-d0f8-491d-87a2-12bba0f0cf66&interactionCount=1&landingPath=NotLandingPage&groups=C0001%3A1%2CC0004%3A1%2CC0002%3A1%2CC0003%3A1%2CV2STACK42%3A1&geolocation=FR%3BIDF&AwaitingReconsent=false; datadome=ItwP7GogqhB1V2C1TSVUICLSWLY9yK3EW6Z0ga3Kn2eZyPaRYs3arhwawWeni6S87TpUAjfFyQ9OWH1O9TlnoYVM5I0PBWCn38DtosfiX1sXYJzjmtDVWThKvTewhshY; TASID=4DDEE48B53DC4F088E6E605A884BD1DB; TATrkConsent=eyJvdXQiOiJTT0NJQUxfTUVESUEiLCJpbiI6IkFEVixBTkEsRlVOQ1RJT05BTCJ9; TASession=V2ID.4DDEE48B53DC4F088E6E605A884BD1DB*SQ.11*LS.DemandLoadAjax*HS.recommended*ES.popularity*DS.5*SAS.popularity*FPS.oldFirst*LF.fr*FA.1*DF.0*FLO.10150117*TRA.false*LD.10150117*EAU._; TAUD=LA-1712136760621-1*RDD-1-2024_04_03************-2.1.F.************-.....; TAReturnTo=%1%%2FRestaurant_Review-g207338-d10150117-Reviews-BLISSS-Merignac_Bordeaux_Gironde_Nouvelle_Aquitaine.html; TATravelInfo=V2*AY.2024*AM.4*AD.14*DY.2024*DM.4*DD.15*A.2*MG.-1*HP.2*FL.3*DSM.1712149583903*RS.1; roybatty=TNI1625!AJ%2FQIXiExVO31QFdbCnKNPGUJILoYi8IKGN7fMp1qVcao7QfJQt%2BnshINX%2FndOs6nMw%2Fx%2FOCjRhvw%2F74Lo3l6CESAiD8IO3IidI5Pnp9WRBal%2BNSvS1k92nbo4tEwdF2qxjktty1m3TNzvtXlA2J157X2DGc1wpjL79nKdofikmBtkug6%2F6Xi2sa0CsuDGX%2FFA%3D%3D%2C1; PAC=ADn8cw7FdP6N8ybpxjaG10Rb57drJTza1egpkdBgQc5AyZobUHBFyD2MdreZEAnxjt7cFuJGWDK_qQp7T3_P1P2tWrouZmONiJUQS9mdsNqlxObPndcjqgCPEtjU9pj1Cv-I1Yma5kSuIJHX6keRPOTBbd2MX0OhTaq2-1CVf-BZi4-sCmtwMHlF9NzoD-YPXaOxfQVpr24CTBkynvLN1Jj-Ifcuk4wxE2ZfZOp0VsEr0vh9O9WYq8pRChS0tJ-PGQ%3D%3D; SRT=TART_SYNC; __vt=_pPdLWzidiHUqbPJABQCwRB1grfcRZKTnW7buAoPsSyDf8FvNSiBjb3d-fWL3aUaMFTyRRIkoxGcknd5xZNFBBdeVUct3BMQWnNJXD9XdvIhsaQv5m2bW8drAwYOcn2N3cU8a3vZ2X68buI6I8OB0NsazQ; __eoi=ID=ba32cb6230b96682:T=1712136795:RT=1712149579:S=AA-AfjYQVCebKrK-bLKFCx_rU2Z_; __gads=ID=edfd0a514cef0920:T=1712136795:RT=1712149579:S=ALNI_MbXwULaM_9Jgyk67TgKIQIdIf5aLg; __gpi=UID=00000d87ede014e1:T=1712136795:RT=1712149579:S=ALNI_MYvcvxN9HGUvZFqpGR_eSqyhF-Tyw; TALanguage=fr; pbjs_unifiedID=%7B%22TDID_LOOKUP%22%3A%22FALSE%22%2C%22TDID_CREATED_AT%22%3A%222024-04-03T09%3A33%3A21%22%7D; pbjs_unifiedID_cst=vSyQLBQsTA%3D%3D; pbjs_sharedId=ef6f169f-ab25-478d-a68a-bf9ac439c2b6; pbjs_sharedId_cst=vSyQLBQsTA%3D%3D; _lr_env_src_ats=false; _lr_sampling_rate=100; OTAdditionalConsentString=1~43.46.55.61.70.83.89.93.108.117.122.124.135.136.143.144.147.149.159.192.196.202.211.228.230.239.259.266.286.291.311.320.322.323.327.338.367.371.385.394.397.407.413.415.424.430.436.445.453.486.491.494.495.522.523.540.550.559.560.568.574.576.584.587.591.737.802.803.820.821.839.864.899.904.922.931.938.979.981.985.1003.1027.1031.1040.1046.1051.1053.1067.1085.1092.1095.1097.1099.1107.1135.1143.1149.1152.1162.1166.1186.1188.1205.1215.1226.1227.1230.1252.1268.1270.1276.1284.1290.1301.1307.1312.1345.1356.1364.1365.1375.1403.1415.1416.1421.1423.1440.1449.1455.1495.1512.1516.1525.1540.1548.1555.1558.1570.1577.1579.1583.1584.1591.1603.1616.1638.1651.1653.1659.1667.1677.1678.1682.1697.1699.1703.1712.1716.1721.1725.1732.1745.1750.1765.1782.1786.1800.1810.1825.1827.1832.1838.1840.1842.1843.1845.1859.1866.1870.1878.1880.1889.1899.1917.1929.1942.1944.1962.1963.1964.1967.1968.1969.1978.1985.1987.2003.2008.2027.2035.2039.2047.2052.2056.2064.2068.2072.2074.2088.2090.2103.2107.2109.2115.2124.2130.2133.2135.2137.2140.2145.2147.2150.2156.2166.2177.2183.2186.2205.2213.2216.2219.2220.2222.2225.2234.2253.2279.2282.2292.2299.2305.2309.2312.2316.2322.2325.2328.2331.2334.2335.2336.2337.2343.2354.2357.2358.2359.2370.2376.2377.2387.2392.2400.2403.2405.2407.2411.2414.2416.2418.2425.2440.2447.2461.2462.2465.2468.2472.2477.2481.2484.2486.2488.2493.2498.2499.2501.2510.2517.2526.2527.2532.2535.2542.2552.2563.2564.2567.2568.2569.2571.2572.2575.2577.2583.2584.2596.2604.2605.2608.2609.2610.2612.2614.2621.2628.2629.2633.2636.2642.2643.2645.2646.2650.2651.2652.2656.2657.2658.2660.2661.2669.2670.2677.2681.2684.2687.2690.2695.2698.2713.2714.2729.2739.2767.2768.2770.2772.2784.2787.2791.2792.2798.2801.2805.2812.2813.2816.2817.2821.2822.2827.2830.2831.2834.2838.2839.2844.2846.2849.2850.2852.2854.2860.2862.2863.2865.2867.2869.2873.2874.2875.2876.2878.2880.2881.2882.2883.2884.2886.2887.2888.2889.2891.2893.2894.2895.2897.2898.2900.2901.2908.2909.2916.2917.2918.2919.2920.2922.2923.2927.2929.2930.2931.2940.2941.2947.2949.2950.2956.2958.2961.2963.2964.2965.2966.2968.2973.2975.2979.2980.2981.2983.2985.2986.2987.2994.2995.2997.2999.3000.3002.3003.3005.3008.3009.3010.3012.3016.3017.3018.3019.3024.3025.3028.3034.3038.3043.3048.3052.3053.3055.3058.3059.3063.3066.3068.3070.3073.3074.3075.3076.3077.3078.3089.3090.3093.3094.3095.3097.3099.3100.3106.3109.3112.3117.3119.3126.3127.3128.3130.3135.3136.3145.3150.3151.3154.3155.3163.3167.3172.3173.3182.3183.3184.3185.3187.3188.3189.3190.3194.3196.3209.3210.3211.3214.3215.3217.3219.3222.3223.3225.3226.3227.3228.3230.3231.3234.3235.3236.3237.3238.3240.3244.3245.3250.3251.3253.3257.3260.3270.3272.3281.3288.3290.3292.3293.3296.3299.3300.3306.3307.3309.3314.3315.3316.3318.3324.3328.3330.3331.3531.3731.3831.3931.4131.4531.4631.4731.4831.5231.6931.7031.7235.7831.7931.8931.9731.10231.10631.10831.11031.11531.12831.13632.13731.14237.15731.16831.21233.23031.24431.24533.25731.25931.26031.26831; OptanonAlertBoxClosed=2024-04-03T09:33:13.721Z; ab.storage.deviceId.6e55efa5-e689-47c3-a55b-e6d7515a6c5d=%7B%22g%22%3A%22e41c694f-1e75-f26a-63ad-4b2572625d16%22%2C%22c%22%3A1712136793764%2C%22l%22%3A1712136793764%7D; ab.storage.sessionId.6e55efa5-e689-47c3-a55b-e6d7515a6c5d=%7B%22g%22%3A%22053fbe38-bc1c-5017-a3d4-593be55e1822%22%2C%22e%22%3A1712136808980%2C%22c%22%3A1712136793763%2C%22l%22%3A1712136793980%7D; eupubconsent-v2=CP8fP7AP8fP7AAcABBENAuEsAP_gAEPgACiQg1NX_D5ebWti8XZUIbtkaYwP55izokQhBhaIEewFwAOG7BgCB2EwNAV4JiACGBAEkiLBAQNlHABUCQAAAIgRiSCMYkWMgTNKJKBAiFMRO0NYCBxmmgFDWQCY5kosszdxmDeAAAIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAIAAAAAAAAAAAAAAAAAAAAA_cff79LgAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAEQaoMoACIAFAAXAA4ADwAKgAXAA4AB4AEAAJAAXwAxADKAGgAagA8AB-AEQAJgAUwAqwBcAF0AMQAaAA3gB-AEJAIgAiQBHACWAE0AMAAYYAywBmgDZAHIAPiAfYB-wD_AQAAg4BEYCLAIwARqAjgCOgEiAJKAT8AqABcwC8gF9AMUAZ8A0QBrwDaAG4AOkAdsA-wB_wEIAImAReAj0BIgCVgExQJkAmUBOwCh4FIAUiApMBUgCrAFZAK7gWIBYoC0YFsAWyAt0BcgC6AF2gLvgXkBeYC-gGCANsgm2CbkE3gTfAnDBOUE5gJ0gTrgnaCdwE8AJ5gT7gn6CfwFAAKCBBqAKBACQAdABcAGyARAAwgCdAFyANsAgcEADAA6AFcARAAwgCdAIHBgA4AOgAuADZAIgAYQBcgEDhAAcAHQA2QCIAGEAToAuQCBwoAGAFwAwgEDhgAIAwgEDhwAYAHQBEADCAJ0AgcBFcgACAMIBA4kADAIgAYQCBxQAKADoAiABhAE6AQO.f_wACHwAAAAA; WLRedir=requested; ServerPool=C; _abck=4E46EAE3EB2D66226290833C84E8E3F1~0~YAAQNIQVAvXDkJiOAQAAEKpNowsJh3TyF1Hg3kjNSWZfKg7D6Mm8FdsbLXuIlZxJdGXi//yBdZZmfxkcyrHc1mQgSJZa+Nbj4YEu1wGrT7urPPmFK6hfKHgO62RyG7DbXbVQAOzGowodh4+N/v0LG6+afUjOBHeHbsdYVCIxCbxnOlGO/NRYTnxrUrSS6Cdf105mNqIBt2mF6pexg89EJ/Ony/h3WoyMnFlwmBS5dxX0gjLuGglpJKSGrFI5pSWwJ/aKgiFn0W1w5QGLo3liPzpsk/4NWH41kvjCIC2TI65Knc4S176pFqQCKnmB56kjR9n7btsTVx3ACPJcbcZDo+tBXrWc0RhtWATKxxISx6qO90vs83keF7BHKOkFMQPd7Ti+D41mvYe2v64gd1ZIuqg5AxVmy5E47hnP23vf~-1~-1~-1; bm_sz=44954CE0901AA17664B8AA48E49D881A~YAAQNIQVAvbDkJiOAQAAEKpNoxcvTwrYoNlWUlyjn0cwBLgNlPqNKxcDE1MZze7q8bjgaHcso9uYwSywephmqfyB8sUI/KlhA0LF47TukkmZc8giTGCNr/k85+r7Aj9+4UR2HN7YCE24FVL5U4bdmabhHV9PNL7LdbphTwNssl4BfhJba1wvlUlSnj9+DIDmRf7l+ThFk1OIFGrTZqGhybmpzcKV0wLC34TTZqxoxkLTZf7RwlBzUEED/qV2WQYaOhDzfwDDNLGr7C/B+T2DClMo8CHWWaM4ZgfX2c9Ll8U/wBvcvkXj9Yc0tvYFT0jq5ALZi+SAHx9rHxMZKZ92PAnRrjTtuy8Ty6tnHrIpWaQ3PIJ3G0IOr69iCBeaA2ahPB/0Mdk8pE99MNZxDGfATtNh3oHp~3294009~3289393; PMC=V2*MS.83*MD.20240403*LD.20240403; TADCID=uuwMgQMuKDVKdB_ZABQCmq6heh9ZSU2yA8SXn9Wv5HylCeAkslblicHCO14lSifEzyL06otPHsHeHIcfBJ3L9FXyfwRnL1hqsrk; TART=%1%enc%3ABJTZdMWv36ola8IZiqN57WaSOsabLe5zZ6jBx62mYZFYmKw8I6xCYWYp7i%2FU1XocwC4pukaKaBg%3D; TASameSite=1; TAUnique=%1%enc%3A1Celg%2FVpygkTYrU97r4mwz7uPiPFbod5R%2BI46KqisvHe7dtcvDqDPdBhuModLpNONox8JbUSTxk%3D; TASSK=enc%3AACL3KUA1l5pIJaglfLgaejn%2BCTUupOGKhBvohzuxPEv3CW%2FzvNdEGiWbNj5EEWKDkAKgO0pA6Zvlwnl1PEL%2BeaQ65aFSCd5DgDR0MjYI4oYMQiFbeG9nb70AzYp%2FeFbbWw%3D%3D; VRMCID=%1%V1*id.13091*llp.%2Fdevelopers*e.1704187563215",
                    'Cache-Control': 'no-cache',
                    'Accept-Encoding': 'gzip, deflate, br',
                    'User-Agent':
                        'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
                },
                body: {
                    filterLang: 'fr',
                    changeSet: 'REVIEW_LIST',
                },
            },
            isResponseValid: (res) => !res?.errorMessage && !res?.includes('geo.captcha-delivery.com'),
            crawlerCount: 1,
            retries: 1,
        });
        expect(result.status).toBe(200);
    }, 60000);

    it('should fetch tripadvisor cookie', async () => {
        const result = await fetchUntilWorks({
            params: {
                url: 'https://api-js.datadome.co/js/',
                headers: {
                    'Content-type': 'application/x-www-form-urlencoded; charset=utf-8',
                },
                method: 'POST',
                body: {
                    ddk: '4980A61279181687DE605B235F81B9',
                    Referer: 'https%3A%2F%2Fwww.thefork.fr%2F',
                },
                timeout: 5000,
            },
            isResponseValid: (res) => !!(res?.status === 200 && res?.cookie),
            retries: 1,
        });
        console.log(result);
    }, 60000);

    it('should fetch tripadvisor rating', async () => {
        const result = await fetchUntilWorks({
            params: {
                url: `${platformsKeys.TRIPADVISOR.baseUrl}/data/1.0/restaurant/${33668457}/overview`,
                headers: Config.platforms.tripadvisor.api.headers,
            },
            isResponseValid: (res) => res?.name,
        });

        console.log(result);
    }, 60000);

    it('should fetch tripadvisor reviews', async () => {
        const body = getPreferredReviewsQuery('33668457', 0);

        const result = await fetchUntilWorks({
            params: {
                method: 'POST',
                url: 'https://www.tripadvisor.fr/data/graphql/ids',
                headers: {
                    'Content-Type': 'application/json',
                    Accept: '*/*',
                    'Accept-Language': 'fr-FR,fr;q=0.9',
                    'User-Agent':
                        'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.5 Safari/605.1.15',
                    Cookie: 'TAUnique=%1%enc%3ZDijZOAIJ38FEIUIUHFE2;', // Looks mandatory but random value seems to work
                },
                body,
            },
            isResponseValid: (res) => !!res?.data?.ReviewsProxy_getReviewListPageForLocation,
        });

        console.log(result);
    }, 60000);

    it.only('should fetch yelp reviews', async () => {
        const result = await getReviewsData({ socialId: 'DNohs_5mLVLtKc8kRnmXbw' }, true);

        console.log(result);
    }, 60000);
});
