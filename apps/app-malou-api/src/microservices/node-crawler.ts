import { <PERSON>raw<PERSON> } from '@malou-io/package-crawlers';
import type { NodeCrawlerEvent } from '@malou-io/package-service-interfaces';

/**
 * Launches multiple AWS Lambda-based crawlers concurrently to fetch a valid response.
 * Each crawler runs the same request in parallel. If none of them returns a valid result,
 * the crawlers are restarted and the process is retried up to the configured number of retries.
 *
 * @template T - The expected response data type.
 * @param {Object} options - The crawler configuration options.
 * @param {NodeCrawlerEvent} options.params - Parameters for the crawler Lambda (must include a valid URL).
 * @param {number} [options.retries=3] - Number of retry cycles after the initial attempt.
 *   - `0` means there will be one single attempt (no retries).
 *   - The total number of attempts is `retries + 1`.
 * @param {number} [options.crawlerCount=1] - Number of crawler Lambdas to run in parallel.
 *   - Must be at least `1` and cannot exceed the available crawler pool.
 * @returns {Promise<T>} Resolves with the first valid response (`isResponseValid` returns `true`),
 *   or rejects if all retries fail to produce a valid response.
 *
 * @throws {AssertionError} If `params.url` is missing.
 * @throws {Error} If no crawler returns a valid result after all attempts.
 *
 * @example
 * ```ts
 * const crawler = new Crawler<MyResponse>((res) => res.status === 'ok');
 * const data = await crawler.fetchUntilWorks({
 *   params: { url: 'https://example.com/api' },
 *   retries: 2,          // Will try up to 3 times total
 *   crawlerCount: 3,     // Launches 3 crawlers in parallel
 * });
 * ```
 */
export async function fetchUntilWorks<T = any>({
    params,
    isResponseValid,
    retries = 3,
    crawlerCount = 3,
}: {
    params: NodeCrawlerEvent;
    isResponseValid: (res: T) => boolean;
    retries?: number;
    crawlerCount?: number;
}) {
    const crawler = new Crawler<T>(isResponseValid);
    return crawler.fetchUntilWorks({ params, retries, crawlerCount });
}
