import type { AxiosRequestHeaders } from 'axios';

type UberEatsManagerApiLocationInfo = {
    name: string;
    contact?: {
        phoneNumber: string;
        emailAddress?: string;
    };
    location?: {
        longitude: number;
        latitude: number;
        address: {
            address1: string;
            city?: string;
            country?: string;
            postalCode?: string;
        };
    };
};

export type UberEatsManagerApiGetStoreResponse = {
    data:
        | {
              uuid: string;
              locationInfo: UberEatsManagerApiLocationInfo;
          }
        | undefined;
};

export type UberEatsManagerApiGetHolidayHoursResponse = {
    data:
        | {
              holidayHoursMap: UberEatsManagerApiHolidayHours;
              status: 'success';
          }
        | undefined;
};

export interface UberEatsManagerApiOpenTimePeriods {
    hourRecords: UberEatsManagerApiOpenTimePeriod[];
}
interface UberEatsManagerApiOpenTimePeriod {
    startTime: string;
    endTime: string;
}

export type UberEatsManagerApiHolidayHours = Record<string, UberEatsManagerApiOpenTimePeriods>;

/// GRAPHQL
export type UberEatsGraphQLHeaders = Pick<
    AxiosRequestHeaders,
    | 'Content-Type'
    | 'Accept-Encoding'
    | 'Connection'
    | 'Authority'
    | 'Accept'
    | 'Accept-Language'
    | 'Origin'
    | 'User-Agent'
    | 'X-Csrf-Token'
    | 'Cookie'
    | 'Referer'
>;

export type UberEatsManagerApiSetInfoBody = {
    operationName: 'updateStoreDetails';
    query: string;
    variables: {
        updateStoreDetailsInput: {
            restaurantUUID: string;
            locationInfo: UberEatsManagerApiLocationInfo;
        };
    };
};
