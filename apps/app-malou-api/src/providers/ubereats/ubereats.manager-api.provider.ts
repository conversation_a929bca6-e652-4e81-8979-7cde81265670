import { singleton } from 'tsyringe';

import { isNotNil, MalouErrorCode, PlatformKey } from '@malou-io/package-utils';

import { Config } from ':config';
import { MalouError } from ':helpers/classes/malou-error';
import { fetchUntilWorks } from ':microservices/node-crawler';
import UberEatsCredentialsRepository from ':modules/credentials/platforms/ubereats/ubereats.repository';
import {
    UberEatsGraphQLHeaders,
    UberEatsManagerApiGetHolidayHoursResponse,
    UberEatsManagerApiGetStoreResponse,
    UberEatsManagerApiHolidayHours,
    UberEatsManagerApiSetInfoBody,
} from ':providers/ubereats/ubereats.manager-api.provider.interfaces';
import { UberEatsStoreInfoInput } from ':providers/ubereats/ubereats.provider.interfaces';

@singleton()
export class UberEatsManagerApiProvider {
    /**
     *
     *  This is the UberEats Unofficial API provider
     *  We're using a cookie to act as if were were connected to https://merchants.ubereats.com/manager/stores
     *  using "<EMAIL>"
     *
     */
    constructor(private readonly _ubereatsCredentialsRepository: UberEatsCredentialsRepository) {}

    async getStore(storeId: string): Promise<UberEatsManagerApiGetStoreResponse['data'] | undefined> {
        const headers = await this._getHeaders();
        const url = `${Config.platforms.ubereats.api.managerUrl}/getBusinessLocation?localeCode=en`;
        const res = await this._makeRequest<UberEatsManagerApiGetStoreResponse>({
            url,
            headers,
            body: {
                businessLocationUUID: storeId,
            },
            isResponseValid: (response) => isNotNil(response?.data?.uuid),
        });

        return res?.data;
    }

    async getHolidayHours(storeId: string): Promise<UberEatsManagerApiHolidayHours | undefined> {
        const headers = await this._getHeaders();
        const url = `${Config.platforms.ubereats.api.managerUrl}/getHolidayHours?localeCode=en`;
        const res = await this._makeRequest<UberEatsManagerApiGetHolidayHoursResponse>({
            url,
            headers,
            body: {
                uuid: storeId,
            },
            isResponseValid: (response) => response?.status === 'success',
        });

        return res?.data?.holidayHoursMap;
    }

    async setHolidayHours({ storeId, holidayHours }: { storeId: string; holidayHours: UberEatsManagerApiHolidayHours }): Promise<void> {
        const headers = await this._getHeaders();
        const url = `${Config.platforms.ubereats.api.managerUrl}/setHoursOverrides?localeCode=en`;
        await this._makeRequest({
            url,
            headers,
            body: {
                restaurantUUID: storeId,
                hoursOverridesMap: holidayHours,
            },
            isResponseValid: (res) => res?.status === 'success',
        });
    }

    async setStoreInfo({ storeId, info }: { storeId: string; info: Required<UberEatsStoreInfoInput> }): Promise<void> {
        const referer = `https://merchants.ubereats.com/manager/stores/${storeId}/edit-store-information`;
        const headers = await this._getGqlHeaders(referer);
        await this._makeRequest({
            url: Config.platforms.ubereats.api.graphQlUrl,
            headers,
            body: this._getSetStoreInfoGraphQLBody({ storeId, info }),
            isResponseValid: (res) => res?.data?.updateStoreDetails?.locationInfo?.name === info.name,
        });
    }

    private _getSetStoreInfoGraphQLBody({
        storeId,
        info,
    }: {
        storeId: string;
        info: Required<UberEatsStoreInfoInput>;
    }): UberEatsManagerApiSetInfoBody {
        return {
            operationName: 'updateStoreDetails',
            query: `
                mutation updateStoreDetails($updateStoreDetailsInput: UpdateStoreDetailsInput!) {
                    updateStoreDetails(updateStoreDetailsInput: $updateStoreDetailsInput) {
                        locationInfo {
                            name
                            __typename
                        }
                        __typename
                    }
                }
            `,
            variables: {
                updateStoreDetailsInput: {
                    restaurantUUID: storeId,
                    locationInfo: {
                        name: info.name,
                        contact: {
                            phoneNumber: info.contact.phone_number,
                            ...(info.contact.email && { emailAddress: info.contact.email }),
                        },
                        location: {
                            longitude: Number(info.location.longitude),
                            latitude: Number(info.location.latitude),
                            address: {
                                address1: info.location.street_address_line_one ?? '',
                                ...(info.location.city && { city: info.location.city }),
                                ...(info.location.country && { country: info.location.country }),
                                ...(info.location.postal_code && { postalCode: info.location.postal_code }),
                            },
                        },
                    },
                },
            },
        };
    }

    private _getHeaders = async (): Promise<{ cookie: string; 'x-csrf-token': string }> => {
        const credentials = await this._ubereatsCredentialsRepository.findOne({
            filter: { key: 'ubereats-malou' },
            options: { lean: true },
        });

        if (!credentials || !credentials.cookie) {
            throw new MalouError(MalouErrorCode.NOT_FOUND, {
                message: 'Cannot find credential',
                metadata: {
                    platform: PlatformKey.UBEREATS,
                    credentials,
                },
            });
        }

        return {
            cookie: credentials.cookie,
            'x-csrf-token': 'x',
        };
    };

    private async _getGqlHeaders(referer: string): Promise<UberEatsGraphQLHeaders> {
        const credentials = await this._ubereatsCredentialsRepository.findOne({
            filter: { key: 'ubereats-malou' },
            options: { lean: true },
        });

        if (!credentials || !credentials.cookie) {
            throw new MalouError(MalouErrorCode.NOT_FOUND, {
                message: 'Cannot find credential',
                metadata: {
                    platform: PlatformKey.UBEREATS,
                    credentials,
                },
            });
        }

        return {
            ...Config.platforms.ubereats.api.headers,
            Cookie: credentials.cookie,
            Referer: referer,
        };
    }

    private async _makeRequest<T>({
        url,
        headers,
        body,
        isResponseValid,
        retries = 1,
    }: {
        url: string;
        headers: object;
        body: object;
        isResponseValid: (res: any) => boolean;
        retries?: number;
    }): Promise<T | undefined> {
        const response = await fetchUntilWorks<T>({
            params: {
                url,
                method: 'POST',
                headers,
                body,
            },
            isResponseValid,
            retries,
            crawlerCount: 1,
        });

        return response;
    }
}
