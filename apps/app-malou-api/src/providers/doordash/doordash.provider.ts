import axios, { AxiosResponse } from 'axios';
import { DateTime } from 'luxon';
import assert from 'node:assert/strict';
import { singleton } from 'tsyringe';

import { isFulfilled, MalouErrorCode, PlatformKey } from '@malou-io/package-utils';

import { Config } from ':config';
import { MalouError } from ':helpers/classes/malou-error';
import { logger } from ':helpers/logger';
import { isRejected } from ':helpers/utils';
import DoorDashCredentialsRepository from ':modules/credentials/platforms/doordash/doordash.repository';
import {
    DoorDashFullReplyPayload,
    DoorDashHours,
    DoorDashOverviewDataResponse,
    DoorDashProviderPort,
    DoorDashReplyResponse,
    DoorDashReviews,
} from ':providers/doordash/doordash.provider.interface';
import { ProviderMetricsService } from ':providers/provider.metrics.service';

@singleton()
export class DoorDashProvider implements DoorDashProviderPort {
    readonly proxy = {
        host: Config.services.brightData.proxyHost as string,
        port: Config.services.brightData.residentialProxyUSPort as number,
        protocol: 'http',
        auth: {
            username: Config.services.brightData.residentialProxyUSUsername as string,
            password: Config.services.brightData.residentialProxyUSPassword as string,
        },
    };

    constructor(
        private readonly _providerMetricsService: ProviderMetricsService,
        private readonly _doordashCredentialsRepository: DoorDashCredentialsRepository
    ) {}

    async getReviews({ storeId, businessId }: { storeId: string; businessId: string }): Promise<DoorDashReviews> {
        assert(storeId, 'storeId is required');
        assert(businessId, 'businessId is required');

        // DoorDash limits reviews from 3 months ago, so no need to fetch more
        const today = DateTime.now().toJSDate();
        const fourMonthAgo = DateTime.now().minus({ months: 3 }).toJSDate();

        const body = {
            storeIds: [storeId],
            businessIds: [businessId],
            dateRange: {
                startDate: fourMonthAgo.toISOString(),
                endDate: today.toISOString(),
            },
            filters: [],
            limit: 3000,
            offset: 0,
        };
        const response = await this._providerMetricsService.callAndTrackExternalAPI({
            hostId: 'doordash',
            requestId: 'reviews.get',
            request: async () =>
                this._fetchWithRetry<DoorDashReviews>({
                    url: 'https://merchant-portal.doordash.com/merchant-analytics-service/api/v1/consumer_feedback/reviews',
                    method: 'post',
                    body,
                    functionName: 'getReviews',
                }),
        });
        if (response.status !== 200) {
            throw new MalouError(MalouErrorCode.DOORDASH_REVIEWS_FETCH_ERROR, {
                metadata: {
                    storeId,
                    businessId,
                    status: response.status,
                    statusText: response.statusText,
                },
            });
        }
        return response.data;
    }

    async getHours({ storeId }: { storeId: string }): Promise<DoorDashHours> {
        assert(storeId, 'storeId is required');

        const url = `https://merchant-portal.doordash.com/merchant-data-service/api/v1/stores/${storeId}/aggregatedMenuHours?activeOnly=false`;
        const response = await this._providerMetricsService.callAndTrackExternalAPI({
            hostId: 'doordash',
            requestId: 'hours.get',
            request: async () =>
                this._fetchWithRetry<DoorDashHours>({
                    url,
                    method: 'get',
                    functionName: 'getHours',
                }),
        });
        if (response.status !== 200) {
            throw new MalouError(MalouErrorCode.DOORDASH_HOURS_FETCH_ERROR, {
                metadata: {
                    storeId,
                    status: response.status,
                    statusText: response.statusText,
                },
            });
        }
        return response.data;
    }

    // TODO: handle DoorDash answer reply if we manage to get something else than a 403
    async replyReview({
        storeId,
        businessName,
        storeAddress,
        rating,
        businessId,
        replyText,
        reviewId,
        userId,
        consumerId,
    }: DoorDashFullReplyPayload): Promise<boolean> {
        assert(storeId, 'storeId is required');
        assert(businessName, 'businessName is required');
        assert(storeAddress, 'storeAddress is required');
        assert(rating >= 1 && rating <= 5, 'rating must be between 1 and 5');
        assert(businessId, 'businessId is required');
        assert(replyText, 'replyText is required');
        assert(reviewId, 'reviewId is required');
        assert(userId, 'userId is required');
        assert(consumerId, 'consumerId is required');

        const url = 'https://merchant-portal.doordash.com/merchant-analytics-service/api/v1/consumer_feedback/send_response';
        const body = {
            messageContent: {
                cx_complaint: '',
                mx_response: replyText,
                business_name: businessName,
                order_date: null,
                order_items: null,
                complaint_store_id: storeId,
                complaint_store_address: storeAddress,
                merchant_rating: rating,
                review_tags: [],
                promo_value: 0,
            },
            storeIds: [storeId],
            businessIds: [businessId],
            recipientConsumerId: consumerId,
            complaintStoreId: storeId,
            recipientUserId: userId,
            orderId: null,
            complaintId: reviewId,
            experience: 1,
            sendPromoExperience: true,
        };

        const response = await this._providerMetricsService.callAndTrackExternalAPI({
            hostId: 'doordash',
            requestId: 'reviews.reply',
            request: async () =>
                this._fetchWithRetry<DoorDashReplyResponse>({
                    url,
                    method: 'post',
                    body,
                    functionName: 'replyReview',
                    retryCount: 3,
                }),
        });
        if (response.status !== 200) {
            throw new Error(`Failed to fetch hours: ${response.statusText}`);
        }
        return response.data.success;
    }

    async getStoreDetails({ storeId }: { storeId: string }): Promise<DoorDashOverviewDataResponse> {
        assert(storeId, 'storeId is required');

        const url = `https://merchant-portal.doordash.com/merchant-data-service/api/v1/stores/${storeId}`;
        const response = await this._providerMetricsService.callAndTrackExternalAPI({
            hostId: 'doordash',
            requestId: 'storeDetails.get',
            request: async () =>
                this._fetchWithRetry<DoorDashOverviewDataResponse>({
                    url,
                    method: 'get',
                    functionName: 'getStoreDetails',
                }),
        });
        if (response.status !== 200) {
            throw new MalouError(MalouErrorCode.DOORDASH_STORE_DETAILS_FETCH_ERROR, {
                metadata: {
                    storeId,
                    status: response.status,
                    statusText: response.statusText,
                },
            });
        }
        return response.data;
    }

    // TODO: Uncomment when we'll do update on DoorDash ourselves + add update functions for name, address, etc.
    // async setRegularHours({
    //     storeId,
    //     menuId,
    //     regularHours,
    // }: {
    //     storeId: string;
    //     menuId: string;
    //     regularHours: DoorDashRegularHoursPayload;
    // }): Promise<boolean> {
    //     assert(storeId, 'storeId is required');
    //     assert(menuId, 'menuId is required');
    //     assert(regularHours.length === 7, 'regularHours must have 7 days of hours');

    //     const url = 'https://merchant-portal.doordash.com/mx-menu-tools-bff/graphql?operation=updateStoreMenuLink';
    //     const body = {
    //         operationName: 'updateStoreMenuLink',
    //         variables: {
    //             storeId,
    //             menuId,
    //             openHours: [
    //                 { dayIndex: 0, startTime: '07:30:00', endTime: '15:45:00' },
    //                 { dayIndex: 1, startTime: '07:30:00', endTime: '15:45:00' },
    //                 { dayIndex: 2, startTime: '07:30:00', endTime: '15:45:00' },
    //                 { dayIndex: 3, startTime: '07:30:00', endTime: '15:45:00' },
    //                 { dayIndex: 4, startTime: '07:30:00', endTime: '15:45:00' },
    //                 { dayIndex: 5, startTime: '07:30:00', endTime: '15:45:00' },
    //                 { dayIndex: 6, startTime: '07:30:00', endTime: '15:45:00' },
    //             ],
    //         },
    //         query: 'mutation updateStoreMenuLink($storeId: ID!, $menuId: ID!, $openHours: [OpenHoursInput!]) {\n  updateStoreMenuLink(\n    initialStoreId: $storeId\n    initialMenuId: $menuId\n    newStoreId: $storeId\n    newMenuId: $menuId\n    openHours: $openHours\n  )\n}\n',
    //     };

    //     const response = await this._providerMetricsService.callAndTrackExternalAPI({
    //         hostId: 'doordash',
    //         requestId: 'regularHours.set',
    //         request: async () =>
    //             this._fetchWithRetry({
    //                 url,
    //                 method: 'post',
    //                 body,
    //                 functionName: 'setRegularHours',
    //                 retryCount: 3,
    //             }),
    //     });
    //     return !!response.data; // TODO: check response type for success validation
    // }

    // async setNewSpecialHours({ storeId, newHours }: { storeId: string; newHours: DoorDashSpecialHourPayload[] }): Promise<boolean> {
    //     assert(storeId, 'storeId is required');
    //     assert(newHours && newHours.length > 0, 'newHours must be provided and not empty');

    //     const url = `https://merchant-portal.doordash.com/merchant-data-service/api/v1/stores/${storeId}/special_hours`;
    //     const body = {
    //         specialHoursList: newHours,
    //     };
    //     const response = await this._providerMetricsService.callAndTrackExternalAPI({
    //         hostId: 'doordash',
    //         requestId: 'specialHours.set',
    //         request: async () =>
    //             this._fetchWithRetry({
    //                 url,
    //                 method: 'post',
    //                 body,
    //                 functionName: 'setNewSpecialHours',
    //                 retryCount: 3,
    //             }),
    //     });
    //     return !!response.data; // TODO: check response type for success validation
    // }

    private async _getCookies(): Promise<string[]> {
        const credentials = await this._doordashCredentialsRepository.getSuperCredentials();
        if (!credentials?.[0]?.cookie) {
            throw new MalouError(MalouErrorCode.CREDENTIALS_DOORDASH_COOKIE_NOT_FOUND, {
                metadata: { message: 'Credential not found for DoorDash platform', platform: PlatformKey.DOORDASH },
            });
        }
        return credentials.map((cred) => cred.cookie);
    }

    private async _fetchWithRetry<T>({
        url,
        method = 'post',
        body,
        functionName,
        retryCount = 3,
    }: {
        url: string;
        method?: 'get' | 'post' | 'put';
        body?: any;
        functionName: 'getReviews' | 'getHours' | 'replyReview' | 'setRegularHours' | 'setNewSpecialHours' | 'getStoreDetails';
        retryCount?: number;
    }): Promise<AxiosResponse<T>> {
        let res;
        let index = 0;
        let lastErr;
        while (!res && index <= retryCount) {
            try {
                res = await this._fetchWithAxios({ url, body, method });
            } catch (err) {
                logger.error('DoorDashProvider: Error while fetching', {
                    functionName,
                    index,
                    url,
                    method,
                    body,
                    error: err,
                });
                lastErr = err;
                res = null;
            }
            index += 1;
        }
        if (index > 1) {
            logger.warn('DoorDashProvider: Retried multiple times for function', {
                functionName,
                index,
                url,
                method,
                body,
            });
        }
        if (!res && lastErr) {
            throw lastErr;
        }
        return res;
    }

    private async _fetchWithAxios<T>({
        url,
        method = 'post',
        body,
    }: {
        url: string;
        method?: 'get' | 'post' | 'put';
        body?: any;
    }): Promise<AxiosResponse<T>> {
        const cookies = await this._getCookies();
        const responses = await Promise.allSettled(
            cookies.map((cookie) =>
                axios({
                    method,
                    url,
                    data: body,
                    headers: {
                        'Content-Type': 'application/json',
                        Cookie: cookie,
                        'apollographql-client-name': 'APP-MERCHANT',
                    },
                    timeout: 60_000,
                    proxy: this.proxy,
                })
            )
        );
        const fulfilledResponse = responses.find(isFulfilled);
        if (fulfilledResponse) {
            return fulfilledResponse.value;
        }
        logger.error('DoorDashProvider: All requests failed', {
            url,
            method,
            body,
            errors: responses.filter((res) => isRejected(res)).map((res) => res.reason),
        });
        throw responses.find((res) => isRejected(res))?.reason;
    }
}
