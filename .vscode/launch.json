{"configurations": [{"name": "Angular debug", "request": "launch", "skipFiles": ["<node_internals>/**"], "type": "chrome", "url": "http://localhost:4200", "webRoot": "${workspaceFolder}/apps/app-malou-web"}, {"env": {"FORCE_COLOR": "1"}, "experimentalNetworking": "off", "name": "start local", "nodeVersionHint": 22.15, "outputCapture": "std", "request": "launch", "runtimeArgs": ["run", "start-local"], "runtimeExecutable": "pnpm", "runtimeVersion": "22.15.0", "smartStep": true, "type": "node"}, {"env": {"FORCE_COLOR": "1"}, "experimentalNetworking": "off", "name": "start local (light)", "nodeVersionHint": 22.15, "outputCapture": "std", "request": "launch", "runtimeArgs": ["run", "start-local-light"], "runtimeExecutable": "pnpm", "runtimeVersion": "22.15.0", "smartStep": true, "type": "node"}, {"env": {"FORCE_COLOR": "1"}, "experimentalNetworking": "off", "name": "start local (api)", "nodeVersionHint": 22.15, "outputCapture": "std", "request": "launch", "runtimeArgs": ["run", "start-local-api-light"], "runtimeExecutable": "pnpm", "runtimeVersion": "22.15.0", "smartStep": true, "type": "node"}, {"env": {"FORCE_COLOR": "1"}, "experimentalNetworking": "off", "name": "start dev", "nodeVersionHint": 22.15, "outputCapture": "std", "request": "launch", "runtimeArgs": ["run", "start-dev"], "runtimeExecutable": "pnpm", "runtimeVersion": "22.15.0", "smartStep": true, "type": "node"}, {"env": {"FORCE_COLOR": "1"}, "experimentalNetworking": "off", "name": "start dev (light)", "nodeVersionHint": 22.15, "outputCapture": "std", "request": "launch", "runtimeArgs": ["run", "start-dev-light"], "runtimeExecutable": "pnpm", "runtimeVersion": "22.15.0", "smartStep": true, "type": "node"}, {"env": {"FORCE_COLOR": "1"}, "experimentalNetworking": "off", "name": "start dev (api)", "nodeVersionHint": 22.15, "outputCapture": "std", "request": "launch", "runtimeArgs": ["run", "start-dev-api-light"], "runtimeExecutable": "pnpm", "runtimeVersion": "22.15.0", "smartStep": true, "type": "node"}, {"env": {"FORCE_COLOR": "1"}, "experimentalNetworking": "off", "name": "start staging", "nodeVersionHint": 22.15, "outputCapture": "std", "request": "launch", "runtimeArgs": ["run", "start-staging"], "runtimeExecutable": "pnpm", "runtimeVersion": "22.15.0", "smartStep": true, "type": "node"}, {"env": {"FORCE_COLOR": "1"}, "experimentalNetworking": "off", "name": "start staging (light)", "nodeVersionHint": 22.15, "outputCapture": "std", "request": "launch", "runtimeArgs": ["run", "start-staging-light"], "runtimeExecutable": "pnpm", "runtimeVersion": "22.15.0", "smartStep": true, "type": "node"}, {"env": {"FORCE_COLOR": "1"}, "experimentalNetworking": "off", "name": "start staging (api)", "nodeVersionHint": 22.15, "outputCapture": "std", "request": "launch", "runtimeArgs": ["run", "start-staging-api-light"], "runtimeExecutable": "pnpm", "runtimeVersion": "22.15.0", "smartStep": true, "type": "node"}, {"env": {"FORCE_COLOR": "1"}, "experimentalNetworking": "off", "name": "start production", "nodeVersionHint": 22.15, "outputCapture": "std", "request": "launch", "runtimeArgs": ["run", "start-production"], "runtimeExecutable": "pnpm", "runtimeVersion": "22.15.0", "smartStep": true, "type": "node"}, {"env": {"FORCE_COLOR": "1"}, "experimentalNetworking": "off", "name": "start production (light)", "nodeVersionHint": 22.15, "outputCapture": "std", "request": "launch", "runtimeArgs": ["run", "start-production-light"], "runtimeExecutable": "pnpm", "runtimeVersion": "22.15.0", "smartStep": true, "type": "node"}, {"env": {"FORCE_COLOR": "1"}, "experimentalNetworking": "off", "name": "start production (api)", "nodeVersionHint": 22.15, "outputCapture": "std", "request": "launch", "runtimeArgs": ["run", "start-production-api-light"], "runtimeExecutable": "pnpm", "runtimeVersion": "22.15.0", "smartStep": true, "type": "node"}, {"args": ["-r", "${workspaceFolder}/apps/app-malou-api/src/env.ts", "-r", "${workspaceFolder}/apps/app-malou-api/src/di.ts", "${file}"], "console": "integratedTerminal", "cwd": "${workspaceFolder}/apps/app-malou-api", "env": {"NODE_ENV": "local"}, "name": "debug task api local", "nodeVersionHint": 22.15, "outputCapture": "std", "request": "launch", "runtimeArgs": ["--<PERSON><PERSON><PERSON>", "-r", "ts-node/register/transpile-only"], "runtimeExecutable": "node", "runtimeVersion": "22.15.0", "smartStep": true, "type": "node"}, {"args": ["-r", "${workspaceFolder}/apps/app-malou-api/src/env.ts", "-r", "${workspaceFolder}/apps/app-malou-api/src/di.ts", "${file}"], "console": "integratedTerminal", "cwd": "${workspaceFolder}/apps/app-malou-api", "env": {"NODE_ENV": "development"}, "name": "debug task api dev", "nodeVersionHint": 22.15, "outputCapture": "std", "request": "launch", "runtimeArgs": ["--<PERSON><PERSON><PERSON>", "-r", "ts-node/register/transpile-only"], "runtimeExecutable": "node", "runtimeVersion": "22.15.0", "smartStep": true, "type": "node"}, {"args": ["-r", "${workspaceFolder}/apps/app-malou-api/src/env.ts", "-r", "${workspaceFolder}/apps/app-malou-api/src/di.ts", "${file}"], "console": "integratedTerminal", "cwd": "${workspaceFolder}/apps/app-malou-api", "env": {"NODE_ENV": "staging"}, "name": "debug task api staging", "nodeVersionHint": 22.15, "outputCapture": "std", "request": "launch", "runtimeArgs": ["--<PERSON><PERSON><PERSON>", "-r", "ts-node/register/transpile-only"], "runtimeExecutable": "node", "runtimeVersion": "22.15.0", "smartStep": true, "type": "node"}, {"args": ["-r", "${workspaceFolder}/apps/app-malou-api/src/env.ts", "-r", "${workspaceFolder}/apps/app-malou-api/src/di.ts", "${file}"], "console": "integratedTerminal", "cwd": "${workspaceFolder}/apps/app-malou-api", "env": {"NODE_ENV": "production"}, "name": "debug task api prod", "nodeVersionHint": 22.15, "outputCapture": "std", "request": "launch", "runtimeArgs": ["--<PERSON><PERSON><PERSON>", "-r", "ts-node/register/transpile-only"], "runtimeExecutable": "node", "runtimeVersion": "22.15.0", "smartStep": true, "type": "node"}, {"args": "fixtures/entities/categories.ts", "cwd": "${workspaceFolder}/apps/app-malou-api", "env": {"NODE_ENV": "local"}, "name": "debug fixture api", "nodeVersionHint": 22.15, "outputCapture": "std", "request": "launch", "runtimeArgs": ["--<PERSON><PERSON><PERSON>", "-r", "ts-node/register/transpile-only"], "runtimeExecutable": "node", "runtimeVersion": "22.15.0", "smartStep": true, "type": "node"}, {"args": ["${relativeFile}"], "cwd": "${workspaceRoot}", "internalConsoleOptions": "openOnSessionStart", "name": "ts-node", "request": "launch", "runtimeArgs": ["-r", "ts-node/register"], "type": "node"}, {"cwd": "${workspaceRoot}/apps/app-malou-e2e", "env": {"PLAYWRIGHT_WORKER_COUNT": "2", "TESTING_TARGET": "production"}, "experimentalNetworking": "off", "name": "Playwright production", "nodeVersionHint": 22.15, "request": "launch", "runtimeArgs": ["run", "playwright-ui"], "runtimeExecutable": "pnpm", "runtimeVersion": "22.15.0", "type": "node"}, {"cwd": "${workspaceRoot}/apps/app-malou-e2e", "env": {"PLAYWRIGHT_WORKER_COUNT": "2", "TESTING_TARGET": "staging"}, "experimentalNetworking": "off", "name": "Playwright staging", "nodeVersionHint": 22.15, "request": "launch", "runtimeArgs": ["run", "playwright-ui"], "runtimeExecutable": "pnpm", "runtimeVersion": "22.15.0", "type": "node"}, {"cwd": "${workspaceRoot}/apps/app-malou-e2e", "env": {"PLAYWRIGHT_WORKER_COUNT": "2", "TESTING_TARGET": "development"}, "experimentalNetworking": "off", "name": "Playwright development", "nodeVersionHint": 22.15, "request": "launch", "runtimeArgs": ["run", "playwright-ui"], "runtimeExecutable": "pnpm", "runtimeVersion": "22.15.0", "type": "node"}, {"cwd": "${workspaceRoot}/apps/app-malou-e2e", "env": {"PLAYWRIGHT_WORKER_COUNT": "2", "TESTING_TARGET": "local"}, "experimentalNetworking": "off", "name": "Playwright local", "nodeVersionHint": 22.15, "request": "launch", "runtimeArgs": ["run", "playwright-ui"], "runtimeExecutable": "pnpm", "runtimeVersion": "22.15.0", "type": "node"}, {"cwd": "${workspaceRoot}/apps/app-malou-e2e", "env": {"PLAYWRIGHT_WORKER_COUNT": "2", "TESTING_TARGET": "local_dev"}, "experimentalNetworking": "off", "name": "Playwright local_dev", "nodeVersionHint": 22.15, "request": "launch", "runtimeArgs": ["run", "playwright-ui"], "runtimeExecutable": "pnpm", "runtimeVersion": "22.15.0", "type": "node"}], "version": "0.2.0"}