name: Deploy Maloupe

on:
    workflow_dispatch:
        inputs:
            environment:
                type: choice
                description: 'Environment'
                required: true
                default: 'development'
                options:
                    - 'development'
                    - 'production'

run-name: Deploy Maloupe to ${{ inputs.environment }}

env:
    PNPM_STORE_PATH: ~/.pnpm-store

jobs:
    deploy:
        name: Deploy
        runs-on: runs-on=${{ github.run_id }}/runner=4cpu-custom/extras=s3-cache

        steps:
            # Necessary to fetch runsOn S3 cache
            - uses: runs-on/action@v1

            - name: Checkout
              uses: actions/checkout@v4

            - name: Setup Node and PNPM
              uses: ./.github/actions/setup-node-and-pnpm

            - name: Turbo caching
              uses: actions/cache/restore@v4
              with:
                  path: .turbo
                  key: ${{ runner.os }}-turbo-${{ github.ref_name }}
                  restore-keys: |
                      ${{ runner.os }}-turbo-main
                      ${{ runner.os }}-turbo-

            - name: Install
              run: pnpm install --filter @malou-io/app-maloupe...

            - name: Build
              run: pnpm run build-${{ inputs.environment }} --filter=@malou-io/app-maloupe --cache-dir=.turbo

            - name: Configure AWS credentials
              uses: aws-actions/configure-aws-credentials@v4
              with:
                  aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
                  aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
                  aws-region: eu-west-3

            - name: Deploy to S3
              run: |
                  aws s3 sync ./apps/app-maloupe/dist s3://maloupe-${{ inputs.environment }} --acl public-read --follow-symlinks --delete

            - name: Set CloudFront distribution ID
              id: set_cloudfront_distribution
              run: |
                  if [ "${{ inputs.environment }}" == "production" ]; then
                    echo "distribution_id=E2PDXKQHCU9G9A" >> $GITHUB_OUTPUT
                  else
                    echo "distribution_id=E3C5YAI9SBSACY" >> $GITHUB_OUTPUT
                  fi

            - name: Create AWS Cloudfront invalidation
              run: |
                  aws cloudfront create-invalidation --distribution-id ${{ steps.set_cloudfront_distribution.outputs.distribution_id }} --paths "/*"
