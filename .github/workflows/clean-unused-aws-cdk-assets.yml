name: Clean AWS CDK unused assets

on:
    workflow_dispatch:
    schedule:
        - cron: '0 3 * * SUN'

env:
    PNPM_STORE_PATH: ~/.pnpm-store

jobs:
    clean-unused-assets:
        name: Clean unused AWS CDK assets
        runs-on: runs-on=${{ github.run_id }}/runner=2cpu-custom/extras=s3-cache
        steps:
            # Necessary to fetch runsOn S3 cache
            - uses: runs-on/action@v1

            - name: Checkout repository
              uses: actions/checkout@v4

            - name: Setup Node and PNPM
              uses: ./.github/actions/setup-node-and-pnpm

            - name: Install dependencies
              run: pnpm install --filter=@malou-io/aws-services

            - name: Configure AWS credentials
              uses: aws-actions/configure-aws-credentials@v4
              with:
                  aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
                  aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
                  aws-region: eu-west-3

            - name: Set env variables
              working-directory: services/aws
              run: |
                  echo "Creating .env.default file"
                  printf "%s" "${{ secrets.AWS_SERVICES_ENV_VARIABLES_PRODUCTION }}" > .env.default

            - name: Clean old AWS CDK assets
              working-directory: services/aws
              run: pnpm run clean-old-assets --confirm=false
