name: Web - deploy feature environment

on:
    workflow_dispatch:
        inputs:
            environment:
                type: choice
                description: 'Environment'
                required: true
                default: 'dev'
                options:
                    - 'dev'
                    - 'staging'
            pull_request_number:
                description: 'Pull Request Number'
                required: true

env:
    PNPM_STORE_PATH: ~/.pnpm-store
    PQINA_NPM_TOKEN: ${{ secrets.PQINA_NPM_TOKEN }}

jobs:
    deploy:
        runs-on: ubuntu-latest

        steps:
            - name: Checkout code
              uses: actions/checkout@v4

            - name: Install PNPM
              uses: pnpm/action-setup@v4
              with:
                  version: 10.6.5

            - name: Setup Node
              uses: actions/setup-node@v4
              with:
                  node-version: 22.15.0
                  cache: 'pnpm'

            - name: Setup Terraform
              uses: hashicorp/setup-terraform@v1

            - name: Set branch name
              run: |
                  BRANCH=${GITHUB_REF#refs/heads/}
                  echo "BRANCH_NAME=$(echo ${BRANCH//\//-} | sed 's/_/-/g')" >> $GITHUB_ENV
                  echo "TERRAFORM_CONFIG_FILES=infrastructure/web" >> $GITHUB_ENV
                  echo ls ${TERRAFORM_CONFIG_FILES}
                  echo "BRANCH_NAME=$(echo ${BRANCH//\//-} | sed 's/_/-/g')"

            - name: Configure AWS credentials
              uses: aws-actions/configure-aws-credentials@v4
              with:
                  aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
                  aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
                  aws-region: eu-west-3

            - name: Terraform Init
              run: |
                  export TF_LOG=TRACE
                  terraform -chdir=${TERRAFORM_CONFIG_FILES} init \
                    -backend-config="bucket=malou-terraform-backend" \
                    -backend-config="key=terraform-state" \
                    -backend-config="region=eu-west-3" \
                    -backend-config="encrypt=true"

            - name: Terraform Workspace
              run: |
                  terraform -chdir=${TERRAFORM_CONFIG_FILES} workspace new ${BRANCH_NAME} || true

            - name: Terraform Apply
              id: apply
              run: |
                  terraform -chdir=${TERRAFORM_CONFIG_FILES} apply -auto-approve -var="branch_name=${BRANCH_NAME}" -var="environment=${{ inputs.environment }}"
                  OUTPUT=$(terraform -chdir=${TERRAFORM_CONFIG_FILES} output cloudfront_url)
                  echo "::set-output name=cloudfront_url::$(echo "$OUTPUT" | grep "cloudfront_url" | awk -F' = ' '{print $2}')"

            - name: Install and build
              run: |
                  pnpm install --filter @malou-io/app-web...
                  pnpm run build-${{ inputs.environment }} --filter=@malou-io/app-web

            - name: Deploy to AWS S3
              uses: jakejarvis/s3-sync-action@master
              with:
                  args: --acl public-read --follow-symlinks --delete
              env:
                  AWS_S3_BUCKET: '${BRANCH_NAME}-${{ inputs.environment }}-bucket'
                  AWS_ACCESS_KEY_ID: ${{ secrets.AWS_ACCESS_KEY_ID }}
                  AWS_SECRET_ACCESS_KEY: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
                  AWS_REGION: eu-west-3
                  SOURCE_DIR: apps/app-malou-web/dist

            - name: Create Comment
              uses: peter-evans/create-or-update-comment@v1
              with:
                  token: ${{ secrets.GITHUB_TOKEN }}
                  issue-number: ${{ inputs.pull_request_number }}
                  body: |
                      The CloudFront URL is: ${{ steps.apply.outputs.cloudfront_url }}
