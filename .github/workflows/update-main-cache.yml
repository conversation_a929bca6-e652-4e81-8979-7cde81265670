# Cache is only shared in PRs or tags context. If we want to have a cache accessible by all branches, we should create and update it on the default branch, aka "main"
# https://medium.com/@everton.spader/how-to-cache-package-dependencies-between-branches-with-github-actions-e6a19f33783a#ede6

name: Update cache

on:
    workflow_dispatch:
    push:
        branches:
            - main
            - release_v**

env:
    PNPM_STORE_PATH: ~/.pnpm-store
    PQINA_NPM_TOKEN: ${{ secrets.PQINA_NPM_TOKEN }}

jobs:
    update-cache:
        name: Build Cache
        runs-on: runs-on=${{ github.run_id }}/runner=8cpu-custom/extras=s3-cache

        steps:
            # Necessary to fetch runsOn S3 cache
            - uses: runs-on/action@v1

            - name: Checkout repository
              uses: actions/checkout@v4

            - name: Install PNPM
              uses: pnpm/action-setup@v4
              with:
                  version: 10.6.5

            - name: Setup Node.js
              uses: actions/setup-node@v4
              with:
                  node-version: 22.15.0

            - name: Ensure PNPM store directory
              run: mkdir -p ${{ env.PNPM_STORE_PATH }}

            - name: Configure PNPM store directory
              run: |
                  export PNPM_STORE_PATH=${{ env.PNPM_STORE_PATH }}
                  pnpm config set store-dir ${{ env.PNPM_STORE_PATH }}

            # Will use and update stored pnpm cache
            - name: PNPM store cache (main)
              if: ${{ github.ref_name == 'main' }}
              uses: actions/cache@v4
              with:
                  path: ${{ env.PNPM_STORE_PATH }}
                  key: ${{ runner.os }}-pnpm-store-${{ hashFiles('**/pnpm-lock.yaml') }}
                  restore-keys: |
                      ${{ runner.os }}-pnpm-store-

            # Only restore cache for other branches
            - name: PNPM store cache
              if: ${{ github.ref_name != 'main' }}
              uses: actions/cache/restore@v4
              with:
                  path: ${{ env.PNPM_STORE_PATH }}
                  key: ${{ runner.os }}-pnpm-store-${{ hashFiles('**/pnpm-lock.yaml') }}
                  restore-keys: |
                      ${{ runner.os }}-pnpm-store-

            # https://github.com/vercel/turborepo/pull/2761/files
            # https://github.com/austinwoon/turbo-repo-cache-with-github-actions/blob/master/.github/workflows/deploy.yml
            - name: Turbo cache
              uses: actions/cache/restore@v4
              with:
                  path: .turbo
                  key: ${{ runner.os }}-turbo-${{ github.ref_name }}
                  restore-keys: |
                      ${{ runner.os }}-turbo-main
                      ${{ runner.os }}-turbo-

            - name: Install dependencies
              run: pnpm install

            - name: Run turbo tasks
              env:
                  NODE_OPTIONS: '--max_old_space_size=4096'
              run: pnpm run format:check lint build --cache-dir=.turbo

            # actions/cache@v4 won't save the cache afterwards if there's a cache hit
            # But we want to save it no matter what, so we have to do it manually, by using restore then save
            - name: Turbo cache save
              uses: actions/cache/save@v4
              with:
                  path: .turbo
                  key: ${{ runner.os }}-turbo-${{ github.ref_name }}
