name: Pull request checks

on:
    workflow_dispatch:

env:
    PQINA_NPM_TOKEN: ${{ secrets.PQINA_NPM_TOKEN }}
    PNPM_STORE_PATH: ~/.pnpm-store

jobs:
    check-format:
        name: Check format
        runs-on: runs-on=${{ github.run_id }}/runner=2cpu-custom/extras=s3-cache

        steps:
            # Necessary to fetch runsOn S3 cache
            - uses: runs-on/action@v1

            - name: Checkout repository
              uses: actions/checkout@v4
              with:
                  fetch-depth: 0

            - name: Setup Node and PNPM
              uses: ./.github/actions/setup-node-and-pnpm

            # https://github.com/vercel/turborepo/pull/2761/files
            # https://github.com/austinwoon/turbo-repo-cache-with-github-actions/tree/master
            # Restore cache only
            - name: Turbo caching
              uses: actions/cache/restore@v4
              with:
                  path: .turbo
                  key: ${{ runner.os }}-turbo-${{ github.event.pull_request.head.ref }}
                  restore-keys: |
                      ${{ runner.os }}-turbo-${{ github.event.pull_request.base.ref }}
                      ${{ runner.os }}-turbo-main
                      ${{ runner.os }}-turbo-

            - name: Install dependencies
              run: pnpm install

            # We run the format check only on the files that have changed in the PR
            # We don't care about the dependencies, we only need to run it in every changed folder
            # No dependsOn in turbo.json so scripts will run in //
            # https://turbo.build/repo/docs/reference/run#advanced-filtering-examples
            - name: Check format
              run: pnpm run format:check --cache-dir=.turbo --filter=\[origin/${{ github.event.pull_request.base.ref }}\]

    check-lint:
        name: Check lint
        runs-on: runs-on=${{ github.run_id }}/runner=2cpu-custom/extras=s3-cache

        steps:
            # Necessary to fetch runsOn S3 cache
            - uses: runs-on/action@v1

            - name: Checkout repository
              uses: actions/checkout@v4
              with:
                  fetch-depth: 0

            - name: Setup Node and PNPM
              uses: ./.github/actions/setup-node-and-pnpm

            # https://github.com/vercel/turborepo/pull/2761/files
            # https://github.com/austinwoon/turbo-repo-cache-with-github-actions/tree/master
            # Restore cache only
            - name: Turbo caching
              uses: actions/cache/restore@v4
              with:
                  path: .turbo
                  key: ${{ runner.os }}-turbo-${{ github.event.pull_request.head.ref }}
                  restore-keys: |
                      ${{ runner.os }}-turbo-${{ github.event.pull_request.base.ref }}
                      ${{ runner.os }}-turbo-main
                      ${{ runner.os }}-turbo-

            - name: Install dependencies
              run: pnpm install

            # We run the lint check only on the files that have changed in the PR
            # We don't care about the dependencies, we only need to run it in every changed folder
            # No dependsOn in turbo.json so scripts will run in //
            # https://turbo.build/repo/docs/reference/run#advanced-filtering-examples
            - name: Check lint
              run: pnpm run lint --cache-dir=.turbo --filter=\[origin/${{ github.event.pull_request.base.ref }}\]

    build:
        name: Check build
        runs-on: runs-on=${{ github.run_id }}/runner=16cpu-custom/extras=s3-cache

        steps:
            # Necessary to fetch runsOn S3 cache
            - uses: runs-on/action@v1

            - name: Checkout repository
              uses: actions/checkout@v4
              with:
                  fetch-depth: 0

            - name: Setup Node and PNPM
              uses: ./.github/actions/setup-node-and-pnpm

            # https://github.com/vercel/turborepo/pull/2761/files
            # https://github.com/austinwoon/turbo-repo-cache-with-github-actions/tree/master
            # Restore cache only
            - name: Turbo caching
              uses: actions/cache/restore@v4
              with:
                  path: .turbo
                  key: ${{ runner.os }}-turbo-${{ github.event.pull_request.head.ref }}
                  restore-keys: |
                      ${{ runner.os }}-turbo-${{ github.event.pull_request.base.ref }}
                      ${{ runner.os }}-turbo-main
                      ${{ runner.os }}-turbo-

            - name: Install dependencies
              run: pnpm install

            # If package/utils changed, then we have to run build in package-utils and all its dependent packages / apps
            # If several dependent packages / apps changed, turborepo is smart enough to only run the tasks once for each package / app
            # https://turbo.build/repo/docs/reference/run#advanced-filtering-examples
            - name: Run build
              env:
                  NODE_OPTIONS: '--max_old_space_size=4096'
              run: pnpm run build --cache-dir=.turbo --filter=...\[origin/${{ github.event.pull_request.base.ref }}\]

            # actions/cache@v4 won't save the cache afterwards if there's a cache hit
            # But we want to save it no matter what, so we have to do it manually, by using restore then save
            - name: Turbo cache save
              uses: actions/cache/save@v4
              with:
                  path: .turbo
                  key: ${{ runner.os }}-turbo-${{ github.event.pull_request.head.ref }}

    unit-test:
        name: Check unit tests
        # We could run this job in parallel with the build job, but by waiting for it, we will take advantage of the cache for the build step
        needs: [build]
        runs-on: runs-on=${{ github.run_id }}/runner=16cpu-custom/extras=s3-cache

        steps:
            # Necessary to fetch runsOn S3 cache
            - uses: runs-on/action@v1

            - name: Checkout repository
              uses: actions/checkout@v4
              with:
                  fetch-depth: 0

            - name: Setup Node and PNPM
              uses: ./.github/actions/setup-node-and-pnpm

            # https://github.com/vercel/turborepo/pull/2761/files
            # https://github.com/austinwoon/turbo-repo-cache-with-github-actions/tree/master
            - name: Turbo caching
              uses: actions/cache/restore@v4
              with:
                  path: .turbo
                  key: ${{ runner.os }}-turbo-${{ github.event.pull_request.head.ref }}
                  restore-keys: |
                      ${{ runner.os }}-turbo-${{ github.event.pull_request.base.ref }}
                      ${{ runner.os }}-turbo-main
                      ${{ runner.os }}-turbo-

            - name: Install dependencies
              run: pnpm install

            - name: Run build
              env:
                  NODE_OPTIONS: '--max_old_space_size=4096'
              run: pnpm run build --cache-dir=.turbo --filter=...\[origin/${{ github.event.pull_request.base.ref }}\]^...

            - name: Set env variables
              run: |
                  touch apps/app-malou-api/.env.jest.tests
                  printf ${{ secrets.TESTS_ENV_FILE }} | base64 --decode > apps/app-malou-api/.env.jest.tests

            # If package/utils changed, then we have to run all unit tests in package-utils and all its dependent packages / apps
            # If several dependent packages / apps changed, turborepo is smart enough to only run the tasks once for each package / app
            # https://turbo.build/repo/docs/reference/run#advanced-filtering-examples
            - name: Run unit tests
              env:
                  NODE_OPTIONS: '--max_old_space_size=4096'
              run: pnpm run test:unit --filter=...\[origin/${{ github.event.pull_request.base.ref }}\] -- --passWithNoTests

    # unit-test:
    # name: Check unit tests
    # # We could run this job in parallel with the build job, but by waiting for it, we will take advantage of the cache for the build step
    # needs: [build]
    # strategy:
    #     matrix:
    #         shardIndex: [1, 2]
    #         shardTotal: [2]
    # runs-on:
    #     - runs-on=${{ github.run_id }}
    #     - runner=4cpu-custom
    #     - extras=s3-cache

    # steps:
    #     # Necessary to fetch runsOn S3 cache
    #     - uses: runs-on/action@v1

    #     - name: Checkout repository
    #       uses: actions/checkout@v4
    #       with:
    #           fetch-depth: 0

    #     - name: Install PNPM
    #       uses: pnpm/action-setup@v4
    #       with:
    #           version: 9.1.3

    #     - name: Configure PNPM store directory
    #       run: |
    #           export PNPM_STORE_PATH=${{ env.PNPM_STORE_PATH }}
    #           pnpm config set store-dir ${{ env.PNPM_STORE_PATH }}

    #     - name: Setup Node.js
    #       uses: actions/setup-node@v4
    #       with:
    #           node-version: 22.15.0

    #     - name: PNPM store caching
    #       id: pnpm-cache
    #       uses: actions/cache/restore@v4
    #       with:
    #           path: ${{ env.PNPM_STORE_PATH }}
    #           key: ${{ runner.os }}-pnpm-store-${{ hashFiles('**/pnpm-lock.yaml') }}
    #           restore-keys: |
    #               ${{ runner.os }}-pnpm-store-

    #     # https://github.com/vercel/turborepo/pull/2761/files
    #     # https://github.com/austinwoon/turbo-repo-cache-with-github-actions/tree/master
    #     - name: Turbo caching
    #       uses: actions/cache/restore@v4
    #       with:
    #           path: .turbo
    #           key: ${{ runner.os }}-turbo-${{ github.event.pull_request.head.ref }}
    #           restore-keys: |
    #               ${{ runner.os }}-turbo-main
    #               ${{ runner.os }}-turbo-

    #     - name: Install dependencies
    #       run: pnpm install

    #     - name: Run build
    #       env:
    #           NODE_OPTIONS: '--max_old_space_size=4096'
    #       run: pnpm run build --cache-dir=.turbo --filter=...\[origin/${{ github.event.pull_request.base.ref }}\]^...

    #     - name: Set env variables
    #       run: |
    #           touch apps/app-malou-api/.env.jest.tests
    #           printf ${{ secrets.TESTS_ENV_FILE }} | base64 --decode > apps/app-malou-api/.env.jest.tests

    #     # If package/utils changed, then we have to run all unit tests in package-utils and all its dependent packages / apps
    #     # If several dependent packages / apps changed, turborepo is smart enough to only run the tasks once for each package / app
    #     # https://turbo.build/repo/docs/reference/run#advanced-filtering-examples
    #     - name: Run unit tests
    #       env:
    #           NODE_OPTIONS: '--max_old_space_size=4096'
    #       run: pnpm run test:unit --filter=...\[origin/${{ github.event.pull_request.base.ref }}\] -- --shard=${{ matrix.shardIndex }}/${{ matrix.shardTotal }} --passWithNoTests
