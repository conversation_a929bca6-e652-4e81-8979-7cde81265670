# https://victoronsoftware.com/posts/github-reusable-workflows-and-steps/#reusable-steps-composite-action

name: Build Docker image and push to ECR
description: Create a base docker image for both API and worker and push it to ECR

inputs:
    # Env variables are available by default but
    # Secrets have to be passed explicitly
    sentry-auth-token:
        description: 'Sentry token'
        required: true
    aws-access-key-id:
        description: 'AWS access key'
        required: true
    aws-secret-access-key:
        description: 'AWS secret key'
        required: true
    aws-region:
        description: 'AWS region'
        required: true
    ecr-repository:
        description: 'ECR repository name'
        required: true
    image-tag:
        description: 'Docker image tag'
        required: true
    environment:
        description: 'Environment'
        required: true
outputs:
    # Map the action output(s) to step output(s)
    docker-image-url:
        description: 'API image URL on ECR'
        value: ${{ steps.store-ecr-variables.outputs.image-ref }}

runs:
    using: 'composite'
    steps:
        - name: Checkout
          uses: actions/checkout@v4

        - name: Configure AWS credentials
          uses: aws-actions/configure-aws-credentials@v4
          with:
              aws-access-key-id: ${{ inputs.aws-access-key-id }}
              aws-secret-access-key: ${{ inputs.aws-secret-access-key }}
              aws-region: ${{ inputs.aws-region }}

        - name: Login to Amazon ECR
          id: login-ecr
          uses: aws-actions/amazon-ecr-login@v2

        - name: Store ECR variables
          id: store-ecr-variables
          env:
              IMAGE_TAG: ${{ steps.login-ecr.outputs.registry }}/malou/${{ inputs.ecr-repository }}:${{ inputs.image-tag }}
          # Shell must explicitly specify the shell for each step. https://github.com/orgs/community/discussions/18597
          shell: bash
          run: |
              echo "image-ref=$IMAGE_TAG" >> $GITHUB_OUTPUT
              echo "cache-ref=$IMAGE_TAG-cache" >> $GITHUB_OUTPUT

        - name: Get Docker BuildKit
          uses: docker/setup-buildx-action@v3

        - name: Build, tag, and push image to Amazon ECR
          uses: docker/build-push-action@v5
          with:
              context: .
              push: true
              file: ./apps/app-malou-api/Dockerfile
              build-args: |
                  ENV=${{ inputs.environment }}
                  GIT_COMMIT_SHA=${{ github.sha }}
                  SENTRY_AUTH_TOKEN=${{ inputs.sentry-auth-token }}
                  BRANCH_NAME=${{ github.ref_name }}
              tags: ${{ steps.store-ecr-variables.outputs.image-ref }}
              # Use local cache with runsOn: self hosted on S3
              #   cache-from: type=gha
              #   cache-to: type=gha,mode=max
              # Use ECR cache
              #   cache-from: type=registry,ref=${{ steps.store-ecr-variables.outputs.cache-ref }}
              #   cache-to: type=registry,ref=${{ steps.store-ecr-variables.outputs.cache-ref }},mode=max,image-manifest=true,oci-mediatypes=true
